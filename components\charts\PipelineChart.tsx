'use client'

import React from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { usePipelineData } from '@/hooks/useChartData'
import { formatters } from '@/components/charts/chart-config'
import { ChartProps } from '@/components/charts/types'

interface PipelineChartProps extends Omit<ChartProps, 'data'> {
  showValue?: boolean
  showCount?: boolean
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{label}</p>
        <p className="text-sm text-muted-foreground">
          Count: <span className="font-medium text-foreground">{data.count}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Value: <span className="font-medium text-foreground">{formatters.currency(data.value)}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Avg Probability: <span className="font-medium text-foreground">{formatters.percentage(data.probability)}</span>
        </p>
      </div>
    )
  }
  return null
}

export function PipelineChart({ 
  config,
  loading: externalLoading,
  error,
  className = "",
  title = "Sales Pipeline",
  subtitle = "Deals by stage",
  showValue = true,
  showCount = false
}: PipelineChartProps) {
  const { data, loading: dataLoading } = usePipelineData()
  const isLoading = externalLoading || dataLoading

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>Error loading pipeline data: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>No pipeline data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          <span className="text-sm font-normal text-muted-foreground">
            {data.reduce((sum, item) => sum + item.count, 0)} total deals
          </span>
        </CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={config?.height || 300}>
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="stage" 
              tick={{ fontSize: 12 }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickFormatter={showValue ? formatters.compactNumber : undefined}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey={showValue ? "value" : "count"} 
              radius={[4, 4, 0, 0]}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
        
        {/* Summary Stats */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">Total Value</p>
            <p className="font-semibold">
              {formatters.currency(data.reduce((sum, item) => sum + item.value, 0))}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Avg Deal Size</p>
            <p className="font-semibold">
              {formatters.currency(
                data.reduce((sum, item) => sum + item.value, 0) / 
                data.reduce((sum, item) => sum + item.count, 0)
              )}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Weighted Value</p>
            <p className="font-semibold">
              {formatters.currency(
                data.reduce((sum, item) => sum + (item.value * item.probability / 100), 0)
              )}
            </p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Avg Probability</p>
            <p className="font-semibold">
              {formatters.percentage(
                data.reduce((sum, item) => sum + item.probability, 0) / data.length
              )}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
