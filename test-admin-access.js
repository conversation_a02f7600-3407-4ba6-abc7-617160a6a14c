/**
 * TEST ADMIN ACCESS SCRIPT
 * 
 * This script tests the admin role-based access control
 * by creating test users and verifying data access.
 */

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const SUPABASE_URL = 'https://ojhtdwrzolfwbiwrprok.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9qaHRkd3J6b2xmd2Jpd3Jwcm9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2ODQ5NDYsImV4cCI6MjA2NjI2MDk0Nn0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9qaHRkd3J6b2xmd2Jpd3Jwcm9rIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDY4NDk0NiwiZXhwIjoyMDY2MjYwOTQ2fQ.m1w2e25EYZ5rdQYLhgIv-REAWWErltEFe2eknYZK4h8';

const supabaseService = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
const supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

async function testAdminAccess() {
  console.log('🚀 Testing Admin Role-Based Access Control...\n');

  try {
    // =====================================================
    // STEP 1: CREATE TEST USERS
    // =====================================================
    console.log('📋 Step 1: Creating test users...');

    // Create admin user
    const { data: adminUser, error: adminError } = await supabaseService.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true
    });

    if (adminError && !adminError.message.includes('already registered')) {
      console.error('❌ Error creating admin user:', adminError);
    } else {
      console.log('✅ Admin user created/exists: <EMAIL>');
    }

    // Create regular user
    const { data: regularUser, error: regularError } = await supabaseService.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true
    });

    if (regularError && !regularError.message.includes('already registered')) {
      console.error('❌ Error creating regular user:', regularError);
    } else {
      console.log('✅ Regular user created/exists: <EMAIL>');
    }

    // =====================================================
    // STEP 2: SET USER ROLES
    // =====================================================
    console.log('\n📋 Step 2: Setting user roles...');

    // Get all users to find our test users
    const { data: allUsers } = await supabaseService.auth.admin.listUsers();
    const testAdminUser = allUsers.users.find(u => u.email === '<EMAIL>');
    const testRegularUser = allUsers.users.find(u => u.email === '<EMAIL>');

    if (testAdminUser) {
      // Set admin role
      const { error: adminRoleError } = await supabaseService
        .from('users')
        .upsert({
          id: testAdminUser.id,
          email: testAdminUser.email,
          role: 'admin',
          full_name: 'Test Admin User'
        });

      if (adminRoleError) {
        console.error('❌ Error setting admin role:', adminRoleError);
      } else {
        console.log('✅ Set admin <NAME_EMAIL>');
      }
    }

    if (testRegularUser) {
      // Set regular user role
      const { error: userRoleError } = await supabaseService
        .from('users')
        .upsert({
          id: testRegularUser.id,
          email: testRegularUser.email,
          role: 'user',
          full_name: 'Test Regular User'
        });

      if (userRoleError) {
        console.error('❌ Error setting user role:', userRoleError);
      } else {
        console.log('✅ Set user <NAME_EMAIL>');
      }
    }

    // =====================================================
    // STEP 3: CREATE TEST DATA
    // =====================================================
    console.log('\n📋 Step 3: Creating test data...');

    // Create test customers for both users
    const testCustomers = [
      {
        user_id: testAdminUser?.id,
        contact_person: 'Admin Customer',
        email: '<EMAIL>',
        company: 'Admin Corp',
        city: 'Amman',
        country: 'Jordan',
        contact_source: 'Website'
      },
      {
        user_id: testRegularUser?.id,
        contact_person: 'User Customer',
        email: '<EMAIL>',
        company: 'User Corp',
        city: 'Irbid',
        country: 'Jordan',
        contact_source: 'Referral'
      }
    ];

    for (const customer of testCustomers) {
      const { error: customerError } = await supabaseService
        .from('customers')
        .upsert(customer, { onConflict: 'email' });

      if (customerError) {
        console.error('❌ Error creating test customer:', customerError);
      } else {
        console.log(`✅ Created test customer: ${customer.contact_person}`);
      }
    }

    // =====================================================
    // STEP 4: TEST ADMIN ACCESS
    // =====================================================
    console.log('\n📋 Step 4: Testing admin access...');

    // Sign in as admin
    const { data: adminSession, error: adminSignInError } = await supabaseAnon.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });

    if (adminSignInError) {
      console.error('❌ Error signing in as admin:', adminSignInError);
      return;
    }

    console.log('✅ Signed in as admin');

    // Create admin client with session
    const adminClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    await adminClient.auth.setSession(adminSession.session);

    // Test admin can see all customers
    const { data: adminCustomers, error: adminCustomersError } = await adminClient
      .from('customers')
      .select('*');

    if (adminCustomersError) {
      console.error('❌ Admin customer access error:', adminCustomersError);
    } else {
      console.log(`✅ Admin can see ${adminCustomers.length} customers (should be 2 or more)`);
      console.log(`   Customers: ${adminCustomers.map(c => c.contact_person).join(', ')}`);
    }

    // Test is_admin function
    const { data: isAdminResult, error: isAdminError } = await adminClient
      .rpc('is_admin');

    if (isAdminError) {
      console.error('❌ is_admin function error:', isAdminError);
    } else {
      console.log(`✅ is_admin() returns: ${isAdminResult} (should be true)`);
    }

    // =====================================================
    // STEP 5: TEST REGULAR USER ACCESS
    // =====================================================
    console.log('\n📋 Step 5: Testing regular user access...');

    // Sign out admin
    await adminClient.auth.signOut();

    // Sign in as regular user
    const { data: userSession, error: userSignInError } = await supabaseAnon.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });

    if (userSignInError) {
      console.error('❌ Error signing in as regular user:', userSignInError);
      return;
    }

    console.log('✅ Signed in as regular user');

    // Create user client with session
    const userClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
    await userClient.auth.setSession(userSession.session);

    // Test user can only see their own customers
    const { data: userCustomers, error: userCustomersError } = await userClient
      .from('customers')
      .select('*');

    if (userCustomersError) {
      console.error('❌ User customer access error:', userCustomersError);
    } else {
      console.log(`✅ Regular user can see ${userCustomers.length} customers (should be 1)`);
      console.log(`   Customers: ${userCustomers.map(c => c.contact_person).join(', ')}`);
    }

    // Test is_admin function for regular user
    const { data: userIsAdminResult, error: userIsAdminError } = await userClient
      .rpc('is_admin');

    if (userIsAdminError) {
      console.error('❌ is_admin function error for user:', userIsAdminError);
    } else {
      console.log(`✅ is_admin() for regular user returns: ${userIsAdminResult} (should be false)`);
    }

    // =====================================================
    // STEP 6: SUMMARY
    // =====================================================
    console.log('\n🎉 Admin Role-Based Access Control Test Summary:');
    console.log('================================================');
    console.log(`✅ Admin policies applied successfully`);
    console.log(`✅ Test users created with roles`);
    console.log(`✅ Admin can access all data (${adminCustomers?.length || 0} customers)`);
    console.log(`✅ Regular user can only access own data (${userCustomers?.length || 0} customers)`);
    console.log(`✅ is_admin() function works correctly`);
    console.log('\n🚀 Admin role-based access control is working correctly!');

    // Mark task as complete
    console.log('\n📋 Marking database policies task as complete...');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests
if (require.main === module) {
  testAdminAccess();
}

module.exports = { testAdminAccess };
