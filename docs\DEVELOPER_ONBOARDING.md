# Nawras CRM - Developer Onboarding Guide

## Welcome to the Nawras CRM Development Team!

This guide will help you get up and running with the Nawras CRM codebase quickly and efficiently. Follow these steps to set up your development environment and understand the project structure.

## Prerequisites

### Required Software
- **Node.js**: Version 18.0 or higher
- **npm**: Version 9.0 or higher (comes with Node.js)
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions
- **PostgreSQL**: For local database (optional, can use Supabase)

### Recommended VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "supabase.supabase-vscode",
    "ms-vscode.vscode-json"
  ]
}
```

## Project Setup

### 1. Clone the Repository
```bash
git clone https://github.com/mrTamtamnew/crmnew.git
cd crmnew
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://ojhtdwrzolfwbiwrprok.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Optional: Database Direct Connection (for migrations)
DATABASE_URL=postgresql://postgres:[password]@db.ojhtdwrzolfwbiwrprok.supabase.co:5432/postgres
```

**Note**: Contact the team lead for the actual Supabase keys.

### 4. Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:3000`.

### 5. Verify Setup
1. Navigate to `http://localhost:3000`
2. You should see the login page
3. Use test credentials: `<EMAIL>` / `111333Tt`
4. Verify you can access the dashboard

## Project Structure Overview

```
crmnew-main/
├── app/                    # Next.js App Router (pages and API routes)
├── components/             # React components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries and configurations
├── docs/                   # Project documentation
├── __tests__/              # Test files
├── scripts/                # Database and utility scripts
├── supabase/              # Supabase configuration and migrations
└── public/                # Static assets
```

### Key Directories Explained

#### `/app` - Application Pages and API
- **`/api`**: Server-side API routes
- **`/dashboard`**: Main application pages
- **`/login`**: Authentication pages
- **`layout.tsx`**: Root layout component
- **`globals.css`**: Global styles

#### `/components` - React Components
- **`/ui`**: Base UI components (buttons, inputs, etc.)
- **`/forms`**: Form system components
- **`/customers`**: Customer-specific components
- **`/deals`**: Deal management components
- **`/tables`**: Data table components

#### `/lib` - Utilities and Configuration
- **`supabase.ts`**: Database client configuration
- **`utils.ts`**: General utility functions
- **`database.types.ts`**: TypeScript type definitions

## Development Workflow

### 1. Branch Strategy
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Work on your feature
git add .
git commit -m "feat: add new customer form validation"

# Push and create PR
git push origin feature/your-feature-name
```

### 2. Commit Message Convention
Follow conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

### 3. Code Style Guidelines
```typescript
// Use TypeScript interfaces for props
interface ComponentProps {
  title: string
  onSubmit: (data: FormData) => void
  isLoading?: boolean
}

// Use functional components with proper typing
export function Component({ title, onSubmit, isLoading = false }: ComponentProps) {
  // Component implementation
}

// Use descriptive variable names
const customerFormData = useForm<CustomerFormData>()
const { data: customers, loading: isLoadingCustomers } = useQuery()
```

### 4. File Naming Conventions
- **Components**: PascalCase (`CustomerForm.tsx`)
- **Hooks**: camelCase with "use" prefix (`useCustomerData.ts`)
- **Utilities**: camelCase (`formatCurrency.ts`)
- **Pages**: lowercase with hyphens (`customer-details.tsx`)

## Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test file
npm test customer-form

# Run tests in watch mode
npm run test:watch

# Run E2E tests
npm run test:e2e
```

### Writing Tests
```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react'
import { CustomerForm } from '@/components/customers/customer-form'

describe('CustomerForm', () => {
  it('renders form fields correctly', () => {
    render(<CustomerForm onSubmit={jest.fn()} onCancel={jest.fn()} />)
    
    expect(screen.getByLabelText(/contact person/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
  })
})
```

### E2E Testing with Playwright
```typescript
// E2E test example
import { test, expect } from '@playwright/test'

test('customer creation flow', async ({ page }) => {
  await page.goto('/dashboard/customers')
  await page.click('[data-testid="add-customer-button"]')
  
  await page.fill('[data-testid="contact-person"]', 'John Doe')
  await page.fill('[data-testid="email"]', '<EMAIL>')
  
  await page.click('[data-testid="submit-button"]')
  
  await expect(page.locator('.success-message')).toBeVisible()
})
```

## Database Development

### Working with Supabase
```typescript
// Using the Supabase client
import { createClient } from '@/lib/supabase'

const supabase = createClient()

// Fetch data
const { data: customers, error } = await supabase
  .from('customers')
  .select('*')
  .eq('user_id', userId)

// Insert data
const { data, error } = await supabase
  .from('customers')
  .insert([customerData])
```

### Database Migrations
```bash
# Create new migration
npx supabase migration new add_customer_table

# Apply migrations
npx supabase db push

# Reset database (development only)
npx supabase db reset
```

## Common Development Tasks

### 1. Adding a New Component
```bash
# Create component file
touch components/ui/new-component.tsx

# Add to index file
echo "export { NewComponent } from './new-component'" >> components/ui/index.ts
```

### 2. Adding a New API Route
```typescript
// app/api/new-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Your API logic here
    
    return NextResponse.json({ data: result })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
```

### 3. Adding a New Page
```typescript
// app/dashboard/new-page/page.tsx
export default function NewPage() {
  return (
    <div>
      <h1>New Page</h1>
      {/* Page content */}
    </div>
  )
}
```

## Debugging

### Common Issues and Solutions

#### 1. Supabase Connection Issues
```typescript
// Check environment variables
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
console.log('Supabase Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 10) + '...')

// Test connection
const { data, error } = await supabase.from('customers').select('count')
console.log('Connection test:', { data, error })
```

#### 2. Authentication Issues
```typescript
// Check user session
const { data: { session }, error } = await supabase.auth.getSession()
console.log('Current session:', session)
```

#### 3. TypeScript Errors
```bash
# Check TypeScript errors
npx tsc --noEmit

# Generate types from Supabase
npx supabase gen types typescript --project-id ojhtdwrzolfwbiwrprok > lib/database.types.ts
```

### Development Tools

#### Browser DevTools
- Use React DevTools for component debugging
- Check Network tab for API calls
- Use Console for logging and debugging

#### VS Code Debugging
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Next.js: debug server-side",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

## Performance Best Practices

### 1. Component Optimization
```typescript
// Use React.memo for expensive components
export const ExpensiveComponent = React.memo(({ data }: Props) => {
  // Component implementation
})

// Use useCallback for event handlers
const handleSubmit = useCallback((data: FormData) => {
  // Handler implementation
}, [dependency])
```

### 2. Data Fetching
```typescript
// Use React Query for server state
const { data, isLoading, error } = useQuery({
  queryKey: ['customers', filters],
  queryFn: () => fetchCustomers(filters),
  staleTime: 5 * 60 * 1000, // 5 minutes
})
```

### 3. Bundle Optimization
```typescript
// Use dynamic imports for code splitting
const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <LoadingSpinner />
})
```

## Deployment

### Development Deployment
```bash
# Build the application
npm run build

# Start production server locally
npm start
```

### Production Deployment
The application automatically deploys to Vercel when changes are pushed to the main branch.

**Live URL**: https://sales.nawrasinchina.com/

## Getting Help

### Resources
1. **Documentation**: Check the `/docs` folder for detailed documentation
2. **Code Examples**: Look at existing components for patterns
3. **Team Chat**: Use the team communication channel
4. **Code Reviews**: Ask for reviews on complex changes

### Team Contacts
- **Tech Lead**: [Contact Information]
- **Senior Developer**: [Contact Information]
- **DevOps**: [Contact Information]

### Useful Links
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Playwright Documentation](https://playwright.dev/docs/intro)

## Next Steps

1. **Complete Setup**: Ensure your development environment is working
2. **Explore Codebase**: Browse through the components and understand the structure
3. **Run Tests**: Execute the test suite to ensure everything works
4. **Pick First Task**: Start with a small bug fix or feature
5. **Ask Questions**: Don't hesitate to ask the team for help

Welcome to the team! We're excited to have you contribute to the Nawras CRM project.
