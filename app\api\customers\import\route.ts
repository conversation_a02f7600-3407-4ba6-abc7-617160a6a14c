import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { CustomerFormData, mapFormToDatabase } from '@/app/types/customer'
import { CustomerImportMapping } from '@/components/customers/customer-import-export'

// Validation functions
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))
}

const validateRequired = (value: any): boolean => {
  return value !== null && value !== undefined && value.toString().trim() !== ''
}

// Data transformation functions
const transformValue = (value: any, type: string): any => {
  if (value === null || value === undefined || value === '') {
    return null
  }

  switch (type) {
    case 'number':
      const num = parseFloat(value.toString().replace(/[,$]/g, ''))
      return isNaN(num) ? null : num
      
    case 'boolean':
      const str = value.toString().toLowerCase()
      return ['true', '1', 'yes', 'y', 'on'].includes(str)
      
    case 'date':
      const date = new Date(value)
      return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0]
      
    case 'email':
      return value.toString().toLowerCase().trim()
      
    case 'text':
    default:
      return value.toString().trim()
  }
}

// Validate customer data
const validateCustomerData = (customer: Partial<CustomerFormData>, rowIndex: number) => {
  const errors: Array<{ row: number; field: string; message: string }> = []
  const warnings: Array<{ row: number; field: string; message: string }> = []

  // Required field validation
  const requiredFields = ['contact_person', 'email', 'company', 'city', 'country']
  
  requiredFields.forEach(field => {
    if (!validateRequired(customer[field as keyof CustomerFormData])) {
      errors.push({
        row: rowIndex,
        field,
        message: `Required field '${field}' is missing or empty`
      })
    }
  })

  // Email validation
  if (customer.email && !validateEmail(customer.email)) {
    errors.push({
      row: rowIndex,
      field: 'email',
      message: `Invalid email format: ${customer.email}`
    })
  }

  // Phone validation
  if (customer.phone && !validatePhone(customer.phone)) {
    warnings.push({
      row: rowIndex,
      field: 'phone',
      message: `Phone number format may be invalid: ${customer.phone}`
    })
  }

  if (customer.mobile && !validatePhone(customer.mobile)) {
    warnings.push({
      row: rowIndex,
      field: 'mobile',
      message: `Mobile number format may be invalid: ${customer.mobile}`
    })
  }

  // Business type validation
  const validBusinessTypes = ['Retailer', 'Wholesaler', 'Distributor', 'Manufacturer', 'E-commerce', 'Other']
  if (customer.business_type && !validBusinessTypes.includes(customer.business_type)) {
    warnings.push({
      row: rowIndex,
      field: 'business_type',
      message: `Unknown business type: ${customer.business_type}. Will be set to 'Other'`
    })
    customer.business_type = 'Other'
  }

  // Company size validation
  const validCompanySizes = ['Small', 'Medium', 'Large', 'Enterprise']
  if (customer.company_size && !validCompanySizes.includes(customer.company_size)) {
    warnings.push({
      row: rowIndex,
      field: 'company_size',
      message: `Unknown company size: ${customer.company_size}. Will be set to 'Small'`
    })
    customer.company_size = 'Small'
  }

  // Customer tier validation
  const validTiers = ['Bronze', 'Silver', 'Gold', 'Platinum', 'VIP']
  if (customer.customer_tier && !validTiers.includes(customer.customer_tier)) {
    warnings.push({
      row: rowIndex,
      field: 'customer_tier',
      message: `Unknown customer tier: ${customer.customer_tier}. Will be set to 'Bronze'`
    })
    customer.customer_tier = 'Bronze'
  }

  // Payment terms validation
  const validPaymentTerms = ['30 Days', '60 Days', '90 Days', 'COD', 'Prepaid', 'Net 15', 'Net 45']
  if (customer.payment_terms && !validPaymentTerms.includes(customer.payment_terms)) {
    warnings.push({
      row: rowIndex,
      field: 'payment_terms',
      message: `Unknown payment terms: ${customer.payment_terms}. Will be set to '30 Days'`
    })
    customer.payment_terms = '30 Days'
  }

  // Shipping method validation
  const validShippingMethods = ['Air', 'Sea', 'Express', 'Ground', 'Courier']
  if (customer.preferred_shipping_method && !validShippingMethods.includes(customer.preferred_shipping_method)) {
    warnings.push({
      row: rowIndex,
      field: 'preferred_shipping_method',
      message: `Unknown shipping method: ${customer.preferred_shipping_method}. Will be set to 'Ground'`
    })
    customer.preferred_shipping_method = 'Ground'
  }

  // Incoterms validation
  const validIncoterms = ['FOB', 'CIF', 'EXW', 'DDP', 'DDU', 'FCA', 'CPT', 'CIP']
  if (customer.preferred_incoterms && !validIncoterms.includes(customer.preferred_incoterms)) {
    warnings.push({
      row: rowIndex,
      field: 'preferred_incoterms',
      message: `Unknown incoterms: ${customer.preferred_incoterms}. Will be set to 'FOB'`
    })
    customer.preferred_incoterms = 'FOB'
  }

  return { errors, warnings }
}

export async function POST(request: NextRequest) {
  try {
    // Use singleton Supabase client to prevent multiple GoTrueClient instances
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { customers, mapping, options }: {
      customers: any[]
      mapping: CustomerImportMapping
      options?: {
        skipDuplicates?: boolean
        updateExisting?: boolean
        validateOnly?: boolean
      }
    } = body

    if (!customers || !Array.isArray(customers) || customers.length === 0) {
      return NextResponse.json({ error: 'No customer data provided' }, { status: 400 })
    }

    if (!mapping || Object.keys(mapping).length === 0) {
      return NextResponse.json({ error: 'No field mapping provided' }, { status: 400 })
    }

    const results = {
      total: customers.length,
      processed: 0,
      successful: 0,
      failed: 0,
      skipped: 0,
      errors: [] as Array<{ row: number; field: string; message: string }>,
      warnings: [] as Array<{ row: number; field: string; message: string }>,
      duplicates: [] as Array<{ row: number; email: string; existingId: string }>
    }

    // Transform and validate data
    const transformedCustomers: CustomerFormData[] = []
    
    for (let i = 0; i < customers.length; i++) {
      const rowData = customers[i]
      const rowIndex = i + 2 // +2 because arrays are 0-indexed and we skip header row
      
      try {
        // Transform data based on mapping
        const customer: Partial<CustomerFormData> = {
          source: 'Other',
          status: 'Active'
        }

        Object.entries(mapping).forEach(([sourceColumn, config]) => {
          const rawValue = rowData[sourceColumn]
          if (rawValue !== undefined && rawValue !== null && rawValue !== '') {
            const transformedValue = transformValue(rawValue, config.type)
            if (transformedValue !== null) {
              customer[config.targetField] = transformedValue
            }
          }
        })

        // Set defaults for required fields if missing
        if (!customer.customer_tier) customer.customer_tier = 'Bronze'
        if (!customer.currency_preference) customer.currency_preference = 'USD'

        // Validate customer data
        const validation = validateCustomerData(customer, rowIndex)
        results.errors.push(...validation.errors)
        results.warnings.push(...validation.warnings)

        // Skip if validation errors exist
        if (validation.errors.length > 0) {
          results.failed++
          continue
        }

        transformedCustomers.push(customer as CustomerFormData)
        
      } catch (error) {
        results.errors.push({
          row: rowIndex,
          field: 'general',
          message: `Failed to process row: ${(error as Error).message}`
        })
        results.failed++
      }
    }

    // If validation only, return results without importing
    if (options?.validateOnly) {
      return NextResponse.json({
        ...results,
        processed: customers.length,
        message: 'Validation completed'
      })
    }

    // Check for duplicates
    if (transformedCustomers.length > 0) {
      const emails = transformedCustomers.map(c => c.email).filter(Boolean)
      
      if (emails.length > 0) {
        const { data: existingCustomers } = await supabase
          .from('customers')
          .select('id, email')
          .eq('user_id', session.user.id)
          .in('email', emails)

        if (existingCustomers && existingCustomers.length > 0) {
          const existingEmails = new Set(existingCustomers.map((c: {id: string, email: string}) => c.email))
          
          transformedCustomers.forEach((customer, index) => {
            if (customer.email && existingEmails.has(customer.email)) {
              const existing = existingCustomers.find((c: {id: string, email: string}) => c.email === customer.email)
              results.duplicates.push({
                row: index + 2,
                email: customer.email,
                existingId: existing?.id || ''
              })
            }
          })
        }
      }
    }

    // Filter out duplicates if skipDuplicates is true
    let customersToImport = transformedCustomers
    if (options?.skipDuplicates && results.duplicates.length > 0) {
      const duplicateEmails = new Set(results.duplicates.map(d => d.email))
      customersToImport = transformedCustomers.filter(c => !duplicateEmails.has(c.email))
      results.skipped = results.duplicates.length
    }

    // Import customers
    const importedCustomers = []
    
    for (const customer of customersToImport) {
      try {
        const dbCustomer = mapFormToDatabase(customer, session.user.id)
        
        const { data, error } = await supabase
          .from('customers')
          .insert(dbCustomer)
          .select()
          .single()

        if (error) {
          console.error('Database insert error:', error)
          results.errors.push({
            row: transformedCustomers.indexOf(customer) + 2,
            field: 'database',
            message: `Database error: ${error.message}`
          })
          results.failed++
        } else {
          importedCustomers.push(data)
          results.successful++
        }
        
      } catch (error) {
        console.error('Import error for customer:', customer, error)
        results.errors.push({
          row: transformedCustomers.indexOf(customer) + 2,
          field: 'import',
          message: `Import failed: ${(error as Error).message}`
        })
        results.failed++
      }
    }

    results.processed = customers.length

    return NextResponse.json({
      ...results,
      importedCustomers: importedCustomers.map(c => c.id),
      message: `Import completed. ${results.successful} customers imported successfully.`
    })

  } catch (error) {
    console.error('Import API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    )
  }
}

// GET endpoint for import validation
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'validate-mapping') {
      // Return available fields for mapping
      const availableFields = [
        { id: 'contact_person', label: 'Contact Person', required: true, type: 'text' },
        { id: 'title_position', label: 'Title/Position', required: false, type: 'text' },
        { id: 'email', label: 'Email', required: true, type: 'email' },
        { id: 'phone', label: 'Phone', required: false, type: 'text' },
        { id: 'mobile', label: 'Mobile', required: false, type: 'text' },
        { id: 'company', label: 'Company', required: true, type: 'text' },
        { id: 'website', label: 'Website', required: false, type: 'text' },
        { id: 'city', label: 'City', required: true, type: 'text' },
        { id: 'country', label: 'Country', required: true, type: 'text' },
        { id: 'business_type', label: 'Business Type', required: false, type: 'select' },
        { id: 'industry', label: 'Industry', required: false, type: 'text' },
        { id: 'annual_volume', label: 'Annual Volume', required: false, type: 'number' },
        { id: 'company_size', label: 'Company Size', required: false, type: 'select' },
        { id: 'tax_id', label: 'Tax ID', required: false, type: 'text' },
        { id: 'credit_limit', label: 'Credit Limit', required: false, type: 'number' },
        { id: 'payment_terms', label: 'Payment Terms', required: false, type: 'select' },
        { id: 'currency_preference', label: 'Currency', required: false, type: 'text' },
        { id: 'preferred_shipping_method', label: 'Shipping Method', required: false, type: 'select' },
        { id: 'preferred_incoterms', label: 'Incoterms', required: false, type: 'select' },
        { id: 'shipping_instructions', label: 'Shipping Instructions', required: false, type: 'text' },
        { id: 'account_manager', label: 'Account Manager', required: false, type: 'text' },
        { id: 'customer_since', label: 'Customer Since', required: false, type: 'date' },
        { id: 'customer_tier', label: 'Customer Tier', required: false, type: 'select' },
        { id: 'tags', label: 'Tags', required: false, type: 'text' },
        { id: 'required_certificates', label: 'Required Certificates', required: false, type: 'text' },
        { id: 'compliance_requirements', label: 'Compliance Requirements', required: false, type: 'text' },
        { id: 'source', label: 'Source', required: false, type: 'select' },
        { id: 'status', label: 'Status', required: false, type: 'select' },
        { id: 'notes', label: 'Notes', required: false, type: 'text' }
      ]

      return NextResponse.json({ fields: availableFields })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Import validation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
