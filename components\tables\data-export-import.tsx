"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { 
  Download, Upload, FileText, FileSpreadsheet, Check, 
  AlertCircle, Loader2, X, Settings, Eye, EyeOff
} from "lucide-react"
import { cn } from "@/lib/utils"

// Export Types
export interface ExportColumn {
  id: string
  label: string
  accessor: string
  format?: "text" | "number" | "currency" | "date" | "boolean"
  transform?: (value: any) => string
}

export interface ExportOptions {
  format: "csv" | "excel" | "json"
  columns: string[]
  includeHeaders: boolean
  dateFormat: string
  encoding: string
  delimiter?: string
}

export interface ExportProgress {
  total: number
  processed: number
  percentage: number
  status: "idle" | "processing" | "complete" | "error"
  message?: string
}

// Import Types
export interface ImportColumn {
  sourceColumn: string
  targetField: string
  required: boolean
  type: "text" | "number" | "date" | "boolean" | "email"
  validation?: (value: any) => string | null
}

export interface ImportPreview {
  headers: string[]
  rows: any[][]
  totalRows: number
  errors: Array<{
    row: number
    column: string
    message: string
  }>
}

export interface ImportProgress {
  total: number
  processed: number
  successful: number
  failed: number
  percentage: number
  status: "idle" | "processing" | "complete" | "error"
  errors: Array<{
    row: number
    message: string
  }>
}

// Export Dialog Component
export function DataExportDialog({
  data,
  columns,
  onExport,
  trigger,
  defaultFormat = "csv"
}: {
  data: any[]
  columns: ExportColumn[]
  onExport: (data: any[], options: ExportOptions) => Promise<void>
  trigger?: React.ReactNode
  defaultFormat?: "csv" | "excel" | "json"
}) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [selectedColumns, setSelectedColumns] = React.useState<string[]>(
    columns.map(col => col.id)
  )
  const [options, setOptions] = React.useState<ExportOptions>({
    format: defaultFormat,
    columns: columns.map(col => col.id),
    includeHeaders: true,
    dateFormat: "YYYY-MM-DD",
    encoding: "UTF-8",
    delimiter: ","
  })
  const [progress, setProgress] = React.useState<ExportProgress>({
    total: 0,
    processed: 0,
    percentage: 0,
    status: "idle"
  })

  const handleColumnToggle = (columnId: string) => {
    const newSelected = selectedColumns.includes(columnId)
      ? selectedColumns.filter(id => id !== columnId)
      : [...selectedColumns, columnId]
    
    setSelectedColumns(newSelected)
    setOptions(prev => ({ ...prev, columns: newSelected }))
  }

  const handleSelectAll = () => {
    const allSelected = selectedColumns.length === columns.length
    const newSelected = allSelected ? [] : columns.map(col => col.id)
    setSelectedColumns(newSelected)
    setOptions(prev => ({ ...prev, columns: newSelected }))
  }

  const handleExport = async () => {
    if (selectedColumns.length === 0) return

    setProgress({
      total: data.length,
      processed: 0,
      percentage: 0,
      status: "processing"
    })

    try {
      await onExport(data, options)
      setProgress(prev => ({
        ...prev,
        processed: prev.total,
        percentage: 100,
        status: "complete"
      }))
      
      setTimeout(() => {
        setIsOpen(false)
        setProgress({
          total: 0,
          processed: 0,
          percentage: 0,
          status: "idle"
        })
      }, 2000)
    } catch (error) {
      setProgress(prev => ({
        ...prev,
        status: "error",
        message: error instanceof Error ? error.message : "Export failed"
      }))
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Export Data</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label>Export Format</Label>
            <Select
              value={options.format}
              onValueChange={(value: "csv" | "excel" | "json") => 
                setOptions(prev => ({ ...prev, format: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="csv">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    CSV File
                  </div>
                </SelectItem>
                <SelectItem value="excel">
                  <div className="flex items-center gap-2">
                    <FileSpreadsheet className="h-4 w-4" />
                    Excel File
                  </div>
                </SelectItem>
                <SelectItem value="json">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    JSON File
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Column Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label>Columns to Export</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSelectAll}
              >
                {selectedColumns.length === columns.length ? "Deselect All" : "Select All"}
              </Button>
            </div>
            
            <div className="max-h-48 overflow-y-auto space-y-2 border rounded-lg p-3">
              {columns.map((column) => (
                <div key={column.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={column.id}
                    checked={selectedColumns.includes(column.id)}
                    onCheckedChange={() => handleColumnToggle(column.id)}
                  />
                  <Label htmlFor={column.id} className="text-sm font-normal flex-1">
                    {column.label}
                  </Label>
                  <Badge variant="outline" className="text-xs">
                    {column.format || "text"}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          {/* Options */}
          <div className="space-y-3">
            <Label>Options</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeHeaders"
                  checked={options.includeHeaders}
                  onCheckedChange={(checked) => 
                    setOptions(prev => ({ ...prev, includeHeaders: !!checked }))
                  }
                />
                <Label htmlFor="includeHeaders" className="text-sm font-normal">
                  Include column headers
                </Label>
              </div>
              
              {options.format === "csv" && (
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <Label className="text-xs">Delimiter</Label>
                    <Select
                      value={options.delimiter}
                      onValueChange={(value) => 
                        setOptions(prev => ({ ...prev, delimiter: value }))
                      }
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value=",">Comma (,)</SelectItem>
                        <SelectItem value=";">Semicolon (;)</SelectItem>
                        <SelectItem value="\t">Tab</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs">Encoding</Label>
                    <Select
                      value={options.encoding}
                      onValueChange={(value) => 
                        setOptions(prev => ({ ...prev, encoding: value }))
                      }
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="UTF-8">UTF-8</SelectItem>
                        <SelectItem value="UTF-16">UTF-16</SelectItem>
                        <SelectItem value="ISO-8859-1">ISO-8859-1</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Progress */}
          {progress.status !== "idle" && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Export Progress</span>
                <span>{progress.percentage.toFixed(0)}%</span>
              </div>
              <Progress value={progress.percentage} />
              
              {progress.status === "processing" && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Processing {progress.processed} of {progress.total} records...
                </div>
              )}
              
              {progress.status === "complete" && (
                <div className="flex items-center gap-2 text-sm text-success">
                  <Check className="h-3 w-3" />
                  Export completed successfully!
                </div>
              )}
              
              {progress.status === "error" && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="h-3 w-3" />
                  {progress.message || "Export failed"}
                </div>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleExport}
              disabled={selectedColumns.length === 0 || progress.status === "processing"}
            >
              {progress.status === "processing" ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {data.length} Records
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Import Dialog Component
export function DataImportDialog({
  columns,
  onImport,
  trigger,
  acceptedFormats = [".csv", ".xlsx", ".json"]
}: {
  columns: ImportColumn[]
  onImport: (data: any[], mapping: Record<string, string>) => Promise<void>
  trigger?: React.ReactNode
  acceptedFormats?: string[]
}) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [file, setFile] = React.useState<File | null>(null)
  const [preview, setPreview] = React.useState<ImportPreview | null>(null)
  const [mapping, setMapping] = React.useState<Record<string, string>>({})
  const [progress, setProgress] = React.useState<ImportProgress>({
    total: 0,
    processed: 0,
    successful: 0,
    failed: 0,
    percentage: 0,
    status: "idle",
    errors: []
  })
  const [step, setStep] = React.useState<"upload" | "mapping" | "preview" | "import">("upload")

  const handleFileSelect = async (selectedFile: File) => {
    setFile(selectedFile)
    
    // Parse file and generate preview
    try {
      const text = await selectedFile.text()
      const lines = text.split("\n").filter(line => line.trim())
      const headers = lines[0].split(",").map(h => h.trim().replace(/"/g, ""))
      const rows = lines.slice(1, 6).map(line => 
        line.split(",").map(cell => cell.trim().replace(/"/g, ""))
      )
      
      setPreview({
        headers,
        rows,
        totalRows: lines.length - 1,
        errors: []
      })
      
      // Auto-map columns
      const autoMapping: Record<string, string> = {}
      headers.forEach(header => {
        const matchingColumn = columns.find(col => 
          col.sourceColumn.toLowerCase() === header.toLowerCase() ||
          col.targetField.toLowerCase() === header.toLowerCase()
        )
        if (matchingColumn) {
          autoMapping[header] = matchingColumn.targetField
        }
      })
      setMapping(autoMapping)
      
      setStep("mapping")
    } catch (error) {
      console.error("Failed to parse file:", error)
    }
  }

  const handleImport = async () => {
    if (!file || !preview) return

    setStep("import")
    setProgress({
      total: preview.totalRows,
      processed: 0,
      successful: 0,
      failed: 0,
      percentage: 0,
      status: "processing",
      errors: []
    })

    try {
      // Process file data with mapping
      const text = await file.text()
      const lines = text.split("\n").filter(line => line.trim())
      const headers = lines[0].split(",").map(h => h.trim().replace(/"/g, ""))
      const data = lines.slice(1).map(line => {
        const values = line.split(",").map(cell => cell.trim().replace(/"/g, ""))
        const row: any = {}
        headers.forEach((header, index) => {
          if (mapping[header]) {
            row[mapping[header]] = values[index]
          }
        })
        return row
      })

      await onImport(data, mapping)
      
      setProgress(prev => ({
        ...prev,
        processed: prev.total,
        successful: prev.total,
        percentage: 100,
        status: "complete"
      }))
      
      setTimeout(() => {
        setIsOpen(false)
        resetState()
      }, 2000)
    } catch (error) {
      setProgress(prev => ({
        ...prev,
        status: "error",
        errors: [{ row: 0, message: error instanceof Error ? error.message : "Import failed" }]
      }))
    }
  }

  const resetState = () => {
    setFile(null)
    setPreview(null)
    setMapping({})
    setProgress({
      total: 0,
      processed: 0,
      successful: 0,
      failed: 0,
      percentage: 0,
      status: "idle",
      errors: []
    })
    setStep("upload")
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Data</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Step Indicator */}
          <div className="flex items-center gap-2">
            {["upload", "mapping", "preview", "import"].map((stepName, index) => (
              <React.Fragment key={stepName}>
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium",
                  step === stepName ? "bg-primary text-primary-foreground" :
                  ["upload", "mapping", "preview", "import"].indexOf(step) > index ? "bg-success text-success-foreground" :
                  "bg-muted text-muted-foreground"
                )}>
                  {index + 1}
                </div>
                {index < 3 && <div className="flex-1 h-0.5 bg-muted" />}
              </React.Fragment>
            ))}
          </div>

          {/* Upload Step */}
          {step === "upload" && (
            <div className="space-y-4">
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                <Upload className="h-8 w-8 mx-auto mb-4 text-muted-foreground" />
                <p className="text-sm font-medium mb-2">Upload your data file</p>
                <p className="text-xs text-muted-foreground mb-4">
                  Supported formats: {acceptedFormats.join(", ")}
                </p>
                <input
                  type="file"
                  accept={acceptedFormats.join(",")}
                  onChange={(e) => {
                    const file = e.target.files?.[0]
                    if (file) handleFileSelect(file)
                  }}
                  className="hidden"
                  id="file-upload"
                />
                <Label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer">
                    Choose File
                  </Button>
                </Label>
              </div>
            </div>
          )}

          {/* Mapping Step */}
          {step === "mapping" && preview && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Map Columns</h3>
                <p className="text-xs text-muted-foreground">
                  Map the columns from your file to the target fields
                </p>
              </div>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {preview.headers.map((header) => (
                  <div key={header} className="flex items-center gap-3 p-2 border rounded">
                    <div className="flex-1">
                      <p className="text-sm font-medium">{header}</p>
                      <p className="text-xs text-muted-foreground">
                        Sample: {preview.rows[0]?.[preview.headers.indexOf(header)] || "N/A"}
                      </p>
                    </div>
                    <div className="w-48">
                      <Select
                        value={mapping[header] || ""}
                        onValueChange={(value) => 
                          setMapping(prev => ({ ...prev, [header]: value }))
                        }
                      >
                        <SelectTrigger className="h-8">
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Skip column</SelectItem>
                          {columns.map((column) => (
                            <SelectItem key={column.targetField} value={column.targetField}>
                              {column.sourceColumn}
                              {column.required && <span className="text-destructive ml-1">*</span>}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setStep("upload")}>
                  Back
                </Button>
                <Button onClick={() => setStep("preview")}>
                  Preview Import
                </Button>
              </div>
            </div>
          )}

          {/* Preview Step */}
          {step === "preview" && preview && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Preview Import</h3>
                <p className="text-xs text-muted-foreground">
                  Review the first few rows before importing {preview.totalRows} records
                </p>
              </div>
              
              <div className="border rounded-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full text-xs">
                    <thead className="bg-muted">
                      <tr>
                        {Object.values(mapping).filter(Boolean).map((field) => (
                          <th key={field} className="p-2 text-left font-medium">
                            {field}
                          </th>
                        ))}
                      </tr>
                    </thead>
                    <tbody>
                      {preview.rows.slice(0, 3).map((row, rowIndex) => (
                        <tr key={rowIndex} className="border-t">
                          {Object.entries(mapping).filter(([_, field]) => field).map(([header, field]) => (
                            <td key={field} className="p-2">
                              {row[preview.headers.indexOf(header)] || ""}
                            </td>
                          ))}
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setStep("mapping")}>
                  Back
                </Button>
                <Button onClick={handleImport}>
                  Import {preview.totalRows} Records
                </Button>
              </div>
            </div>
          )}

          {/* Import Step */}
          {step === "import" && (
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Importing Data</h3>
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{progress.percentage.toFixed(0)}%</span>
                </div>
                <Progress value={progress.percentage} className="mt-2" />
              </div>
              
              {progress.status === "processing" && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin" />
                  Processing {progress.processed} of {progress.total} records...
                </div>
              )}
              
              {progress.status === "complete" && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-success">
                    <Check className="h-3 w-3" />
                    Import completed successfully!
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {progress.successful} records imported, {progress.failed} failed
                  </div>
                </div>
              )}
              
              {progress.status === "error" && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-3 w-3" />
                    Import failed
                  </div>
                  {progress.errors.length > 0 && (
                    <div className="text-xs text-muted-foreground max-h-32 overflow-y-auto">
                      {progress.errors.map((error, index) => (
                        <div key={index}>
                          Row {error.row}: {error.message}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
