"use client"

import { <PERSON>, Clock, CheckCircle, FileText } from "lucide-react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/components/auth-provider"

export default function InspectionPage() {
  // Add authentication hook to ensure proper auth flow
  const { user } = useAuth()
  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <Search className="h-8 w-8 text-purple-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Inspection</h1>
            <p className="text-gray-600">Manage quality inspections and compliance</p>
          </div>
        </div>

        {/* Coming Soon Card */}
        <Card className="border-2 border-dashed border-purple-200 bg-purple-50/50">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-purple-100">
              <Clock className="h-8 w-8 text-purple-600" />
            </div>
            <CardTitle className="text-2xl text-purple-900">Coming Soon</CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-gray-600 max-w-md mx-auto">
              The Inspection module is currently under development. This feature will enable 
              comprehensive quality control and compliance inspection management.
            </p>
            
            <div className="flex flex-wrap justify-center gap-2">
              <Badge variant="outline" className="bg-white">
                <Search className="h-3 w-3 mr-1" />
                Quality Control
              </Badge>
              <Badge variant="outline" className="bg-white">
                <CheckCircle className="h-3 w-3 mr-1" />
                Compliance Checks
              </Badge>
              <Badge variant="outline" className="bg-white">
                <FileText className="h-3 w-3 mr-1" />
                Inspection Reports
              </Badge>
            </div>

            <div className="mt-6 p-4 bg-white rounded-lg border">
              <p className="text-sm text-gray-500">
                Expected features: Inspection scheduling, quality checklists, compliance tracking, 
                photo documentation, and automated reporting systems.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
