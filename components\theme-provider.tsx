"use client"

import * as React from "react"

interface ThemeProviderProps {
  children: React.ReactNode
  attribute?: string
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

export function ThemeProvider({
  children,
  ...props
}: ThemeProviderProps) {
  // Simple theme provider without next-themes dependency
  // Just provides a stable wrapper for now
  return (
    <>
      {children}
    </>
  )
}