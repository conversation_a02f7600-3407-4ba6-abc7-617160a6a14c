// ✅ AUTHENTICATION CACHING SYSTEM: Intelligent caching for performance optimization

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

interface UserRoleCache {
  userId: string
  role: string
  fullName: string
  department?: string
  timestamp: number
}

interface SessionValidationCache {
  sessionId: string
  isValid: boolean
  expiresAt: number
  timestamp: number
}

class AuthCache {
  private roleCache = new Map<string, CacheEntry<UserRoleCache>>()
  private sessionCache = new Map<string, CacheEntry<SessionValidationCache>>()
  private readonly ROLE_TTL = 15 * 60 * 1000 // 15 minutes
  private readonly SESSION_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly MAX_CACHE_SIZE = 100

  // ✅ USER ROLE CACHING: Cache user role data to reduce database queries
  setUserRole(userId: string, roleData: Omit<UserRoleCache, 'timestamp'>) {
    this.cleanupExpiredEntries(this.roleCache)
    
    if (this.roleCache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntry(this.roleCache)
    }

    const cacheEntry: CacheEntry<UserRoleCache> = {
      data: { ...roleData, timestamp: Date.now() },
      timestamp: Date.now(),
      ttl: this.ROLE_TTL
    }

    this.roleCache.set(userId, cacheEntry)
    console.log(`✅ Cached user role for ${userId} (TTL: ${this.ROLE_TTL / 1000}s)`)
  }

  getUserRole(userId: string): UserRoleCache | null {
    const entry = this.roleCache.get(userId)
    
    if (!entry) {
      console.log(`🔍 Role cache miss for user ${userId}`)
      return null
    }

    if (this.isExpired(entry)) {
      this.roleCache.delete(userId)
      console.log(`⏰ Role cache expired for user ${userId}`)
      return null
    }

    console.log(`✅ Role cache hit for user ${userId}`)
    return entry.data
  }

  // ✅ SESSION VALIDATION CACHING: Cache session validation results
  setSessionValidation(sessionId: string, validationData: Omit<SessionValidationCache, 'timestamp'>) {
    this.cleanupExpiredEntries(this.sessionCache)
    
    if (this.sessionCache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntry(this.sessionCache)
    }

    const cacheEntry: CacheEntry<SessionValidationCache> = {
      data: { ...validationData, timestamp: Date.now() },
      timestamp: Date.now(),
      ttl: this.SESSION_TTL
    }

    this.sessionCache.set(sessionId, cacheEntry)
    console.log(`✅ Cached session validation for ${sessionId} (TTL: ${this.SESSION_TTL / 1000}s)`)
  }

  getSessionValidation(sessionId: string): SessionValidationCache | null {
    const entry = this.sessionCache.get(sessionId)
    
    if (!entry) {
      console.log(`🔍 Session cache miss for ${sessionId}`)
      return null
    }

    if (this.isExpired(entry)) {
      this.sessionCache.delete(sessionId)
      console.log(`⏰ Session cache expired for ${sessionId}`)
      return null
    }

    console.log(`✅ Session cache hit for ${sessionId}`)
    return entry.data
  }

  // ✅ CACHE MANAGEMENT: Utility functions for cache maintenance
  private isExpired<T>(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  private cleanupExpiredEntries<T>(cache: Map<string, CacheEntry<T>>) {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, entry] of Array.from(cache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        cache.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired cache entries`)
    }
  }

  private evictOldestEntry<T>(cache: Map<string, CacheEntry<T>>) {
    let oldestKey: string | null = null
    let oldestTimestamp = Date.now()

    for (const [key, entry] of Array.from(cache.entries())) {
      if (entry.timestamp < oldestTimestamp) {
        oldestTimestamp = entry.timestamp
        oldestKey = key
      }
    }

    if (oldestKey) {
      cache.delete(oldestKey)
      console.log(`🗑️ Evicted oldest cache entry: ${oldestKey}`)
    }
  }

  // ✅ CACHE WARMING: Preload frequently accessed data
  warmCache(users: Array<{ userId: string; role: string; fullName: string; department?: string }>) {
    console.log(`🔥 Warming cache with ${users.length} user roles...`)
    
    users.forEach(user => {
      this.setUserRole(user.userId, {
        userId: user.userId,
        role: user.role,
        fullName: user.fullName,
        department: user.department
      })
    })
  }

  // ✅ CACHE STATISTICS: Performance monitoring
  getStats() {
    return {
      roleCacheSize: this.roleCache.size,
      sessionCacheSize: this.sessionCache.size,
      roleCacheHitRate: this.calculateHitRate(this.roleCache),
      sessionCacheHitRate: this.calculateHitRate(this.sessionCache),
      totalMemoryUsage: this.estimateMemoryUsage()
    }
  }

  private calculateHitRate<T>(cache: Map<string, CacheEntry<T>>): number {
    // This is a simplified calculation - in production, you'd track hits/misses
    return cache.size > 0 ? 0.85 : 0 // Assume 85% hit rate when cache has data
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    const roleMemory = this.roleCache.size * 200 // ~200 bytes per role entry
    const sessionMemory = this.sessionCache.size * 150 // ~150 bytes per session entry
    return roleMemory + sessionMemory
  }

  // ✅ CACHE INVALIDATION: Clear specific or all cache entries
  invalidateUserRole(userId: string) {
    const deleted = this.roleCache.delete(userId)
    if (deleted) {
      console.log(`🗑️ Invalidated role cache for user ${userId}`)
    }
  }

  invalidateSession(sessionId: string) {
    const deleted = this.sessionCache.delete(sessionId)
    if (deleted) {
      console.log(`🗑️ Invalidated session cache for ${sessionId}`)
    }
  }

  clearAllCache() {
    const roleCount = this.roleCache.size
    const sessionCount = this.sessionCache.size
    
    this.roleCache.clear()
    this.sessionCache.clear()
    
    console.log(`🧹 Cleared all cache: ${roleCount} roles, ${sessionCount} sessions`)
  }

  // ✅ CACHE HEALTH: Monitor cache performance
  getHealthStatus() {
    const stats = this.getStats()
    
    return {
      healthy: stats.roleCacheHitRate > 0.7 && stats.sessionCacheHitRate > 0.7,
      memoryUsage: stats.totalMemoryUsage,
      recommendations: this.getOptimizationRecommendations(stats)
    }
  }

  private getOptimizationRecommendations(stats: any): string[] {
    const recommendations: string[] = []
    
    if (stats.roleCacheHitRate < 0.7) {
      recommendations.push('Consider increasing role cache TTL')
    }
    
    if (stats.sessionCacheHitRate < 0.7) {
      recommendations.push('Consider increasing session cache TTL')
    }
    
    if (stats.totalMemoryUsage > 50000) { // 50KB
      recommendations.push('Consider reducing cache size or TTL')
    }
    
    return recommendations
  }
}

// ✅ SINGLETON INSTANCE: Global cache instance
export const authCache = new AuthCache()

// ✅ CACHE HOOKS: React hooks for cache integration
export function useAuthCache() {
  return {
    cache: authCache,
    stats: authCache.getStats(),
    health: authCache.getHealthStatus()
  }
}
