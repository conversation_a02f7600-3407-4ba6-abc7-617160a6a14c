"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import {
  Zap, Clock, Database, Image, Code, Wifi,
  TrendingUp, TrendingDown, AlertTriangle, CheckCircle,
  Monitor, Smartphone, Tablet, BarChart, Activity
} from "lucide-react"
import { cn } from "@/lib/utils"

// Performance Types
export interface CoreWebVitals {
  lcp: { value: number; rating: "good" | "needs-improvement" | "poor" }
  fid: { value: number; rating: "good" | "needs-improvement" | "poor" }
  cls: { value: number; rating: "good" | "needs-improvement" | "poor" }
  fcp: { value: number; rating: "good" | "needs-improvement" | "poor" }
  ttfb: { value: number; rating: "good" | "needs-improvement" | "poor" }
}

export interface BundleAnalysis {
  totalSize: number
  gzippedSize: number
  chunks: {
    name: string
    size: number
    gzippedSize: number
    type: "initial" | "async" | "runtime"
  }[]
  dependencies: {
    name: string
    size: number
    percentage: number
  }[]
}

export interface PerformanceMetrics {
  coreWebVitals: CoreWebVitals
  bundleAnalysis: BundleAnalysis
  networkRequests: {
    total: number
    cached: number
    failed: number
    averageTime: number
  }
  memoryUsage: {
    used: number
    total: number
    percentage: number
  }
  renderMetrics: {
    componentsRendered: number
    averageRenderTime: number
    slowestComponent: string
    reRenders: number
  }
  deviceMetrics: {
    mobile: { score: number; issues: string[] }
    tablet: { score: number; issues: string[] }
    desktop: { score: number; issues: string[] }
  }
  lastMeasured: Date
}

// Mock performance data
const mockPerformanceMetrics: PerformanceMetrics = {
  coreWebVitals: {
    lcp: { value: 2.1, rating: "good" },
    fid: { value: 85, rating: "needs-improvement" },
    cls: { value: 0.08, rating: "needs-improvement" },
    fcp: { value: 1.4, rating: "good" },
    ttfb: { value: 320, rating: "good" }
  },
  bundleAnalysis: {
    totalSize: 1250000, // 1.25MB
    gzippedSize: 420000, // 420KB
    chunks: [
      { name: "main", size: 450000, gzippedSize: 150000, type: "initial" },
      { name: "vendor", size: 600000, gzippedSize: 200000, type: "initial" },
      { name: "customers", size: 120000, gzippedSize: 40000, type: "async" },
      { name: "deals", size: 80000, gzippedSize: 30000, type: "async" }
    ],
    dependencies: [
      { name: "react", size: 180000, percentage: 14.4 },
      { name: "next", size: 220000, percentage: 17.6 },
      { name: "@radix-ui", size: 150000, percentage: 12.0 },
      { name: "lucide-react", size: 80000, percentage: 6.4 },
      { name: "tailwindcss", size: 60000, percentage: 4.8 }
    ]
  },
  networkRequests: {
    total: 45,
    cached: 32,
    failed: 2,
    averageTime: 180
  },
  memoryUsage: {
    used: 85,
    total: 512,
    percentage: 16.6
  },
  renderMetrics: {
    componentsRendered: 127,
    averageRenderTime: 2.3,
    slowestComponent: "EnhancedDataTable",
    reRenders: 8
  },
  deviceMetrics: {
    mobile: { score: 78, issues: ["Bundle size too large", "Touch targets too small"] },
    tablet: { score: 85, issues: ["Minor layout shifts"] },
    desktop: { score: 92, issues: [] }
  },
  lastMeasured: new Date()
}

// Performance Monitor Component
export function PerformanceMonitor({
  metrics = mockPerformanceMetrics,
  onRunAnalysis,
  onOptimize,
  className
}: {
  metrics?: PerformanceMetrics
  onRunAnalysis?: () => Promise<void>
  onOptimize?: (type: string) => Promise<void>
  className?: string
}) {
  const [isAnalyzing, setIsAnalyzing] = React.useState(false)
  const [selectedTab, setSelectedTab] = React.useState<"overview" | "vitals" | "bundle" | "network">("overview")

  const getRatingColor = (rating: string) => {
    switch (rating) {
      case "good": return "text-green-600"
      case "needs-improvement": return "text-yellow-600"
      case "poor": return "text-red-600"
      default: return "text-gray-600"
    }
  }

  const getRatingBadge = (rating: string) => {
    switch (rating) {
      case "good": return "bg-green-100 text-green-800"
      case "needs-improvement": return "bg-yellow-100 text-yellow-800"
      case "poor": return "bg-red-100 text-red-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 B"
    const k = 1024
    const sizes = ["B", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i]
  }

  const getOverallScore = () => {
    const vitalsScore = Object.values(metrics.coreWebVitals).reduce((acc, vital) => {
      return acc + (vital.rating === "good" ? 100 : vital.rating === "needs-improvement" ? 70 : 40)
    }, 0) / Object.keys(metrics.coreWebVitals).length

    const bundleScore = metrics.bundleAnalysis.gzippedSize < 500000 ? 100 : 
                       metrics.bundleAnalysis.gzippedSize < 1000000 ? 80 : 60

    const networkScore = metrics.networkRequests.averageTime < 200 ? 100 :
                        metrics.networkRequests.averageTime < 500 ? 80 : 60

    const memoryScore = metrics.memoryUsage.percentage < 20 ? 100 :
                       metrics.memoryUsage.percentage < 40 ? 80 : 60

    return Math.round((vitalsScore + bundleScore + networkScore + memoryScore) / 4)
  }

  const handleRunAnalysis = async () => {
    setIsAnalyzing(true)
    try {
      await onRunAnalysis?.()
    } finally {
      setIsAnalyzing(false)
    }
  }

  const overallScore = getOverallScore()

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="heading-2 text-foreground flex items-center gap-2">
            <Activity className="h-6 w-6" />
            Performance Monitor
          </h2>
          <p className="body-small text-muted-foreground mt-1">
            Real-time performance analysis and optimization recommendations
          </p>
        </div>
        <Button 
          onClick={handleRunAnalysis} 
          disabled={isAnalyzing}
          className="gap-2"
        >
          <Zap className="h-4 w-4" />
          {isAnalyzing ? "Analyzing..." : "Run Analysis"}
        </Button>
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Overall Performance Score</span>
            <Badge 
              className={cn(
                overallScore >= 90 ? "bg-green-100 text-green-800" :
                overallScore >= 70 ? "bg-yellow-100 text-yellow-800" :
                "bg-red-100 text-red-800"
              )}
            >
              {overallScore >= 90 ? "Excellent" : overallScore >= 70 ? "Good" : "Needs Work"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center mb-4">
            <div className={cn(
              "text-4xl font-bold",
              overallScore >= 90 ? "text-green-600" :
              overallScore >= 70 ? "text-yellow-600" :
              "text-red-600"
            )}>
              {overallScore}
            </div>
            <p className="text-sm text-muted-foreground">Performance Score</p>
          </div>
          <Progress value={overallScore} className="h-2 mb-4" />
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="flex items-center justify-center mb-1">
                <Monitor className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="text-lg font-semibold">{metrics.deviceMetrics.desktop.score}</div>
              <p className="text-xs text-muted-foreground">Desktop</p>
            </div>
            <div>
              <div className="flex items-center justify-center mb-1">
                <Tablet className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="text-lg font-semibold">{metrics.deviceMetrics.tablet.score}</div>
              <p className="text-xs text-muted-foreground">Tablet</p>
            </div>
            <div>
              <div className="flex items-center justify-center mb-1">
                <Smartphone className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="text-lg font-semibold">{metrics.deviceMetrics.mobile.score}</div>
              <p className="text-xs text-muted-foreground">Mobile</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        {[
          { id: "overview", label: "Overview", icon: BarChart },
          { id: "vitals", label: "Core Web Vitals", icon: Zap },
          { id: "bundle", label: "Bundle Analysis", icon: Code },
          { id: "network", label: "Network", icon: Wifi }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setSelectedTab(tab.id as any)}
            className={cn(
              "flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              selectedTab === tab.id
                ? "bg-background text-foreground shadow-sm"
                : "text-muted-foreground hover:text-foreground"
            )}
          >
            <tab.icon className="h-4 w-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {selectedTab === "overview" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Clock className="h-6 w-6 mx-auto mb-2 text-blue-600" />
              <div className="text-2xl font-bold">{metrics.coreWebVitals.lcp.value}s</div>
              <p className="text-xs text-muted-foreground">Largest Contentful Paint</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Code className="h-6 w-6 mx-auto mb-2 text-purple-600" />
              <div className="text-2xl font-bold">{formatBytes(metrics.bundleAnalysis.gzippedSize)}</div>
              <p className="text-xs text-muted-foreground">Bundle Size (Gzipped)</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Wifi className="h-6 w-6 mx-auto mb-2 text-green-600" />
              <div className="text-2xl font-bold">{metrics.networkRequests.total}</div>
              <p className="text-xs text-muted-foreground">Network Requests</p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Database className="h-6 w-6 mx-auto mb-2 text-orange-600" />
              <div className="text-2xl font-bold">{metrics.memoryUsage.percentage}%</div>
              <p className="text-xs text-muted-foreground">Memory Usage</p>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === "vitals" && (
        <div className="space-y-4">
          {Object.entries(metrics.coreWebVitals).map(([key, vital]) => (
            <Card key={key}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium capitalize">{key.toUpperCase()}</h4>
                    <p className="text-sm text-muted-foreground">
                      {key === "lcp" && "Largest Contentful Paint"}
                      {key === "fid" && "First Input Delay"}
                      {key === "cls" && "Cumulative Layout Shift"}
                      {key === "fcp" && "First Contentful Paint"}
                      {key === "ttfb" && "Time to First Byte"}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={cn("text-lg font-semibold", getRatingColor(vital.rating))}>
                      {vital.value}{key === "fid" || key === "ttfb" ? "ms" : key === "cls" ? "" : "s"}
                    </div>
                    <Badge className={getRatingBadge(vital.rating)}>
                      {vital.rating.replace("-", " ")}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedTab === "bundle" && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bundle Size Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm text-muted-foreground">Total Size</p>
                  <p className="text-lg font-semibold">{formatBytes(metrics.bundleAnalysis.totalSize)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Gzipped Size</p>
                  <p className="text-lg font-semibold">{formatBytes(metrics.bundleAnalysis.gzippedSize)}</p>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-3">
                <h4 className="font-medium">Chunks</h4>
                {metrics.bundleAnalysis.chunks.map((chunk) => (
                  <div key={chunk.name} className="flex items-center justify-between">
                    <div>
                      <span className="font-medium">{chunk.name}</span>
                      <Badge variant="outline" className="ml-2">
                        {chunk.type}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">{formatBytes(chunk.gzippedSize)}</div>
                      <div className="text-xs text-muted-foreground">
                        {formatBytes(chunk.size)} uncompressed
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Top Dependencies</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {metrics.bundleAnalysis.dependencies.map((dep) => (
                  <div key={dep.name} className="flex items-center justify-between">
                    <span className="font-medium">{dep.name}</span>
                    <div className="text-right">
                      <div className="text-sm">{formatBytes(dep.size)}</div>
                      <div className="text-xs text-muted-foreground">{dep.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === "network" && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{metrics.networkRequests.total}</div>
                <p className="text-xs text-muted-foreground">Total Requests</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{metrics.networkRequests.cached}</div>
                <p className="text-xs text-muted-foreground">Cached</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{metrics.networkRequests.failed}</div>
                <p className="text-xs text-muted-foreground">Failed</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{metrics.networkRequests.averageTime}ms</div>
                <p className="text-xs text-muted-foreground">Average Time</p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Optimization Recommendations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Enable HTTP/2</p>
                    <p className="text-sm text-muted-foreground">
                      Reduce request latency with multiplexing
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Implement Service Worker</p>
                    <p className="text-sm text-muted-foreground">
                      Cache resources for offline functionality
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-medium">Optimize Image Loading</p>
                    <p className="text-sm text-muted-foreground">
                      Use next/image for automatic optimization
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Optimizations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            <Button 
              variant="outline" 
              className="h-auto flex-col gap-2 p-4"
              onClick={() => onOptimize?.("bundle")}
            >
              <Code className="h-5 w-5" />
              <span className="text-sm">Optimize Bundle</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto flex-col gap-2 p-4"
              onClick={() => onOptimize?.("images")}
            >
              <Image className="h-5 w-5" />
              <span className="text-sm">Optimize Images</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto flex-col gap-2 p-4"
              onClick={() => onOptimize?.("cache")}
            >
              <Database className="h-5 w-5" />
              <span className="text-sm">Setup Caching</span>
            </Button>
            
            <Button 
              variant="outline" 
              className="h-auto flex-col gap-2 p-4"
              onClick={() => onOptimize?.("lazy")}
            >
              <Zap className="h-5 w-5" />
              <span className="text-sm">Lazy Loading</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Last Updated */}
      <div className="text-center text-sm text-muted-foreground">
        Last measured: {metrics.lastMeasured.toLocaleDateString()} at {metrics.lastMeasured.toLocaleTimeString()}
      </div>
    </div>
  )
}
