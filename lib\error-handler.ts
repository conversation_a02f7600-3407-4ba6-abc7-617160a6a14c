import { NextRequest, NextResponse } from 'next/server'

// Error types
export interface AppError extends Error {
  statusCode?: number
  code?: string
  details?: any
}

// Error codes
export const ERROR_CODES = {
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  RATE_LIMIT: 'RATE_LIMIT',
  NETWORK_ERROR: 'NETWORK_ERROR'
} as const

// Create custom error
export function createError(
  message: string,
  statusCode: number = 500,
  code?: string,
  details?: any
): AppError {
  const error = new Error(message) as AppError
  error.statusCode = statusCode
  error.code = code
  error.details = details
  return error
}

// Error response formatter
export function formatErrorResponse(error: AppError | Error, request?: NextRequest) {
  const appError = error as AppError
  const statusCode = appError.statusCode || 500
  const code = appError.code || ERROR_CODES.INTERNAL_ERROR
  
  // Log error details
  console.error('API Error:', {
    message: error.message,
    statusCode,
    code,
    details: appError.details,
    stack: error.stack,
    url: request?.url,
    method: request?.method,
    timestamp: new Date().toISOString()
  })

  // Determine user-friendly message
  const userMessage = getUserFriendlyMessage(statusCode, code, error.message)

  // Response payload
  const response = {
    error: true,
    message: userMessage,
    code,
    timestamp: new Date().toISOString(),
    ...(process.env.NODE_ENV === 'development' && {
      details: appError.details,
      stack: error.stack
    })
  }

  return NextResponse.json(response, { status: statusCode })
}

// Get user-friendly error messages
function getUserFriendlyMessage(statusCode: number, code: string, originalMessage: string): string {
  switch (statusCode) {
    case 400:
      return 'Invalid request. Please check your input and try again.'
    case 401:
      return 'Authentication required. Please log in and try again.'
    case 403:
      return 'Access denied. You do not have permission to perform this action.'
    case 404:
      return 'The requested resource was not found.'
    case 409:
      return 'Conflict detected. The resource already exists or is in use.'
    case 422:
      return 'Validation failed. Please check your input and try again.'
    case 429:
      return 'Too many requests. Please wait a moment and try again.'
    case 500:
      return 'Internal server error. Please try again later.'
    case 502:
      return 'Service temporarily unavailable. Please try again later.'
    case 503:
      return 'Service maintenance in progress. Please try again later.'
    default:
      return originalMessage || 'An unexpected error occurred.'
  }
}

// Database error handler
export function handleDatabaseError(error: any): AppError {
  console.error('Database error:', error)

  // Supabase specific errors
  if (error.code) {
    switch (error.code) {
      case '23505': // Unique violation
        return createError(
          'A record with this information already exists.',
          409,
          ERROR_CODES.VALIDATION_ERROR,
          { constraint: error.constraint }
        )
      case '23503': // Foreign key violation
        return createError(
          'Referenced record does not exist.',
          400,
          ERROR_CODES.VALIDATION_ERROR,
          { constraint: error.constraint }
        )
      case '23502': // Not null violation
        return createError(
          'Required field is missing.',
          400,
          ERROR_CODES.VALIDATION_ERROR,
          { column: error.column }
        )
      case '42501': // Insufficient privilege
        return createError(
          'Access denied to this resource.',
          403,
          ERROR_CODES.FORBIDDEN
        )
      case '42P01': // Undefined table
        return createError(
          'Database configuration error.',
          500,
          ERROR_CODES.DATABASE_ERROR
        )
      default:
        return createError(
          'Database operation failed.',
          500,
          ERROR_CODES.DATABASE_ERROR,
          { code: error.code, message: error.message }
        )
    }
  }

  // Generic database error
  return createError(
    'Database operation failed.',
    500,
    ERROR_CODES.DATABASE_ERROR,
    { message: error.message }
  )
}

// Validation error handler
export function handleValidationError(errors: Record<string, string>): AppError {
  const errorMessages = Object.values(errors).filter(Boolean)
  const message = errorMessages.length > 0 
    ? `Validation failed: ${errorMessages.join(', ')}`
    : 'Validation failed'

  return createError(
    message,
    422,
    ERROR_CODES.VALIDATION_ERROR,
    { fields: errors }
  )
}

// Authentication error handler
export function handleAuthError(error: any): AppError {
  console.error('Auth error:', error)

  if (error.message?.includes('JWT')) {
    return createError(
      'Invalid or expired session. Please log in again.',
      401,
      ERROR_CODES.UNAUTHORIZED
    )
  }

  if (error.message?.includes('refresh')) {
    return createError(
      'Session expired. Please log in again.',
      401,
      ERROR_CODES.UNAUTHORIZED
    )
  }

  return createError(
    'Authentication failed.',
    401,
    ERROR_CODES.UNAUTHORIZED,
    { message: error.message }
  )
}

// API route wrapper with error handling
export function withErrorHandler(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any): Promise<NextResponse> => {
    try {
      return await handler(request, context)
    } catch (error) {
      // Handle different error types
      if (error instanceof Error) {
        const appError = error as AppError
        
        // Database errors
        if (appError.code?.startsWith('23') || appError.code?.startsWith('42')) {
          return formatErrorResponse(handleDatabaseError(error), request)
        }
        
        // Auth errors
        if (appError.message?.includes('auth') || appError.message?.includes('Unauthorized')) {
          return formatErrorResponse(handleAuthError(error), request)
        }
        
        // Already formatted app errors
        if (appError.statusCode) {
          return formatErrorResponse(appError, request)
        }
      }

      // Generic error
      const genericError = createError(
        'An unexpected error occurred.',
        500,
        ERROR_CODES.INTERNAL_ERROR
      )
      
      return formatErrorResponse(genericError, request)
    }
  }
}

// Client-side error handler
export function handleClientError(error: any): string {
  console.error('Client error:', error)

  // Network errors
  if (error.name === 'NetworkError' || error.message?.includes('fetch')) {
    return 'Network error. Please check your connection and try again.'
  }

  // API errors
  if (error.response?.data?.message) {
    return error.response.data.message
  }

  // Generic error
  return error.message || 'An unexpected error occurred.'
}

// Error boundary helper
export function logError(error: Error, errorInfo?: any) {
  console.error('Application error:', {
    message: error.message,
    stack: error.stack,
    errorInfo,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'server',
    url: typeof window !== 'undefined' ? window.location.href : 'server'
  })

  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, or Bugsnag
}

// Health check helper
export function createHealthCheck() {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  }
}
