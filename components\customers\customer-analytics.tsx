"use client"

import * as React from "react"
import { useState, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DatePickerWithRange } from "@/components/ui/date-picker"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  <PERSON>ricCard, 
  SimpleBar<PERSON>hart, 
  SimplePie<PERSON>hart, 
  TrendChart,
  type MetricData,
  type ChartDataPoint,
  type TimeSeriesData
} from "@/components/tables/data-visualization"
import { CustomerFormData } from "@/app/types/customer"
import {
  Users, TrendingUp, DollarSign, Star, Globe, Building2,
  Calendar, Target, Award, MapPin, Briefcase, CreditCard,
  Bar<PERSON>hart, <PERSON><PERSON>hart, <PERSON>Chart, Filter, Download,
  ArrowUpRight, ArrowDownRight, Minus, AlertCircle,
  CheckCircle, Clock, Zap, Activity
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DateRange } from "react-day-picker"

// Analytics Types
export interface CustomerAnalytics {
  overview: {
    totalCustomers: number
    activeCustomers: number
    newCustomers: number
    churnedCustomers: number
    growthRate: number
    retentionRate: number
    avgCustomerValue: number
    totalRevenue: number
  }
  demographics: {
    byCountry: ChartDataPoint[]
    byCity: ChartDataPoint[]
    byIndustry: ChartDataPoint[]
    byBusinessType: ChartDataPoint[]
    byCompanySize: ChartDataPoint[]
  }
  segmentation: {
    byTier: ChartDataPoint[]
    bySource: ChartDataPoint[]
    byStatus: ChartDataPoint[]
    byPaymentTerms: ChartDataPoint[]
    byShippingMethod: ChartDataPoint[]
  }
  financial: {
    revenueByTier: ChartDataPoint[]
    volumeDistribution: ChartDataPoint[]
    creditLimitUtilization: ChartDataPoint[]
    paymentTermsBreakdown: ChartDataPoint[]
    currencyDistribution: ChartDataPoint[]
  }
  trends: {
    customerGrowth: TimeSeriesData[]
    revenueGrowth: TimeSeriesData[]
    tierProgression: TimeSeriesData[]
    acquisitionTrends: TimeSeriesData[]
  }
  insights: {
    topPerformers: CustomerFormData[]
    riskCustomers: CustomerFormData[]
    growthOpportunities: CustomerFormData[]
    recentActivity: Array<{
      type: string
      customer: string
      description: string
      date: string
      impact: "positive" | "negative" | "neutral"
    }>
  }
}

// Analytics Calculation Functions
export function calculateCustomerAnalytics(
  customers: CustomerFormData[],
  dateRange?: DateRange
): CustomerAnalytics {
  // Filter customers by date range if provided
  const filteredCustomers = dateRange?.from && dateRange?.to 
    ? customers.filter(customer => {
        const customerDate = new Date(customer.customer_since || Date.now())
        return customerDate >= dateRange.from! && customerDate <= dateRange.to!
      })
    : customers

  // Overview calculations
  const totalCustomers = filteredCustomers.length
  const activeCustomers = filteredCustomers.filter(c => c.status === "Active" || !c.status).length
  const newCustomers = filteredCustomers.filter(c => {
    const customerDate = new Date(c.customer_since || Date.now())
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    return customerDate >= thirtyDaysAgo
  }).length
  
  const totalRevenue = filteredCustomers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
  const avgCustomerValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0
  const growthRate = 12.5 // Mock calculation - would be based on historical data
  const retentionRate = 87.3 // Mock calculation

  // Demographics calculations
  const countryDistribution = filteredCustomers.reduce((acc, customer) => {
    const country = customer.country || "Unknown"
    acc[country] = (acc[country] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const cityDistribution = filteredCustomers.reduce((acc, customer) => {
    const city = customer.city || "Unknown"
    acc[city] = (acc[city] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const industryDistribution = filteredCustomers.reduce((acc, customer) => {
    const industry = customer.industry || "Other"
    acc[industry] = (acc[industry] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const businessTypeDistribution = filteredCustomers.reduce((acc, customer) => {
    const type = customer.business_type || "Other"
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const companySizeDistribution = filteredCustomers.reduce((acc, customer) => {
    const size = customer.company_size || "Small"
    acc[size] = (acc[size] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Segmentation calculations
  const tierDistribution = filteredCustomers.reduce((acc, customer) => {
    const tier = customer.customer_tier || "Bronze"
    acc[tier] = (acc[tier] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const sourceDistribution = filteredCustomers.reduce((acc, customer) => {
    const source = customer.source || "Unknown"
    acc[source] = (acc[source] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const statusDistribution = filteredCustomers.reduce((acc, customer) => {
    const status = customer.status || "active"
    acc[status] = (acc[status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Financial calculations
  const revenueByTier = Object.entries(tierDistribution).map(([tier, count]) => {
    const tierRevenue = filteredCustomers
      .filter(c => (c.customer_tier || "Bronze") === tier)
      .reduce((sum, c) => sum + (c.annual_volume || 0), 0)
    
    return {
      label: tier,
      value: tierRevenue,
      color: getTierColor(tier)
    }
  })

  const currencyDistribution = filteredCustomers.reduce((acc, customer) => {
    const currency = customer.currency_preference || "USD"
    acc[currency] = (acc[currency] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Generate mock trend data
  const customerGrowthTrend = generateMockTrendData("customers", 12)
  const revenueGrowthTrend = generateMockTrendData("revenue", 12)

  // Insights calculations
  const topPerformers = filteredCustomers
    .filter(c => c.annual_volume && c.annual_volume > 0)
    .sort((a, b) => (b.annual_volume || 0) - (a.annual_volume || 0))
    .slice(0, 5)

  const riskCustomers = filteredCustomers
    .filter(c => c.status === "Inactive" || (c.annual_volume || 0) < avgCustomerValue * 0.3)
    .slice(0, 5)

  const growthOpportunities = filteredCustomers
    .filter(c => c.customer_tier === "Bronze" && (c.annual_volume || 0) > avgCustomerValue * 0.8)
    .slice(0, 5)

  return {
    overview: {
      totalCustomers,
      activeCustomers,
      newCustomers,
      churnedCustomers: totalCustomers - activeCustomers,
      growthRate,
      retentionRate,
      avgCustomerValue,
      totalRevenue
    },
    demographics: {
      byCountry: Object.entries(countryDistribution).map(([country, count]) => ({
        label: country,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value).slice(0, 10),
      byCity: Object.entries(cityDistribution).map(([city, count]) => ({
        label: city,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value).slice(0, 10),
      byIndustry: Object.entries(industryDistribution).map(([industry, count]) => ({
        label: industry,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value),
      byBusinessType: Object.entries(businessTypeDistribution).map(([type, count]) => ({
        label: type,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value),
      byCompanySize: Object.entries(companySizeDistribution).map(([size, count]) => ({
        label: size,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value)
    },
    segmentation: {
      byTier: Object.entries(tierDistribution).map(([tier, count]) => ({
        label: tier,
        value: count,
        percentage: (count / totalCustomers) * 100,
        color: getTierColor(tier)
      })).sort((a, b) => getTierOrder(a.label) - getTierOrder(b.label)),
      bySource: Object.entries(sourceDistribution).map(([source, count]) => ({
        label: source,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value),
      byStatus: Object.entries(statusDistribution).map(([status, count]) => ({
        label: status,
        value: count,
        percentage: (count / totalCustomers) * 100,
        color: getStatusColor(status)
      })),
      byPaymentTerms: Object.entries(filteredCustomers.reduce((acc, customer) => {
        const terms = customer.payment_terms || "30 Days"
        acc[terms] = (acc[terms] || 0) + 1
        return acc
      }, {} as Record<string, number>)).map(([terms, count]) => ({
        label: terms,
        value: count,
        percentage: (count / totalCustomers) * 100,
        color: getStatusColor(terms)
      })),
      byShippingMethod: Object.entries(filteredCustomers.reduce((acc, customer) => {
        const method = customer.preferred_shipping_method || "Ground"
        acc[method] = (acc[method] || 0) + 1
        return acc
      }, {} as Record<string, number>)).map(([method, count]) => ({
        label: method,
        value: count,
        percentage: (count / totalCustomers) * 100,
        color: getStatusColor(method)
      }))
    },
    financial: {
      revenueByTier,
      volumeDistribution: [
        { label: "< $100K", value: filteredCustomers.filter(c => (c.annual_volume || 0) < 100000).length },
        { label: "$100K - $500K", value: filteredCustomers.filter(c => (c.annual_volume || 0) >= 100000 && (c.annual_volume || 0) < 500000).length },
        { label: "$500K - $1M", value: filteredCustomers.filter(c => (c.annual_volume || 0) >= 500000 && (c.annual_volume || 0) < 1000000).length },
        { label: "> $1M", value: filteredCustomers.filter(c => (c.annual_volume || 0) >= 1000000).length }
      ],
      creditLimitUtilization: [],
      paymentTermsBreakdown: Object.entries(filteredCustomers.reduce((acc, customer) => {
        const terms = customer.payment_terms || "30 Days"
        acc[terms] = (acc[terms] || 0) + 1
        return acc
      }, {} as Record<string, number>)).map(([terms, count]) => ({
        label: terms,
        value: count,
        percentage: (count / totalCustomers) * 100
      })),
      currencyDistribution: Object.entries(currencyDistribution).map(([currency, count]) => ({
        label: currency,
        value: count,
        percentage: (count / totalCustomers) * 100
      })).sort((a, b) => b.value - a.value)
    },
    trends: {
      customerGrowth: customerGrowthTrend,
      revenueGrowth: revenueGrowthTrend,
      tierProgression: generateMockTrendData("tier", 6),
      acquisitionTrends: generateMockTrendData("acquisition", 12)
    },
    insights: {
      topPerformers,
      riskCustomers,
      growthOpportunities,
      recentActivity: [
        {
          type: "tier_upgrade",
          customer: "Acme Corp",
          description: "Upgraded from Gold to Platinum tier",
          date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          impact: "positive"
        },
        {
          type: "large_order",
          customer: "Tech Solutions Inc",
          description: "Placed order worth $250,000",
          date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          impact: "positive"
        },
        {
          type: "payment_delay",
          customer: "Global Industries",
          description: "Payment overdue by 15 days",
          date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          impact: "negative"
        }
      ]
    }
  }
}

// Helper functions
function getTierColor(tier: string): string {
  const colors = {
    "VIP": "#8B5CF6",
    "Platinum": "#6B7280",
    "Gold": "#F59E0B",
    "Silver": "#9CA3AF",
    "Bronze": "#CD7C2F"
  }
  return colors[tier as keyof typeof colors] || "#6B7280"
}

function getTierOrder(tier: string): number {
  const order = { "Bronze": 1, "Silver": 2, "Gold": 3, "Platinum": 4, "VIP": 5 }
  return order[tier as keyof typeof order] || 0
}

function getStatusColor(status: string): string {
  const colors = {
    "active": "#10B981",
    "inactive": "#EF4444",
    "pending": "#F59E0B",
    "suspended": "#DC2626"
  }
  return colors[status as keyof typeof colors] || "#6B7280"
}

function generateMockTrendData(type: string, months: number): TimeSeriesData[] {
  const data: TimeSeriesData[] = []
  const now = new Date()
  
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
    let value = 0
    
    switch (type) {
      case "customers":
        value = 100 + Math.floor(Math.random() * 50) + i * 5
        break
      case "revenue":
        value = 50000 + Math.floor(Math.random() * 25000) + i * 2000
        break
      case "tier":
        value = 10 + Math.floor(Math.random() * 15)
        break
      case "acquisition":
        value = 15 + Math.floor(Math.random() * 20)
        break
    }
    
    data.push({
      date: date.toISOString().split('T')[0],
      value,
      label: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
    })
  }
  
  return data
}

// Generate Customer Insights Function
export function generateCustomerInsights(customers: CustomerFormData[]) {
  const analytics = calculateCustomerAnalytics(customers)
  
  const insights = [
    {
      id: "revenue-growth",
      type: "opportunity",
      title: "Revenue Growth Opportunity",
      description: `${analytics.insights.growthOpportunities.length} customers show potential for tier upgrades`,
      priority: "high",
      impact: "high",
      confidence: 85,
      actionable: true,
      data: analytics.insights.growthOpportunities
    },
    {
      id: "customer-retention",
      type: "risk",
      title: "Customer Retention Risk",
      description: `${analytics.insights.riskCustomers.length} customers require immediate attention`,
      priority: "critical",
      impact: "high",
      confidence: 92,
      actionable: true,
      data: analytics.insights.riskCustomers
    },
    {
      id: "top-performers",
      type: "success",
      title: "Top Performing Customers",
      description: `${analytics.insights.topPerformers.length} customers driving majority of revenue`,
      priority: "medium",
      impact: "medium",
      confidence: 95,
      actionable: false,
      data: analytics.insights.topPerformers
    }
  ]

  const metrics = {
    totalInsights: insights.length,
    actionableInsights: insights.filter(i => i.actionable).length,
    highPriorityInsights: insights.filter(i => i.priority === "high" || i.priority === "critical").length,
    avgConfidence: insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length
  }

  return { insights, metrics }
}

// Main Customer Analytics Component
interface CustomerAnalyticsProps {
  customers: CustomerFormData[]
  className?: string
  onExportReport?: (analytics: CustomerAnalytics) => void
}

export function CustomerAnalytics({
  customers,
  className,
  onExportReport
}: CustomerAnalyticsProps) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [selectedTab, setSelectedTab] = useState("overview")
  const [selectedMetric, setSelectedMetric] = useState("all")

  // Calculate analytics based on current filters
  const analytics = useMemo(() => {
    return calculateCustomerAnalytics(customers, dateRange)
  }, [customers, dateRange])

  const handleExportReport = () => {
    onExportReport?.(analytics)
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpRight className="h-4 w-4 text-green-600" />
    if (change < 0) return <ArrowDownRight className="h-4 w-4 text-red-600" />
    return <Minus className="h-4 w-4 text-gray-400" />
  }

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case "positive": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "negative": return <AlertCircle className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="heading-2 text-foreground flex items-center gap-2">
            <BarChart className="h-6 w-6" />
            Customer Analytics & Insights
          </h2>
          <p className="body-small text-muted-foreground mt-1">
            Comprehensive analysis of customer data and business insights
          </p>
        </div>

        <div className="flex items-center gap-3">
          <DatePickerWithRange
            from={dateRange?.from}
            to={dateRange?.to}
            onDateRangeChange={(range) => setDateRange(range.from && range.to ? { from: range.from, to: range.to } : undefined)}
            placeholder="Select date range"
          />
          <Button variant="outline" onClick={handleExportReport} className="gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          metric={{
            label: "Total Customers",
            value: analytics.overview.totalCustomers,
            change: analytics.overview.growthRate,
            icon: Users,
            color: "primary"
          }}
        />
        <MetricCard
          metric={{
            label: "Active Customers",
            value: analytics.overview.activeCustomers,
            change: 5.2,
            icon: TrendingUp,
            color: "success"
          }}
        />
        <MetricCard
          metric={{
            label: "Total Revenue",
            value: analytics.overview.totalRevenue,
            format: "currency",
            change: 12.8,
            icon: DollarSign,
            color: "info"
          }}
        />
        <MetricCard
          metric={{
            label: "Avg Customer Value",
            value: analytics.overview.avgCustomerValue,
            format: "currency",
            change: 8.5,
            icon: Star,
            color: "warning"
          }}
        />
      </div>

      {/* Analytics Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="demographics">Demographics</TabsTrigger>
          <TabsTrigger value="segmentation">Segmentation</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Growth Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Customer Growth Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <TrendChart
                  data={analytics.trends.customerGrowth}
                  title="Monthly Customer Growth"
                  height={200}
                />
              </CardContent>
            </Card>

            {/* Revenue Growth Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Revenue Growth Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <TrendChart
                  data={analytics.trends.revenueGrowth}
                  title="Monthly Revenue Growth"
                  height={200}
                />
              </CardContent>
            </Card>

            {/* Customer Tier Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Customer Tier Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimplePieChart
                  data={analytics.segmentation.byTier}
                  size={150}
                  showLegend={true}
                />
              </CardContent>
            </Card>

            {/* Top Countries */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Top Countries
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.demographics.byCountry.slice(0, 5)}
                  height={200}
                  showValues={true}
                />
              </CardContent>
            </Card>
          </div>

          {/* Performance Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Customer Retention</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Retention Rate</span>
                    <span className="font-medium">{analytics.overview.retentionRate}%</span>
                  </div>
                  <Progress value={analytics.overview.retentionRate} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    {analytics.overview.churnedCustomers} customers churned this period
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">New Customer Acquisition</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-green-600">
                    {analytics.overview.newCustomers}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    {getChangeIcon(analytics.overview.growthRate)}
                    <span className={cn(
                      analytics.overview.growthRate > 0 ? "text-green-600" : "text-red-600"
                    )}>
                      {Math.abs(analytics.overview.growthRate)}% vs last period
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Revenue per Customer</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">
                    ${analytics.overview.avgCustomerValue.toLocaleString()}
                  </div>
                  <div className="flex items-center gap-1 text-sm">
                    {getChangeIcon(8.5)}
                    <span className="text-green-600">8.5% vs last period</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Demographics Tab */}
        <TabsContent value="demographics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Geographic Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="country">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="country">By Country</TabsTrigger>
                    <TabsTrigger value="city">By City</TabsTrigger>
                  </TabsList>
                  <TabsContent value="country">
                    <SimpleBarChart
                      data={analytics.demographics.byCountry}
                      height={250}
                      showValues={true}
                    />
                  </TabsContent>
                  <TabsContent value="city">
                    <SimpleBarChart
                      data={analytics.demographics.byCity}
                      height={250}
                      showValues={true}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Industry Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Industry Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimplePieChart
                  data={analytics.demographics.byIndustry}
                  size={180}
                  showLegend={true}
                />
              </CardContent>
            </Card>

            {/* Business Type Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Business Type Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.demographics.byBusinessType}
                  height={200}
                  showValues={true}
                />
              </CardContent>
            </Card>

            {/* Company Size Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Company Size Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimplePieChart
                  data={analytics.demographics.byCompanySize}
                  size={150}
                  showLegend={true}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Segmentation Tab */}
        <TabsContent value="segmentation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Customer Tier Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="h-5 w-5" />
                  Customer Tier Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.segmentation.byTier.map((tier, index) => (
                    <div key={tier.label} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: tier.color }}
                          />
                          <span className="font-medium">{tier.label}</span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {tier.value} customers ({tier.percentage?.toFixed(1)}%)
                        </div>
                      </div>
                      <Progress value={tier.percentage} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Customer Source Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Customer Source Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.segmentation.bySource}
                  height={250}
                  showValues={true}
                />
              </CardContent>
            </Card>

            {/* Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Customer Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimplePieChart
                  data={analytics.segmentation.byStatus}
                  size={150}
                  showLegend={true}
                />
              </CardContent>
            </Card>

            {/* Payment Terms Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Payment Terms Analysis
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.financial.paymentTermsBreakdown}
                  height={200}
                  showValues={true}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue by Tier */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Revenue by Customer Tier
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.financial.revenueByTier}
                  height={250}
                  showValues={true}
                />
              </CardContent>
            </Card>

            {/* Volume Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart className="h-5 w-5" />
                  Annual Volume Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimplePieChart
                  data={analytics.financial.volumeDistribution}
                  size={180}
                  showLegend={true}
                />
              </CardContent>
            </Card>

            {/* Currency Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Currency Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <SimpleBarChart
                  data={analytics.financial.currencyDistribution}
                  height={200}
                  showValues={true}
                />
              </CardContent>
            </Card>

            {/* Financial Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Financial Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Total Revenue</span>
                    <span className="font-medium">
                      ${analytics.overview.totalRevenue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Average Customer Value</span>
                    <span className="font-medium">
                      ${analytics.overview.avgCustomerValue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Revenue Growth</span>
                    <span className="font-medium text-green-600">+12.8%</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Projected Annual Revenue</span>
                    <span className="text-primary">
                      ${(analytics.overview.totalRevenue * 1.128).toLocaleString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Top Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.insights.topPerformers.map((customer, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <div>
                        <p className="font-medium text-sm">{customer.company}</p>
                        <p className="text-xs text-muted-foreground">{customer.contact_person}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          ${(customer.annual_volume || 0).toLocaleString()}
                        </p>
                        <Badge variant="secondary" className="text-xs">
                          {customer.customer_tier}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Risk Customers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Risk Customers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.insights.riskCustomers.map((customer, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded border-red-200">
                      <div>
                        <p className="font-medium text-sm">{customer.company}</p>
                        <p className="text-xs text-muted-foreground">{customer.contact_person}</p>
                      </div>
                      <div className="text-right">
                        <Badge variant="destructive" className="text-xs">
                          {customer.status || "At Risk"}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Growth Opportunities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Growth Opportunities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.insights.growthOpportunities.map((customer, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded border-green-200">
                      <div>
                        <p className="font-medium text-sm">{customer.company}</p>
                        <p className="text-xs text-muted-foreground">{customer.contact_person}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          ${(customer.annual_volume || 0).toLocaleString()}
                        </p>
                        <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                          Upgrade Potential
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Customer Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analytics.insights.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 border rounded">
                    {getImpactIcon(activity.impact)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">{activity.customer}</p>
                        <span className="text-xs text-muted-foreground">
                          {new Date(activity.date).toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">{activity.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
