/**
 * VERIFY ROLE IMPLEMENTATION
 * 
 * This script verifies that all role-based access control
 * implementation files are in place and contain the required code.
 */

const fs = require('fs');
const path = require('path');

function verifyRoleImplementation() {
  console.log('🚀 Verifying Role Implementation Files...\n');

  let allChecksPass = true;

  try {
    // =====================================================
    // STEP 1: CHECK ROLE HOOK
    // =====================================================
    console.log('📋 Step 1: Checking role hook implementation...');

    const roleHookPath = path.join(__dirname, 'crmnew-main/hooks/use-role.ts');
    if (fs.existsSync(roleHookPath)) {
      console.log('✅ Role hook file exists');
      
      const content = fs.readFileSync(roleHookPath, 'utf8');
      
      const requiredFunctions = [
        'useRole',
        'usePermissions', 
        'useRoleBasedUI',
        'isAdmin',
        'hasRole',
        'canViewAllData',
        'canEditCustomers',
        'canDeleteCustomers'
      ];

      requiredFunctions.forEach(func => {
        if (content.includes(func)) {
          console.log(`  ✅ ${func} function implemented`);
        } else {
          console.log(`  ❌ ${func} function missing`);
          allChecksPass = false;
        }
      });
    } else {
      console.log('❌ Role hook file not found');
      allChecksPass = false;
    }

    // =====================================================
    // STEP 2: CHECK AUTH PROVIDER ENHANCEMENT
    // =====================================================
    console.log('\n📋 Step 2: Checking auth provider enhancement...');

    const authProviderPath = path.join(__dirname, 'crmnew-main/components/auth-provider.tsx');
    if (fs.existsSync(authProviderPath)) {
      console.log('✅ Auth provider file exists');
      
      const content = fs.readFileSync(authProviderPath, 'utf8');
      
      const requiredFeatures = [
        'UserWithRole',
        'enhanceUserWithRole',
        'fetchUserRole',
        'isAdmin',
        'hasRole'
      ];

      requiredFeatures.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`  ✅ ${feature} implemented`);
        } else {
          console.log(`  ❌ ${feature} missing`);
          allChecksPass = false;
        }
      });
    } else {
      console.log('❌ Auth provider file not found');
      allChecksPass = false;
    }

    // =====================================================
    // STEP 3: CHECK SIDEBAR ROLE INTEGRATION
    // =====================================================
    console.log('\n📋 Step 3: Checking sidebar role integration...');

    const sidebarPath = path.join(__dirname, 'crmnew-main/components/app-sidebar.tsx');
    if (fs.existsSync(sidebarPath)) {
      console.log('✅ Sidebar file exists');
      
      const content = fs.readFileSync(sidebarPath, 'utf8');
      
      const requiredFeatures = [
        'useRole',
        'getNavigationData',
        'Administration',
        'User Management',
        'System Reports'
      ];

      requiredFeatures.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`  ✅ ${feature} implemented`);
        } else {
          console.log(`  ❌ ${feature} missing`);
          allChecksPass = false;
        }
      });
    } else {
      console.log('❌ Sidebar file not found');
      allChecksPass = false;
    }

    // =====================================================
    // STEP 4: CHECK CUSTOMERS PAGE ROLE INTEGRATION
    // =====================================================
    console.log('\n📋 Step 4: Checking customers page role integration...');

    const customersPagePath = path.join(__dirname, 'crmnew-main/app/dashboard/customers/page.tsx');
    if (fs.existsSync(customersPagePath)) {
      console.log('✅ Customers page file exists');
      
      const content = fs.readFileSync(customersPagePath, 'utf8');
      
      const requiredFeatures = [
        'useRole',
        'isAdmin',
        'canViewAllData',
        'Admin View',
        'system-wide'
      ];

      requiredFeatures.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`  ✅ ${feature} implemented`);
        } else {
          console.log(`  ❌ ${feature} missing`);
          allChecksPass = false;
        }
      });
    } else {
      console.log('❌ Customers page file not found');
      allChecksPass = false;
    }

    // =====================================================
    // STEP 5: CHECK DEALS PAGE ROLE INTEGRATION
    // =====================================================
    console.log('\n📋 Step 5: Checking deals page role integration...');

    const dealsPagePath = path.join(__dirname, 'crmnew-main/app/dashboard/deals/page.tsx');
    if (fs.existsSync(dealsPagePath)) {
      console.log('✅ Deals page file exists');
      
      const content = fs.readFileSync(dealsPagePath, 'utf8');
      
      const requiredFeatures = [
        'useRole',
        'isAdmin',
        'canEditDeals',
        'canDeleteDeals'
      ];

      requiredFeatures.forEach(feature => {
        if (content.includes(feature)) {
          console.log(`  ✅ ${feature} implemented`);
        } else {
          console.log(`  ❌ ${feature} missing`);
          allChecksPass = false;
        }
      });
    } else {
      console.log('❌ Deals page file not found');
      allChecksPass = false;
    }

    // =====================================================
    // STEP 6: CHECK DATABASE POLICIES
    // =====================================================
    console.log('\n📋 Step 6: Checking database policy files...');

    const policyFiles = [
      'admin-role-policies.sql',
      'apply-admin-policies.js',
      'test-admin-access.js'
    ];

    policyFiles.forEach(file => {
      const filePath = path.join(__dirname, file);
      if (fs.existsSync(filePath)) {
        console.log(`  ✅ ${file} exists`);
      } else {
        console.log(`  ❌ ${file} missing`);
        allChecksPass = false;
      }
    });

    // =====================================================
    // STEP 7: SUMMARY
    // =====================================================
    console.log('\n🎉 Role Implementation Verification Summary:');
    console.log('============================================');
    
    if (allChecksPass) {
      console.log('✅ All role implementation checks passed!');
      console.log('✅ Database policies are in place');
      console.log('✅ Auth provider enhanced with role fetching');
      console.log('✅ Role-based hooks implemented');
      console.log('✅ UI components updated with conditional rendering');
      console.log('✅ Navigation enhanced with admin sections');
      console.log('\n🚀 Frontend role integration is COMPLETE and ready for testing!');
      
      console.log('\n📋 Next Steps:');
      console.log('1. Start the development server: npm run dev');
      console.log('2. Login as admin (<EMAIL>) to see admin features');
      console.log('3. Login as regular user to see restricted view');
      console.log('4. Verify admin can see all data, regular users see only their own');
      console.log('5. Check that admin navigation items appear only for admin users');
      
    } else {
      console.log('❌ Some role implementation checks failed');
      console.log('⚠️ Please review the missing components above');
    }

    return allChecksPass;

  } catch (error) {
    console.error('❌ Verification failed with error:', error);
    return false;
  }
}

// Test role-based logic
function testRoleLogic() {
  console.log('\n🧪 Testing Role Logic...\n');

  // Simulate role checking functions
  const testCases = [
    {
      name: 'Admin user permissions',
      userRole: 'admin',
      tests: [
        { permission: 'viewAllData', expected: true },
        { permission: 'manageUsers', expected: true },
        { permission: 'deleteRecords', expected: true },
        { permission: 'editAnyRecord', expected: true }
      ]
    },
    {
      name: 'Manager user permissions',
      userRole: 'manager',
      tests: [
        { permission: 'viewAllData', expected: false },
        { permission: 'manageUsers', expected: false },
        { permission: 'deleteRecords', expected: false },
        { permission: 'editTeamRecords', expected: true }
      ]
    },
    {
      name: 'Regular user permissions',
      userRole: 'user',
      tests: [
        { permission: 'viewAllData', expected: false },
        { permission: 'manageUsers', expected: false },
        { permission: 'deleteRecords', expected: false },
        { permission: 'editOwnRecords', expected: true }
      ]
    }
  ];

  // Mock permission functions
  const checkPermission = (userRole, permission, context = {}) => {
    switch (permission) {
      case 'viewAllData':
      case 'manageUsers':
      case 'deleteRecords':
      case 'editAnyRecord':
        return userRole === 'admin';
      
      case 'editTeamRecords':
        return userRole === 'admin' || userRole === 'manager';
      
      case 'editOwnRecords':
        return true; // All authenticated users can edit their own records
      
      default:
        return false;
    }
  };

  testCases.forEach(testCase => {
    console.log(`📋 Testing ${testCase.name}:`);
    
    testCase.tests.forEach(test => {
      const result = checkPermission(testCase.userRole, test.permission);
      const status = result === test.expected ? '✅' : '❌';
      console.log(`  ${status} ${test.permission}: ${result} (expected: ${test.expected})`);
    });
    
    console.log('');
  });

  console.log('🎉 Role logic testing complete!');
}

// Run the verification
if (require.main === module) {
  const success = verifyRoleImplementation();
  testRoleLogic();
  
  if (success) {
    console.log('\n🎯 PHASE 2: FRONTEND ROLE INTEGRATION - COMPLETE! ✅');
  } else {
    console.log('\n⚠️ PHASE 2: Some issues found, please review above');
  }
}

module.exports = { verifyRoleImplementation, testRoleLogic };
