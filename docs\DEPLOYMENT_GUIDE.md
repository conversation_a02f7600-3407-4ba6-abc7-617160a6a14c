# Nawras CRM - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Nawras CRM application to production environments. The application is designed for deployment on Vercel with Supabase as the backend service.

## Deployment Architecture

### Production Stack
- **Frontend**: Next.js 14 deployed on Vercel
- **Database**: Supabase PostgreSQL (hosted)
- **Authentication**: Supabase Auth
- **File Storage**: Supabase Storage
- **CDN**: Vercel Edge Network
- **Domain**: Custom domain with SSL

### Environment Overview
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Users/Clients │────│  Vercel (CDN)   │────│   Next.js App   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                              ┌─────────────────┐
                                              │   Supabase      │
                                              │  - PostgreSQL   │
                                              │  - Auth         │
                                              │  - Storage      │
                                              └─────────────────┘
```

## Pre-Deployment Checklist

### 1. Code Quality Verification
```bash
# Run all tests
npm test
npm run test:e2e

# Check TypeScript compilation
npx tsc --noEmit

# Lint code
npm run lint

# Build application
npm run build

# Verify build output
npm start
```

### 2. Environment Configuration
Ensure all environment variables are properly configured:

```env
# Production Environment Variables
NEXT_PUBLIC_SUPABASE_URL=https://ojhtdwrzolfwbiwrprok.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
NEXT_PUBLIC_APP_URL=https://sales.nawrasinchina.com
NODE_ENV=production

# Optional: Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
SENTRY_DSN=your_sentry_dsn
```

### 3. Database Preparation
```sql
-- Verify all migrations are applied
SELECT * FROM supabase_migrations.schema_migrations;

-- Check RLS policies are enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = false;

-- Verify user roles and permissions
SELECT * FROM user_profiles WHERE role = 'admin';
```

### 4. Performance Optimization
```bash
# Optimize images
npm run optimize-images

# Analyze bundle size
npm run analyze

# Check for unused dependencies
npm run depcheck
```

## Vercel Deployment

### 1. Initial Setup
```bash
# Install Vercel CLI
npm i -g vercel

# Login to Vercel
vercel login

# Link project to Vercel
vercel link
```

### 2. Configure Vercel Project
Create `vercel.json` configuration:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "regions": ["iad1"],
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "https://sales.nawrasinchina.com"
        },
        {
          "key": "Access-Control-Allow-Methods",
          "value": "GET, POST, PUT, DELETE, OPTIONS"
        },
        {
          "key": "Access-Control-Allow-Headers",
          "value": "Content-Type, Authorization"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/",
      "destination": "/dashboard",
      "permanent": false
    }
  ],
  "rewrites": [
    {
      "source": "/api/:path*",
      "destination": "/api/:path*"
    }
  ]
}
```

### 3. Environment Variables Setup
Configure environment variables in Vercel dashboard:

```bash
# Set environment variables via CLI
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add NEXT_PUBLIC_APP_URL production

# Or use Vercel dashboard
# https://vercel.com/dashboard/project-name/settings/environment-variables
```

### 4. Deploy to Production
```bash
# Deploy to production
vercel --prod

# Or push to main branch for automatic deployment
git push origin main
```

## Custom Domain Configuration

### 1. Domain Setup
```bash
# Add custom domain via Vercel CLI
vercel domains add sales.nawrasinchina.com

# Or configure in Vercel dashboard
# Project Settings > Domains > Add Domain
```

### 2. DNS Configuration
Configure DNS records with your domain provider:

```
Type: CNAME
Name: sales (or @)
Value: cname.vercel-dns.com
TTL: 300
```

### 3. SSL Certificate
Vercel automatically provisions SSL certificates for custom domains.

## Database Migration and Setup

### 1. Production Database Setup
```sql
-- Create production database schema
-- Run all migration files in order

-- Set up RLS policies
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE deals ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;
-- ... enable for all tables

-- Create admin user
INSERT INTO user_profiles (id, full_name, role)
VALUES ('admin-user-id', 'Admin User', 'admin');
```

### 2. Data Migration (if applicable)
```bash
# Export data from staging
npx supabase db dump --data-only > staging_data.sql

# Import to production (be careful!)
psql "postgresql://postgres:[password]@db.ojhtdwrzolfwbiwrprok.supabase.co:5432/postgres" < staging_data.sql
```

### 3. Database Backup Setup
```sql
-- Configure automated backups in Supabase dashboard
-- Set backup retention period
-- Configure point-in-time recovery
```

## Monitoring and Logging

### 1. Application Monitoring
```typescript
// lib/monitoring.ts
import { init } from '@sentry/nextjs'

init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
})

// Add performance monitoring
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    })
  }
}
```

### 2. Error Tracking
```typescript
// lib/error-tracking.ts
export function logError(error: Error, context?: any) {
  console.error('Application Error:', error, context)
  
  // Send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Sentry, LogRocket, etc.
  }
}
```

### 3. Performance Monitoring
```typescript
// lib/performance.ts
export function measurePerformance(name: string, fn: () => void) {
  const start = performance.now()
  fn()
  const end = performance.now()
  
  console.log(`${name} took ${end - start} milliseconds`)
  
  // Send metrics to monitoring service
}
```

## Security Configuration

### 1. Content Security Policy
```typescript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app;
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https:;
      font-src 'self';
      connect-src 'self' *.supabase.co;
    `.replace(/\s{2,}/g, ' ').trim()
  }
]

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ]
  },
}
```

### 2. Rate Limiting
```typescript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit'
import { Redis } from '@upstash/redis'

const ratelimit = new Ratelimit({
  redis: Redis.fromEnv(),
  limiter: Ratelimit.slidingWindow(10, '10 s'),
})

export async function checkRateLimit(identifier: string) {
  const { success } = await ratelimit.limit(identifier)
  return success
}
```

### 3. Authentication Security
```typescript
// middleware.ts
import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'
import { NextResponse } from 'next/server'

export async function middleware(req: NextRequest) {
  const res = NextResponse.next()
  const supabase = createMiddlewareClient({ req, res })
  
  const { data: { session } } = await supabase.auth.getSession()
  
  // Protect dashboard routes
  if (req.nextUrl.pathname.startsWith('/dashboard') && !session) {
    return NextResponse.redirect(new URL('/login', req.url))
  }
  
  return res
}
```

## Performance Optimization

### 1. Caching Strategy
```typescript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, s-maxage=60, stale-while-revalidate=300'
          }
        ]
      }
    ]
  }
}
```

### 2. Image Optimization
```typescript
// next.config.js
module.exports = {
  images: {
    domains: ['ojhtdwrzolfwbiwrprok.supabase.co'],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  }
}
```

### 3. Bundle Optimization
```typescript
// next.config.js
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer({
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons']
  }
})
```

## Rollback Strategy

### 1. Vercel Rollback
```bash
# List deployments
vercel ls

# Rollback to previous deployment
vercel rollback [deployment-url]

# Or use Vercel dashboard
# Project > Deployments > Previous Deployment > Promote to Production
```

### 2. Database Rollback
```sql
-- Point-in-time recovery (if needed)
-- Contact Supabase support for major rollbacks

-- Manual rollback for schema changes
-- Keep migration rollback scripts ready
```

### 3. Emergency Procedures
```bash
# Emergency maintenance mode
# Create maintenance.html and deploy

# Quick hotfix deployment
git checkout main
git cherry-pick [hotfix-commit]
git push origin main
```

## Post-Deployment Verification

### 1. Smoke Tests
```bash
# Run critical path tests
npm run test:smoke

# Check all major features
curl https://sales.nawrasinchina.com/api/health
curl https://sales.nawrasinchina.com/api/customers
```

### 2. Performance Verification
```bash
# Check Core Web Vitals
npx lighthouse https://sales.nawrasinchina.com --view

# Monitor response times
curl -w "@curl-format.txt" -o /dev/null -s https://sales.nawrasinchina.com
```

### 3. Security Verification
```bash
# SSL certificate check
openssl s_client -connect sales.nawrasinchina.com:443

# Security headers check
curl -I https://sales.nawrasinchina.com
```

## Maintenance and Updates

### 1. Regular Updates
```bash
# Weekly dependency updates
npm update
npm audit fix

# Monthly security updates
npm audit
npm run test:security
```

### 2. Database Maintenance
```sql
-- Monthly database maintenance
VACUUM ANALYZE;
REINDEX DATABASE postgres;

-- Check database performance
SELECT * FROM pg_stat_activity;
```

### 3. Monitoring Dashboard
Set up monitoring dashboards for:
- Application performance metrics
- Error rates and types
- User activity and engagement
- Database performance
- Infrastructure costs

## Troubleshooting

### Common Deployment Issues
1. **Build Failures**: Check TypeScript errors and dependencies
2. **Environment Variables**: Verify all required variables are set
3. **Database Connection**: Check Supabase connection and RLS policies
4. **Performance Issues**: Monitor bundle size and API response times
5. **Authentication Problems**: Verify Supabase configuration and middleware

### Support Contacts
- **Vercel Support**: https://vercel.com/support
- **Supabase Support**: https://supabase.com/support
- **Development Team**: [Team contact information]

This deployment guide ensures a smooth and secure deployment process for the Nawras CRM application with proper monitoring, security, and rollback procedures in place.
