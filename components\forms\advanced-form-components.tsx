"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { FormField, type FormFieldProps } from "./form-field"
import { 
  ChevronLeft, ChevronRight, CheckCircle, AlertCircle, 
  Upload, X, FileText, Image, Video, File
} from "lucide-react"
import { cn } from "@/lib/utils"

// Multi-Step Form Types
export interface FormStep {
  id: string
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  required?: boolean
  validation?: (data: any) => boolean
  fields?: string[]
}

export interface MultiStepFormProps {
  steps: FormStep[]
  currentStep: number
  onStepChange: (step: number) => void
  onNext?: () => void
  onPrevious?: () => void
  onSubmit?: () => void
  children: React.ReactNode
  showProgress?: boolean
  allowSkip?: boolean
  className?: string
}

// Conditional Field Types
export interface ConditionalFieldProps extends FormFieldProps {
  condition: (data: any) => boolean
  formData: any
  fallback?: React.ReactNode
}

// File Upload Types
export interface FileUploadProps {
  id: string
  label?: string
  description?: string
  accept?: string
  multiple?: boolean
  maxSize?: number // in MB
  maxFiles?: number
  value?: File[]
  onChange?: (files: File[]) => void
  onError?: (error: string) => void
  disabled?: boolean
  className?: string
  variant?: "dropzone" | "button" | "inline"
  showPreview?: boolean
}

// Multi-Step Form Component
export function MultiStepForm({
  steps,
  currentStep,
  onStepChange,
  onNext,
  onPrevious,
  onSubmit,
  children,
  showProgress = true,
  allowSkip = false,
  className
}: MultiStepFormProps) {
  const currentStepData = steps[currentStep]
  const isFirstStep = currentStep === 0
  const isLastStep = currentStep === steps.length - 1
  const progress = ((currentStep + 1) / steps.length) * 100

  return (
    <div className={cn("space-y-6", className)}>
      {/* Step Progress */}
      {showProgress && (
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">
              Step {currentStep + 1} of {steps.length}
            </span>
            <span className="font-medium">{Math.round(progress)}% Complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}

      {/* Step Navigation */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4 overflow-x-auto">
          {steps.map((step, index) => {
            const isActive = index === currentStep
            const isCompleted = index < currentStep
            const isAccessible = index <= currentStep || allowSkip
            const Icon = step.icon

            return (
              <button
                key={step.id}
                onClick={() => isAccessible && onStepChange(index)}
                disabled={!isAccessible}
                className={cn(
                  "flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors",
                  "min-w-0 flex-shrink-0",
                  isActive && "bg-primary text-primary-foreground",
                  isCompleted && !isActive && "bg-success/10 text-success",
                  !isActive && !isCompleted && "text-muted-foreground hover:bg-accent",
                  !isAccessible && "opacity-50 cursor-not-allowed"
                )}
              >
                {Icon && (
                  <Icon className="h-4 w-4 flex-shrink-0" />
                )}
                {isCompleted && !isActive && (
                  <CheckCircle className="h-4 w-4 flex-shrink-0" />
                )}
                <span className="text-sm font-medium truncate">{step.title}</span>
                {step.required && (
                  <Badge variant="outline" className="text-xs">Required</Badge>
                )}
              </button>
            )
          })}
        </div>
      </div>

      {/* Current Step Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {currentStepData.icon && (
              <currentStepData.icon className="h-5 w-5" />
            )}
            {currentStepData.title}
          </CardTitle>
          {currentStepData.description && (
            <p className="text-sm text-muted-foreground">
              {currentStepData.description}
            </p>
          )}
        </CardHeader>
        <CardContent>
          {children}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <div className="flex items-center justify-between pt-4 border-t">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isFirstStep}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>

        <div className="flex items-center gap-2">
          {allowSkip && !isLastStep && (
            <Button
              variant="ghost"
              onClick={onNext}
              className="text-muted-foreground"
            >
              Skip
            </Button>
          )}
          
          {isLastStep ? (
            <Button onClick={onSubmit} className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Complete
            </Button>
          ) : (
            <Button onClick={onNext} className="flex items-center gap-2">
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// Conditional Field Component
export function ConditionalField({
  condition,
  formData,
  fallback = null,
  ...fieldProps
}: ConditionalFieldProps) {
  const shouldShow = condition(formData)

  if (!shouldShow) {
    return fallback as React.ReactElement
  }

  return <FormField {...fieldProps} />
}

// File Upload Component
export function FileUpload({
  id,
  label,
  description,
  accept = "*/*",
  multiple = false,
  maxSize = 10, // 10MB default
  maxFiles = 5,
  value = [],
  onChange,
  onError,
  disabled = false,
  className,
  variant = "dropzone",
  showPreview = true
}: FileUploadProps) {
  const [dragActive, setDragActive] = React.useState(false)
  const [files, setFiles] = React.useState<File[]>(value)
  const inputRef = React.useRef<HTMLInputElement>(null)

  const handleFiles = (newFiles: FileList | null) => {
    if (!newFiles) return

    const fileArray = Array.from(newFiles)
    const validFiles: File[] = []
    
    for (const file of fileArray) {
      // Check file size
      if (file.size > maxSize * 1024 * 1024) {
        onError?.(`File "${file.name}" is too large. Maximum size is ${maxSize}MB.`)
        continue
      }

      // Check file count
      if (!multiple && validFiles.length >= 1) {
        onError?.("Only one file is allowed.")
        break
      }

      if (multiple && files.length + validFiles.length >= maxFiles) {
        onError?.(`Maximum ${maxFiles} files allowed.`)
        break
      }

      validFiles.push(file)
    }

    const updatedFiles = multiple ? [...files, ...validFiles] : validFiles
    setFiles(updatedFiles)
    onChange?.(updatedFiles)
  }

  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index)
    setFiles(updatedFiles)
    onChange?.(updatedFiles)
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return
    
    handleFiles(e.dataTransfer.files)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files)
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) return Image
    if (file.type.startsWith("video/")) return Video
    if (file.type.includes("pdf") || file.type.includes("document")) return FileText
    return File
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  if (variant === "button") {
    return (
      <div className={cn("space-y-2", className)}>
        {label && (
          <label className="text-sm font-medium">{label}</label>
        )}
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        
        <Button
          type="button"
          variant="outline"
          onClick={() => inputRef.current?.click()}
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <Upload className="h-4 w-4" />
          Choose Files
        </Button>
        
        <input
          ref={inputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
        
        {showPreview && files.length > 0 && (
          <div className="space-y-2">
            {files.map((file, index) => {
              const Icon = getFileIcon(file)
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 border rounded-lg"
                >
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">{file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )
            })}
          </div>
        )}
      </div>
    )
  }

  // Dropzone variant
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="text-sm font-medium">{label}</label>
      )}
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          dragActive && "border-primary bg-primary/5",
          !dragActive && "border-muted-foreground/25 hover:border-muted-foreground/50",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && inputRef.current?.click()}
      >
        <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
        <p className="text-sm font-medium">
          Drop files here or click to browse
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          {accept !== "*/*" && `Accepted formats: ${accept}`}
          {maxSize && ` • Max size: ${maxSize}MB`}
          {multiple && ` • Max files: ${maxFiles}`}
        </p>
        
        <input
          ref={inputRef}
          type="file"
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />
      </div>
      
      {showPreview && files.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {files.map((file, index) => {
            const Icon = getFileIcon(file)
            return (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg bg-card"
              >
                <div className="flex items-center gap-2 min-w-0">
                  <Icon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <div className="min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    removeFile(index)
                  }}
                  className="flex-shrink-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Field Array Component for dynamic fields
export interface FieldArrayProps {
  label?: string
  description?: string
  children: (index: number, remove: () => void) => React.ReactNode
  value: any[]
  onChange: (value: any[]) => void
  addLabel?: string
  minItems?: number
  maxItems?: number
  className?: string
}

export function FieldArray({
  label,
  description,
  children,
  value,
  onChange,
  addLabel = "Add Item",
  minItems = 0,
  maxItems = 10,
  className
}: FieldArrayProps) {
  const addItem = () => {
    if (value.length < maxItems) {
      onChange([...value, {}])
    }
  }

  const removeItem = (index: number) => {
    if (value.length > minItems) {
      onChange(value.filter((_, i) => i !== index))
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {label && (
        <div>
          <label className="text-sm font-medium">{label}</label>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      )}
      
      <div className="space-y-3">
        {value.map((_, index) => (
          <div key={index} className="relative">
            {children(index, () => removeItem(index))}
          </div>
        ))}
      </div>
      
      {value.length < maxItems && (
        <Button
          type="button"
          variant="outline"
          onClick={addItem}
          className="w-full"
        >
          {addLabel}
        </Button>
      )}
    </div>
  )
}
