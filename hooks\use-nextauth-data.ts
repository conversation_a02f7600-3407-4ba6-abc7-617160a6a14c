"use client"

import { useState, useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"
import { useToast } from "@/hooks/use-toast"

interface UseNextAuthDataOptions {
  table: string
  select?: string
  orderBy?: string
  orderAsc?: boolean
  limit?: number
  enableCache?: boolean
  cacheTTL?: number
}

interface UseNextAuthDataReturn<T> {
  data: T[]
  loading: boolean
  error: string | null
  refetch: () => void
  create: (data: Partial<T>) => Promise<T | null>
  update: (id: string, data: Partial<T>) => Promise<T | null>
  remove: (id: string) => Promise<boolean>
}

// Simple cache implementation
const cache = new Map<string, { data: any[], timestamp: number, ttl: number }>()

export function useNextAuthData<T = any>({
  table,
  select = '*',
  orderBy,
  orderAsc = false,
  limit,
  enableCache = true,
  cacheTTL = 5 * 60 * 1000 // 5 minutes default
}: UseNextAuthDataOptions): UseNextAuthDataReturn<T> {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { data: session } = useSession()
  const { toast } = useToast()
  const user = session?.user

  const fetchData = useCallback(async () => {
    if (!user) {
      setData([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Check cache first
      const cacheKey = `${table}-${select}-${orderBy}-${orderAsc}-${limit}`
      if (enableCache) {
        const cached = cache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
          console.log(`⚡ [NEXTAUTH-DATA] Cache hit for ${table}`)
          setData(cached.data)
          setLoading(false)
          return
        }
      }

      console.log(`🔄 [NEXTAUTH-DATA] Fetching ${table} data via API...`)

      // Build query parameters
      const params = new URLSearchParams()
      if (select !== '*') params.append('select', select)
      if (orderBy) params.append('orderBy', orderBy)
      if (orderAsc !== false) params.append('orderAsc', orderAsc.toString())
      if (limit) params.append('limit', limit.toString())

      const response = await fetch(`/api/data/${table}?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      console.log(`✅ [NEXTAUTH-DATA] Successfully fetched ${result.data.length} records from ${table}`)

      // Cache the result
      if (enableCache) {
        cache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now(),
          ttl: cacheTTL
        })
      }

      setData(result.data)
    } catch (error: any) {
      console.error(`❌ [NEXTAUTH-DATA] Error fetching ${table}:`, error)
      setError(error.message)
      setData([])
    } finally {
      setLoading(false)
    }
  }, [user, table, select, orderBy, orderAsc, limit, enableCache, cacheTTL])

  const create = useCallback(async (newData: Partial<T>): Promise<T | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create records",
        variant: "destructive",
      })
      return null
    }

    try {
      console.log(`🔄 [NEXTAUTH-DATA] Creating record in ${table}...`)

      const response = await fetch(`/api/data/${table}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      console.log(`✅ [NEXTAUTH-DATA] Successfully created record in ${table}`)

      // Clear cache and refetch data
      cache.clear()
      await fetchData()

      return result.data
    } catch (error: any) {
      console.error(`❌ [NEXTAUTH-DATA] Error creating ${table}:`, error)
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
      return null
    }
  }, [user, table, fetchData, toast])

  const update = useCallback(async (id: string, updateData: Partial<T>): Promise<T | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to update records",
        variant: "destructive",
      })
      return null
    }

    try {
      console.log(`🔄 [NEXTAUTH-DATA] Updating record in ${table}...`)

      const response = await fetch(`/api/data/${table}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const result = await response.json()
      console.log(`✅ [NEXTAUTH-DATA] Successfully updated record in ${table}`)

      // Clear cache and refetch data
      cache.clear()
      await fetchData()

      return result.data
    } catch (error: any) {
      console.error(`❌ [NEXTAUTH-DATA] Error updating ${table}:`, error)
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
      return null
    }
  }, [user, table, fetchData, toast])

  const remove = useCallback(async (id: string): Promise<boolean> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to delete records",
        variant: "destructive",
      })
      return false
    }

    try {
      console.log(`🔄 [NEXTAUTH-DATA] Deleting record from ${table}...`)

      const response = await fetch(`/api/data/${table}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      console.log(`✅ [NEXTAUTH-DATA] Successfully deleted record from ${table}`)

      // Clear cache and refetch data
      cache.clear()
      await fetchData()

      return true
    } catch (error: any) {
      console.error(`❌ [NEXTAUTH-DATA] Error deleting ${table}:`, error)
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      })
      return false
    }
  }, [user, table, fetchData, toast])

  const refetch = useCallback(() => {
    cache.clear()
    fetchData()
  }, [fetchData])

  useEffect(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch,
    create,
    update,
    remove
  }
}
