"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Smartphone, Tablet, Monitor, CheckCircle, AlertCircle, 
  XCircle, Eye, Settings, Zap, Accessibility
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

// Audit Types
export interface ResponsivenessIssue {
  id: string
  component: string
  severity: "critical" | "high" | "medium" | "low"
  category: "layout" | "navigation" | "interaction" | "content" | "performance"
  description: string
  recommendation: string
  breakpoint?: "mobile" | "tablet" | "desktop"
  status: "open" | "in-progress" | "resolved"
}

export interface ResponsivenessScore {
  overall: number
  mobile: number
  tablet: number
  desktop: number
  accessibility: number
  performance: number
}

export interface AuditResults {
  score: ResponsivenessScore
  issues: Re<PERSON>onsivenessIssue[]
  testedComponents: string[]
  lastAuditDate: Date
}

// Mock audit data - in real implementation, this would come from automated testing
const mockAuditResults: AuditResults = {
  score: {
    overall: 87,
    mobile: 82,
    tablet: 89,
    desktop: 95,
    accessibility: 91,
    performance: 84
  },
  issues: [
    {
      id: "1",
      component: "EnhancedDataTable",
      severity: "high",
      category: "layout",
      description: "Table horizontal scrolling not optimized for mobile devices",
      recommendation: "Implement card layout for mobile view and improve horizontal scroll indicators",
      breakpoint: "mobile",
      status: "open"
    },
    {
      id: "2", 
      component: "CustomerForm",
      severity: "medium",
      category: "interaction",
      description: "Form fields too close together on mobile, difficult to tap accurately",
      recommendation: "Increase touch target size to minimum 44px and add more spacing between fields",
      breakpoint: "mobile",
      status: "in-progress"
    },
    {
      id: "3",
      component: "Navigation",
      severity: "low",
      category: "navigation",
      description: "Mobile menu animation could be smoother",
      recommendation: "Optimize animation performance and add haptic feedback",
      breakpoint: "mobile",
      status: "open"
    },
    {
      id: "4",
      component: "DealKanban",
      severity: "critical",
      category: "interaction",
      description: "Drag and drop not working on touch devices",
      recommendation: "Implement touch-friendly drag and drop with visual feedback",
      breakpoint: "mobile",
      status: "open"
    },
    {
      id: "5",
      component: "Charts",
      severity: "medium",
      category: "content",
      description: "Chart tooltips not accessible on touch devices",
      recommendation: "Add touch-friendly tooltip interactions and improve accessibility",
      breakpoint: "mobile",
      status: "resolved"
    }
  ],
  testedComponents: [
    "EnhancedDataTable", "CustomerForm", "DealKanban", "Navigation", 
    "Charts", "Dashboard", "Sidebar", "Modal", "Dropdown", "Pagination"
  ],
  lastAuditDate: new Date()
}

// Responsiveness Audit Component
export function MobileResponsivenessAudit({
  results = mockAuditResults,
  onRunAudit,
  onFixIssue,
  className
}: {
  results?: AuditResults
  onRunAudit?: () => void
  onFixIssue?: (issueId: string) => void
  className?: string
}) {
  const isMobile = useIsMobile()
  const [selectedCategory, setSelectedCategory] = React.useState<string>("all")
  const [selectedSeverity, setSelectedSeverity] = React.useState<string>("all")

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-red-100 text-red-800 border-red-200"
      case "high": return "bg-orange-100 text-orange-800 border-orange-200"
      case "medium": return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "low": return "bg-blue-100 text-blue-800 border-blue-200"
      default: return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "resolved": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "in-progress": return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case "open": return <XCircle className="h-4 w-4 text-red-600" />
      default: return null
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 80) return "text-yellow-600"
    if (score >= 70) return "text-orange-600"
    return "text-red-600"
  }

  const filteredIssues = results.issues.filter(issue => {
    const categoryMatch = selectedCategory === "all" || issue.category === selectedCategory
    const severityMatch = selectedSeverity === "all" || issue.severity === selectedSeverity
    return categoryMatch && severityMatch
  })

  const issueStats = {
    total: results.issues.length,
    critical: results.issues.filter(i => i.severity === "critical").length,
    high: results.issues.filter(i => i.severity === "high").length,
    medium: results.issues.filter(i => i.severity === "medium").length,
    low: results.issues.filter(i => i.severity === "low").length,
    resolved: results.issues.filter(i => i.status === "resolved").length
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="heading-2 text-foreground">Mobile Responsiveness Audit</h2>
          <p className="body-small text-muted-foreground mt-1">
            Comprehensive analysis of mobile responsiveness across all components
          </p>
        </div>
        <Button onClick={onRunAudit} className="gap-2">
          <Zap className="h-4 w-4" />
          Run New Audit
        </Button>
      </div>

      {/* Score Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Monitor className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.overall))}>
                {results.score.overall}%
              </div>
              <p className="text-xs text-muted-foreground">Overall</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Smartphone className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.mobile))}>
                {results.score.mobile}%
              </div>
              <p className="text-xs text-muted-foreground">Mobile</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Tablet className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.tablet))}>
                {results.score.tablet}%
              </div>
              <p className="text-xs text-muted-foreground">Tablet</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Monitor className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.desktop))}>
                {results.score.desktop}%
              </div>
              <p className="text-xs text-muted-foreground">Desktop</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Accessibility className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.accessibility))}>
                {results.score.accessibility}%
              </div>
              <p className="text-xs text-muted-foreground">Accessibility</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="space-y-2">
              <Zap className="h-6 w-6 mx-auto text-muted-foreground" />
              <div className={cn("text-2xl font-bold", getScoreColor(results.score.performance))}>
                {results.score.performance}%
              </div>
              <p className="text-xs text-muted-foreground">Performance</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Issue Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Issue Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{issueStats.total}</div>
              <p className="text-xs text-muted-foreground">Total Issues</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{issueStats.critical}</div>
              <p className="text-xs text-muted-foreground">Critical</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{issueStats.high}</div>
              <p className="text-xs text-muted-foreground">High</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{issueStats.medium}</div>
              <p className="text-xs text-muted-foreground">Medium</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{issueStats.low}</div>
              <p className="text-xs text-muted-foreground">Low</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{issueStats.resolved}</div>
              <p className="text-xs text-muted-foreground">Resolved</p>
            </div>
          </div>
          
          <Separator className="my-4" />
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Resolution Progress</span>
              <span>{Math.round((issueStats.resolved / issueStats.total) * 100)}%</span>
            </div>
            <Progress value={(issueStats.resolved / issueStats.total) * 100} />
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <div className="flex flex-wrap gap-2">
        <select
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="px-3 py-1 border rounded-md text-sm"
        >
          <option value="all">All Categories</option>
          <option value="layout">Layout</option>
          <option value="navigation">Navigation</option>
          <option value="interaction">Interaction</option>
          <option value="content">Content</option>
          <option value="performance">Performance</option>
        </select>

        <select
          value={selectedSeverity}
          onChange={(e) => setSelectedSeverity(e.target.value)}
          className="px-3 py-1 border rounded-md text-sm"
        >
          <option value="all">All Severities</option>
          <option value="critical">Critical</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
      </div>

      {/* Issues List */}
      <div className="space-y-4">
        {filteredIssues.map((issue) => (
          <Card key={issue.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2 flex-wrap">
                    <Badge className={getSeverityColor(issue.severity)}>
                      {issue.severity.toUpperCase()}
                    </Badge>
                    <Badge variant="outline">{issue.category}</Badge>
                    <Badge variant="outline">{issue.component}</Badge>
                    {issue.breakpoint && (
                      <Badge variant="secondary">{issue.breakpoint}</Badge>
                    )}
                    {getStatusIcon(issue.status)}
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-foreground">{issue.description}</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      <strong>Recommendation:</strong> {issue.recommendation}
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  {issue.status !== "resolved" && onFixIssue && (
                    <Button 
                      size="sm" 
                      onClick={() => onFixIssue(issue.id)}
                    >
                      Fix
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tested Components */}
      <Card>
        <CardHeader>
          <CardTitle>Tested Components ({results.testedComponents.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {results.testedComponents.map((component) => (
              <Badge key={component} variant="outline">
                {component}
              </Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground mt-4">
            Last audit: {results.lastAuditDate.toLocaleDateString()}
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
