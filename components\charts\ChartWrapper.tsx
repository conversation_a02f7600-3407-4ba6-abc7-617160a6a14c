'use client'

import React, { Suspense, lazy, memo, useC<PERSON>back, useMemo } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

// Performance monitoring
const useChartPerformance = (chartName: string) => {
  const startTime = useMemo(() => performance.now(), [])
  
  const logPerformance = useCallback(() => {
    const endTime = performance.now()
    const loadTime = endTime - startTime
    
    if (loadTime > 3000) {
      console.warn(`⚠️ Chart ${chartName} took ${loadTime.toFixed(2)}ms to load (>3s target)`)
    } else {
      console.log(`✅ Chart ${chartName} loaded in ${loadTime.toFixed(2)}ms`)
    }
    
    return loadTime
  }, [chartName, startTime])
  
  return { logPerformance }
}

// Lazy load chart components for better performance
const LazyPipelineChart = lazy(() => import('./PipelineChart').then(module => ({ default: module.PipelineChart })))
const LazyRevenueChart = lazy(() => import('./RevenueChart').then(module => ({ default: module.RevenueChart })))
const LazyLeadFunnelChart = lazy(() => import('./LeadFunnelChart').then(module => ({ default: module.LeadFunnelChart })))
const LazyCustomerDistributionChart = lazy(() => import('./CustomerDistributionChart').then(module => ({ default: module.CustomerDistributionChart })))
const LazyActivityChart = lazy(() => import('./ActivityChart').then(module => ({ default: module.ActivityChart })))

// Chart loading skeleton
const ChartSkeleton = memo(({ height = 300, title }: { height?: number; title?: string }) => (
  <Card>
    {title && (
      <CardHeader>
        <CardTitle>
          <Skeleton className="h-6 w-48" />
        </CardTitle>
        <Skeleton className="h-4 w-64" />
      </CardHeader>
    )}
    <CardContent>
      <Skeleton className="w-full" style={{ height: `${height}px` }} />
      <div className="mt-4 grid grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="text-center space-y-2">
            <Skeleton className="h-4 w-16 mx-auto" />
            <Skeleton className="h-6 w-20 mx-auto" />
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
))

ChartSkeleton.displayName = 'ChartSkeleton'

// Error boundary for charts
interface ChartErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  onRetry?: () => void
}

class ChartErrorBoundary extends React.Component<
  ChartErrorBoundaryProps,
  { hasError: boolean; error?: Error }
> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Chart Error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Chart Error</h3>
            <p className="text-muted-foreground mb-4">
              Failed to load chart. Please try again.
            </p>
            {this.props.onRetry && (
              <Button
                variant="outline"
                onClick={() => {
                  this.setState({ hasError: false, error: undefined })
                  this.props.onRetry?.()
                }}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            )}
          </CardContent>
        </Card>
      )
    }

    return this.props.children
  }
}

// High-performance chart wrapper with lazy loading and error handling
interface ChartWrapperProps {
  type: 'pipeline' | 'revenue' | 'leadFunnel' | 'customerDistribution' | 'activity'
  title?: string
  subtitle?: string
  className?: string
  config?: any
  loading?: boolean
  error?: string | null
  onRetry?: () => void
  [key: string]: any
}

export const ChartWrapper = memo(({ 
  type, 
  title, 
  subtitle, 
  className, 
  config, 
  loading, 
  error, 
  onRetry,
  ...props 
}: ChartWrapperProps) => {
  const { logPerformance } = useChartPerformance(type)

  const ChartComponent = useMemo(() => {
    switch (type) {
      case 'pipeline':
        return LazyPipelineChart
      case 'revenue':
        return LazyRevenueChart
      case 'leadFunnel':
        return LazyLeadFunnelChart
      case 'customerDistribution':
        return LazyCustomerDistributionChart
      case 'activity':
        return LazyActivityChart
      default:
        return null
    }
  }, [type])

  const handleLoad = useCallback(() => {
    logPerformance()
  }, [logPerformance])

  if (loading) {
    return <ChartSkeleton height={config?.height} title={title} />
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Error loading {type} chart: {error}
          {onRetry && (
            <Button variant="outline" size="sm" onClick={onRetry} className="ml-2">
              <RefreshCw className="h-4 w-4 mr-1" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (!ChartComponent) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Unknown chart type: {type}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <ChartErrorBoundary onRetry={onRetry}>
      <Suspense fallback={<ChartSkeleton height={config?.height} title={title} />}>
        <ChartComponent
          title={title}
          subtitle={subtitle}
          className={className}
          config={config}
          {...props}
        />
      </Suspense>
    </ChartErrorBoundary>
  )
})

ChartWrapper.displayName = 'ChartWrapper'

// Performance-optimized chart exports
export const OptimizedPipelineChart = memo((props: any) => (
  <ChartWrapper type="pipeline" {...props} />
))

export const OptimizedRevenueChart = memo((props: any) => (
  <ChartWrapper type="revenue" {...props} />
))

export const OptimizedLeadFunnelChart = memo((props: any) => (
  <ChartWrapper type="leadFunnel" {...props} />
))

export const OptimizedCustomerDistributionChart = memo((props: any) => (
  <ChartWrapper type="customerDistribution" {...props} />
))

export const OptimizedActivityChart = memo((props: any) => (
  <ChartWrapper type="activity" {...props} />
))

OptimizedPipelineChart.displayName = 'OptimizedPipelineChart'
OptimizedRevenueChart.displayName = 'OptimizedRevenueChart'
OptimizedLeadFunnelChart.displayName = 'OptimizedLeadFunnelChart'
OptimizedCustomerDistributionChart.displayName = 'OptimizedCustomerDistributionChart'
OptimizedActivityChart.displayName = 'OptimizedActivityChart'
