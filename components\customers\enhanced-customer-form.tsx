"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CustomerFormData, FormErrors, CustomerAddress, CustomerDocument } from "@/app/types/customer"
import { Building2, User, CreditCard, Truck, FileText, Star, MapPin, Upload } from "lucide-react"
import { AddressManager } from "./address-manager"
import { FileUpload } from "@/components/ui/file-upload"

interface EnhancedCustomerFormProps {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function EnhancedCustomerForm({ 
  initialData, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}: EnhancedCustomerFormProps) {
  const [formData, setFormData] = useState<CustomerFormData>({
    contact_person: initialData?.contact_person || "",
    title_position: initialData?.title_position || "",
    email: initialData?.email || "",
    phone: initialData?.phone || "",
    mobile: initialData?.mobile || "",
    company: initialData?.company || "",
    website: initialData?.website || "",
    city: initialData?.city || "",
    country: initialData?.country || "Jordan",
    business_type: initialData?.business_type || undefined,
    industry: initialData?.industry || "",
    annual_volume: initialData?.annual_volume || undefined,
    company_size: initialData?.company_size || undefined,
    tax_id: initialData?.tax_id || "",
    credit_limit: initialData?.credit_limit || undefined,
    payment_terms: initialData?.payment_terms || undefined,
    currency_preference: initialData?.currency_preference || "USD",
    preferred_shipping_method: initialData?.preferred_shipping_method || undefined,
    preferred_incoterms: initialData?.preferred_incoterms || undefined,
    shipping_instructions: initialData?.shipping_instructions || "",
    account_manager: initialData?.account_manager || "",
    customer_since: initialData?.customer_since || "",
    customer_tier: initialData?.customer_tier || "Bronze",
    tags: initialData?.tags || [],
    required_certificates: initialData?.required_certificates || [],
    compliance_requirements: initialData?.compliance_requirements || [],
    source: initialData?.source || "Website",
    status: initialData?.status || "Active",
    notes: initialData?.notes || "",
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [activeTab, setActiveTab] = useState("basic")
  const [addresses, setAddresses] = useState<CustomerAddress[]>([])
  const [documents, setDocuments] = useState<File[]>([])

  const handleInputChange = (field: keyof CustomerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Required fields validation
    if (!formData.contact_person.trim()) {
      newErrors.contact_person = "Contact person is required"
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }
    if (!formData.company.trim()) {
      newErrors.company = "Company name is required"
    }
    if (!formData.city.trim()) {
      newErrors.city = "City is required"
    }
    if (!formData.country.trim()) {
      newErrors.country = "Country is required"
    }

    // Numeric validations
    if (formData.annual_volume && formData.annual_volume < 0) {
      newErrors.annual_volume = "Annual volume must be positive"
    }
    if (formData.credit_limit && formData.credit_limit < 0) {
      newErrors.credit_limit = "Credit limit must be positive"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSubmit(formData)
    } catch (error) {
      console.error("Form submission error:", error)
      setErrors({ submit: "Failed to save customer. Please try again." })
    }
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'bg-amber-100 text-amber-800'
      case 'Silver': return 'bg-gray-100 text-gray-800'
      case 'Gold': return 'bg-yellow-100 text-yellow-800'
      case 'Platinum': return 'bg-purple-100 text-purple-800'
      case 'VIP': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="business" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Business
          </TabsTrigger>
          <TabsTrigger value="financial" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Financial
          </TabsTrigger>
          <TabsTrigger value="shipping" className="flex items-center gap-2">
            <Truck className="h-4 w-4" />
            Shipping
          </TabsTrigger>
          <TabsTrigger value="addresses" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Addresses
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Documents
          </TabsTrigger>
          <TabsTrigger value="additional" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Additional
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact_person">Contact Person *</Label>
                <Input
                  id="contact_person"
                  value={formData.contact_person}
                  onChange={(e) => handleInputChange("contact_person", e.target.value)}
                  placeholder="John Doe"
                />
                {errors.contact_person && (
                  <p className="text-sm text-red-600">{errors.contact_person}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="title_position">Title/Position</Label>
                <Input
                  id="title_position"
                  value={formData.title_position}
                  onChange={(e) => handleInputChange("title_position", e.target.value)}
                  placeholder="Sales Manager"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+962-6-123-4567"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="mobile">Mobile Number</Label>
                <Input
                  id="mobile"
                  value={formData.mobile}
                  onChange={(e) => handleInputChange("mobile", e.target.value)}
                  placeholder="+962-79-123-4567"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://company.com"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Company Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company">Company Name *</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  placeholder="ABC Trading Company"
                />
                {errors.company && (
                  <p className="text-sm text-red-600">{errors.company}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Amman"
                />
                {errors.city && (
                  <p className="text-sm text-red-600">{errors.city}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="country">Country *</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange("country", e.target.value)}
                  placeholder="Jordan"
                />
                {errors.country && (
                  <p className="text-sm text-red-600">{errors.country}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Active">Active</SelectItem>
                    <SelectItem value="Inactive">Inactive</SelectItem>
                    <SelectItem value="Prospect">Prospect</SelectItem>
                    <SelectItem value="VIP">VIP</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Business Details
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="business_type">Business Type</Label>
                <Select value={formData.business_type} onValueChange={(value) => handleInputChange("business_type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Retailer">Retailer</SelectItem>
                    <SelectItem value="Wholesaler">Wholesaler</SelectItem>
                    <SelectItem value="Distributor">Distributor</SelectItem>
                    <SelectItem value="Manufacturer">Manufacturer</SelectItem>
                    <SelectItem value="E-commerce">E-commerce</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="industry">Industry</Label>
                <Input
                  id="industry"
                  value={formData.industry}
                  onChange={(e) => handleInputChange("industry", e.target.value)}
                  placeholder="Electronics, Textiles, etc."
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="company_size">Company Size</Label>
                <Select value={formData.company_size} onValueChange={(value) => handleInputChange("company_size", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select company size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Small">Small (1-50 employees)</SelectItem>
                    <SelectItem value="Medium">Medium (51-250 employees)</SelectItem>
                    <SelectItem value="Large">Large (251-1000 employees)</SelectItem>
                    <SelectItem value="Enterprise">Enterprise (1000+ employees)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="annual_volume">Annual Volume (USD)</Label>
                <Input
                  id="annual_volume"
                  type="number"
                  value={formData.annual_volume || ""}
                  onChange={(e) => handleInputChange("annual_volume", e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="1000000"
                />
                {errors.annual_volume && (
                  <p className="text-sm text-red-600">{errors.annual_volume}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="customer_tier">Customer Tier</Label>
                <Select value={formData.customer_tier} onValueChange={(value) => handleInputChange("customer_tier", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Bronze">Bronze</SelectItem>
                    <SelectItem value="Silver">Silver</SelectItem>
                    <SelectItem value="Gold">Gold</SelectItem>
                    <SelectItem value="Platinum">Platinum</SelectItem>
                    <SelectItem value="VIP">VIP</SelectItem>
                  </SelectContent>
                </Select>
                <Badge className={getTierColor(formData.customer_tier || "Bronze")}>
                  <Star className="h-3 w-3 mr-1" />
                  {formData.customer_tier} Customer
                </Badge>
              </div>

              <div className="space-y-2">
                <Label htmlFor="account_manager">Account Manager</Label>
                <Input
                  id="account_manager"
                  value={formData.account_manager}
                  onChange={(e) => handleInputChange("account_manager", e.target.value)}
                  placeholder="Sales representative name"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Financial Information
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="tax_id">Tax ID / VAT Number</Label>
                <Input
                  id="tax_id"
                  value={formData.tax_id}
                  onChange={(e) => handleInputChange("tax_id", e.target.value)}
                  placeholder="*********"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="credit_limit">Credit Limit (USD)</Label>
                <Input
                  id="credit_limit"
                  type="number"
                  value={formData.credit_limit || ""}
                  onChange={(e) => handleInputChange("credit_limit", e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="50000"
                />
                {errors.credit_limit && (
                  <p className="text-sm text-red-600">{errors.credit_limit}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="payment_terms">Payment Terms</Label>
                <Select value={formData.payment_terms} onValueChange={(value) => handleInputChange("payment_terms", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment terms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Net 15">Net 15 Days</SelectItem>
                    <SelectItem value="30 Days">Net 30 Days</SelectItem>
                    <SelectItem value="Net 45">Net 45 Days</SelectItem>
                    <SelectItem value="60 Days">Net 60 Days</SelectItem>
                    <SelectItem value="90 Days">Net 90 Days</SelectItem>
                    <SelectItem value="COD">Cash on Delivery</SelectItem>
                    <SelectItem value="Prepaid">Prepaid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="currency_preference">Currency Preference</Label>
                <Select value={formData.currency_preference} onValueChange={(value) => handleInputChange("currency_preference", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                    <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                    <SelectItem value="JOD">JOD - Jordanian Dinar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Shipping Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="preferred_shipping_method">Preferred Shipping Method</Label>
                <Select value={formData.preferred_shipping_method} onValueChange={(value) => handleInputChange("preferred_shipping_method", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select shipping method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Air">Air Freight</SelectItem>
                    <SelectItem value="Sea">Sea Freight</SelectItem>
                    <SelectItem value="Express">Express Courier</SelectItem>
                    <SelectItem value="Ground">Ground Transport</SelectItem>
                    <SelectItem value="Courier">Local Courier</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="preferred_incoterms">Preferred Incoterms</Label>
                <Select value={formData.preferred_incoterms} onValueChange={(value) => handleInputChange("preferred_incoterms", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select incoterms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="FOB">FOB - Free on Board</SelectItem>
                    <SelectItem value="CIF">CIF - Cost, Insurance & Freight</SelectItem>
                    <SelectItem value="EXW">EXW - Ex Works</SelectItem>
                    <SelectItem value="DDP">DDP - Delivered Duty Paid</SelectItem>
                    <SelectItem value="DDU">DDU - Delivered Duty Unpaid</SelectItem>
                    <SelectItem value="FCA">FCA - Free Carrier</SelectItem>
                    <SelectItem value="CPT">CPT - Carriage Paid To</SelectItem>
                    <SelectItem value="CIP">CIP - Carriage & Insurance Paid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="shipping_instructions">Special Shipping Instructions</Label>
                <Textarea
                  id="shipping_instructions"
                  value={formData.shipping_instructions}
                  onChange={(e) => handleInputChange("shipping_instructions", e.target.value)}
                  placeholder="Any special handling requirements, delivery instructions, etc."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addresses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Address Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AddressManager
                addresses={addresses}
                onAddressChange={setAddresses}
                customerId={undefined} // Will be set after customer creation
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Document Upload
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FileUpload
                onFileSelect={setDocuments}
                maxFiles={10}
                maxSize={10}
                acceptedTypes={[
                  "image/*",
                  ".pdf",
                  ".doc",
                  ".docx",
                  ".xls",
                  ".xlsx",
                  ".txt"
                ]}
              />
              <div className="mt-4 text-sm text-gray-600">
                <p className="font-medium mb-2">Recommended documents:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Business License</li>
                  <li>Tax Certificate</li>
                  <li>Bank Details</li>
                  <li>Insurance Documents</li>
                  <li>Quality Certifications</li>
                  <li>Contracts & Agreements</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="additional" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Additional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer_since">Customer Since</Label>
                  <Input
                    id="customer_since"
                    type="date"
                    value={formData.customer_since}
                    onChange={(e) => handleInputChange("customer_since", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="source">Lead Source</Label>
                  <Select value={formData.source} onValueChange={(value) => handleInputChange("source", value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Website">Website</SelectItem>
                      <SelectItem value="Referral">Referral</SelectItem>
                      <SelectItem value="Trade Show">Trade Show</SelectItem>
                      <SelectItem value="Cold Call">Cold Call</SelectItem>
                      <SelectItem value="Social Media">Social Media</SelectItem>
                      <SelectItem value="Advertisement">Advertisement</SelectItem>
                      <SelectItem value="Partner">Partner</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Additional notes about the customer..."
                  rows={4}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {errors.submit && (
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
          {errors.submit}
        </div>
      )}

      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : "Save Customer"}
        </Button>
      </div>
    </form>
  )
}
