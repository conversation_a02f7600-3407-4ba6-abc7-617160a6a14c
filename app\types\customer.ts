// Contact source options with emojis
export const CONTACT_SOURCES = [
  { value: "Website", label: "🌐 Website" },
  { value: "Referral", label: "👥 Referral" },
  { value: "Cold Call", label: "📞 Cold Call" },
  { value: "Social Media", label: "📱 Social Media" },
  { value: "Trade Show", label: "🎪 Trade Show" },
  { value: "Other", label: "📋 Other" }
] as const;

// Status options with emojis (matching database constraints)
export const STATUS_OPTIONS = [
  { value: "Active", label: "🟢 Active" },
  { value: "Inactive", label: "⚫ Inactive" },
  { value: "Potential", label: "🟡 Potential" }
] as const;

// Type for contact source
export type ContactSource = typeof CONTACT_SOURCES[number]["value"];

// Type for customer status
export type CustomerStatus = typeof STATUS_OPTIONS[number]["value"];

// Enhanced form data interface for comprehensive customer management
export interface CustomerFormData {
  // Basic Information
  contact_person: string;
  title_position?: string;
  email: string;
  phone?: string;
  mobile?: string;
  company: string;
  website?: string;

  // Location
  city: string;
  country: string;

  // Business Details
  business_type?: 'Retailer' | 'Wholesaler' | 'Distributor' | 'Manufacturer' | 'E-commerce' | 'Other';
  industry?: string;
  annual_volume?: number;
  company_size?: 'Small' | 'Medium' | 'Large' | 'Enterprise';

  // Financial Information
  tax_id?: string;
  credit_limit?: number;
  payment_terms?: '30 Days' | '60 Days' | '90 Days' | 'COD' | 'Prepaid' | 'Net 15' | 'Net 45';
  currency_preference?: string;

  // Shipping Preferences
  preferred_shipping_method?: 'Air' | 'Sea' | 'Express' | 'Ground' | 'Courier';
  preferred_incoterms?: 'FOB' | 'CIF' | 'EXW' | 'DDP' | 'DDU' | 'FCA' | 'CPT' | 'CIP';
  shipping_instructions?: string;

  // Relationship Management
  account_manager?: string;
  customer_since?: string;
  customer_tier?: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'VIP';

  // Arrays
  tags?: string[];
  required_certificates?: string[];
  compliance_requirements?: string[];

  // Original fields
  source: ContactSource;
  status: CustomerStatus;
  notes?: string;
}

// Enhanced database schema interface
export interface CustomerDatabaseSchema {
  id: string;
  user_id: string;
  name: string;
  contact_person: string;
  title_position?: string | null;
  email: string;
  phone: string | null;
  mobile?: string | null;
  company: string;
  website?: string | null;
  city: string;
  country: string;
  business_type?: 'Retailer' | 'Wholesaler' | 'Distributor' | 'Manufacturer' | 'E-commerce' | 'Other' | null;
  industry?: string | null;
  annual_volume?: number | null;
  company_size?: 'Small' | 'Medium' | 'Large' | 'Enterprise' | null;
  tax_id?: string | null;
  credit_limit?: number | null;
  payment_terms?: '30 Days' | '60 Days' | '90 Days' | 'COD' | 'Prepaid' | 'Net 15' | 'Net 45' | null;
  currency_preference?: string | null;
  preferred_shipping_method?: 'Air' | 'Sea' | 'Express' | 'Ground' | 'Courier' | null;
  preferred_incoterms?: 'FOB' | 'CIF' | 'EXW' | 'DDP' | 'DDU' | 'FCA' | 'CPT' | 'CIP' | null;
  shipping_instructions?: string | null;
  account_manager?: string | null;
  customer_since?: string | null;
  last_order_date?: string | null;
  total_orders?: number | null;
  lifetime_value?: number | null;
  customer_tier?: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | 'VIP' | null;
  tags?: string[] | null;
  required_certificates?: string[] | null;
  compliance_requirements?: string[] | null;
  contact_source: ContactSource;
  status: CustomerStatus;
  notes: string | null;
  created_at: string;
  updated_at: string;
}

// Customer Address interface
export interface CustomerAddress {
  id: string;
  customer_id: string;
  user_id: string;
  address_type: 'billing' | 'shipping' | 'office' | 'warehouse';
  is_primary: boolean;
  company_name?: string;
  contact_person?: string;
  street_address: string;
  address_line_2?: string;
  city: string;
  state_province?: string;
  postal_code?: string;
  country: string;
  phone?: string;
  email?: string;
  special_instructions?: string;
  created_at: string;
  updated_at: string;
}

// Customer Document interface
export interface CustomerDocument {
  id: string;
  customer_id: string;
  user_id: string;
  document_type: 'business_license' | 'tax_certificate' | 'bank_details' | 'insurance' | 'certification' | 'contract' | 'other';
  document_name: string;
  file_url: string;
  file_size?: number;
  file_type?: string;
  description?: string;
  expiry_date?: string;
  is_verified: boolean;
  uploaded_at: string;
  created_at: string;
  updated_at: string;
}

// Customer Contact interface
export interface CustomerContact {
  id: string;
  customer_id: string;
  user_id: string;
  contact_type: 'primary' | 'secondary' | 'finance' | 'operations' | 'technical' | 'management';
  full_name: string;
  title_position?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  department?: string;
  is_decision_maker: boolean;
  preferred_contact_method: 'email' | 'phone' | 'whatsapp' | 'wechat';
  notes?: string;
  created_at: string;
  updated_at: string;
}

// Form errors interface
export interface FormErrors {
  contact_person?: string;
  title_position?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  company?: string;
  website?: string;
  country?: string;
  city?: string;
  business_type?: string;
  industry?: string;
  annual_volume?: string;
  company_size?: string;
  tax_id?: string;
  credit_limit?: string;
  payment_terms?: string;
  currency_preference?: string;
  preferred_shipping_method?: string;
  preferred_incoterms?: string;
  shipping_instructions?: string;
  account_manager?: string;
  customer_since?: string;
  customer_tier?: string;
  tags?: string;
  required_certificates?: string;
  compliance_requirements?: string;
  source?: string;
  status?: string;
  notes?: string;
  submit?: string;
}

// Form props interface
export interface CustomerFormProps {
  onSubmit: (data: CustomerFormData) => Promise<void>;
  onClose: () => void;
  initialData?: CustomerFormData;
}

// Helper function to convert empty strings to null for database fields
function sanitizeForDatabase(value: any): any {
  if (value === "" || value === undefined) {
    return null;
  }
  return value;
}

// Helper function to convert empty date strings to null
function sanitizeDateForDatabase(dateString: string | undefined): string | null {
  if (!dateString || dateString.trim() === "") {
    return null;
  }

  // Validate date format if provided
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    console.warn(`Invalid date format: ${dateString}, converting to null`);
    return null;
  }

  return dateString;
}

// Map form data to database schema
export function mapFormToDatabase(formData: CustomerFormData, userId: string): Omit<CustomerDatabaseSchema, 'id' | 'created_at' | 'updated_at'> {
  console.log("🔍 [MAPPING DEBUG] Input form data:", formData)
  console.log("🔍 [MAPPING DEBUG] User ID:", userId)

  const mappedData = {
    user_id: userId,
    name: formData.contact_person,
    contact_person: formData.contact_person,
    title_position: sanitizeForDatabase(formData.title_position),
    email: formData.email,
    phone: sanitizeForDatabase(formData.phone),
    mobile: sanitizeForDatabase(formData.mobile),
    company: formData.company,
    website: sanitizeForDatabase(formData.website),
    city: formData.city,
    country: formData.country,
    business_type: sanitizeForDatabase(formData.business_type),
    industry: sanitizeForDatabase(formData.industry),
    annual_volume: formData.annual_volume ?? null,
    company_size: sanitizeForDatabase(formData.company_size),
    tax_id: sanitizeForDatabase(formData.tax_id),
    credit_limit: formData.credit_limit ?? null,
    payment_terms: sanitizeForDatabase(formData.payment_terms),
    currency_preference: formData.currency_preference || 'USD',
    preferred_shipping_method: sanitizeForDatabase(formData.preferred_shipping_method),
    preferred_incoterms: sanitizeForDatabase(formData.preferred_incoterms),
    shipping_instructions: sanitizeForDatabase(formData.shipping_instructions),
    account_manager: sanitizeForDatabase(formData.account_manager),
    customer_since: sanitizeDateForDatabase(formData.customer_since),
    last_order_date: null, // Always null for new customers
    total_orders: 0,
    lifetime_value: 0,
    customer_tier: formData.customer_tier || 'Bronze',
    tags: formData.tags && formData.tags.length > 0 ? formData.tags : null,
    required_certificates: formData.required_certificates && formData.required_certificates.length > 0 ? formData.required_certificates : null,
    compliance_requirements: formData.compliance_requirements && formData.compliance_requirements.length > 0 ? formData.compliance_requirements : null,
    contact_source: formData.source,
    status: formData.status,
    notes: sanitizeForDatabase(formData.notes)
  };

  console.log("🔍 [MAPPING DEBUG] Output mapped data:", mappedData)
  return mappedData;
}

// Map database schema to form data
export function mapDatabaseToForm(data: CustomerDatabaseSchema): CustomerFormData {
  return {
    contact_person: data.name || data.contact_person,
    title_position: data.title_position ?? undefined,
    email: data.email,
    phone: data.phone ?? undefined,
    mobile: data.mobile ?? undefined,
    company: data.company,
    website: data.website ?? undefined,
    city: data.city,
    country: data.country,
    business_type: data.business_type ?? undefined,
    industry: data.industry ?? undefined,
    annual_volume: data.annual_volume ?? undefined,
    company_size: data.company_size ?? undefined,
    tax_id: data.tax_id ?? undefined,
    credit_limit: data.credit_limit ?? undefined,
    payment_terms: data.payment_terms ?? undefined,
    currency_preference: data.currency_preference ?? undefined,
    preferred_shipping_method: data.preferred_shipping_method ?? undefined,
    preferred_incoterms: data.preferred_incoterms ?? undefined,
    shipping_instructions: data.shipping_instructions ?? undefined,
    account_manager: data.account_manager ?? undefined,
    customer_since: data.customer_since ?? undefined,
    customer_tier: data.customer_tier ?? undefined,
    tags: data.tags ?? undefined,
    required_certificates: data.required_certificates ?? undefined,
    compliance_requirements: data.compliance_requirements ?? undefined,
    source: data.contact_source,
    status: data.status,
    notes: data.notes ?? undefined
  };
}