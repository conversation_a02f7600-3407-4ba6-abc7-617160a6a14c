import { test, expect } from '@playwright/test';

test.describe('Smoke Tests - Basic Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for navigation
    page.setDefaultTimeout(30000);
  });

  test('should load homepage without errors', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/Nawras CRM/i);
    
    // Check for console errors
    const errors: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    // Wait for page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Log any console errors found
    if (errors.length > 0) {
      console.log('Console errors found:', errors);
    }
  });

  test('should navigate to login page', async ({ page }) => {
    await page.goto('/login');
    await expect(page.locator('h1, h2')).toContainText(/login|sign in/i);
    
    // Check for login form elements
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
  });

  test('should attempt login with test credentials', async ({ page }) => {
    await page.goto('/login');
    
    // Fill in test credentials
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    
    // Click login button
    await page.click('button[type="submit"], button:has-text("Login"), button:has-text("Sign in")');
    
    // Wait for navigation or error message
    await page.waitForTimeout(5000);
    
    // Check if we're redirected to dashboard or see an error
    const currentUrl = page.url();
    console.log('Current URL after login attempt:', currentUrl);
    
    // Log page content for debugging
    const pageContent = await page.textContent('body');
    console.log('Page content after login:', pageContent?.substring(0, 500));
  });

  test('should check dashboard accessibility (if logged in)', async ({ page }) => {
    // Try to access dashboard directly
    await page.goto('/dashboard');
    
    // Wait for page to load
    await page.waitForTimeout(3000);
    
    const currentUrl = page.url();
    console.log('Dashboard access URL:', currentUrl);
    
    // Check if we're redirected to login or can access dashboard
    if (currentUrl.includes('/login')) {
      console.log('Dashboard requires authentication - redirected to login');
    } else {
      console.log('Dashboard accessible - checking for basic elements');
      
      // Look for common dashboard elements
      const hasNavigation = await page.locator('nav, [role="navigation"]').count() > 0;
      const hasSidebar = await page.locator('aside, .sidebar').count() > 0;
      const hasMainContent = await page.locator('main, .main-content').count() > 0;
      
      console.log('Dashboard elements found:', { hasNavigation, hasSidebar, hasMainContent });
    }
  });

  test('should check for 404 errors on main routes', async ({ page }) => {
    const routes = [
      '/dashboard/customers',
      '/dashboard/deals',
      '/dashboard/leads',
      '/dashboard/opportunities',
      '/dashboard/companies',
      '/dashboard/tasks',
      '/dashboard/reports',
      '/dashboard/settings',
      '/dashboard/admin'
    ];

    const results: { route: string; status: number; accessible: boolean }[] = [];

    for (const route of routes) {
      try {
        const response = await page.goto(route);
        const status = response?.status() || 0;
        const accessible = status < 400;
        
        results.push({ route, status, accessible });
        console.log(`Route ${route}: Status ${status} - ${accessible ? 'Accessible' : 'Not Accessible'}`);
        
        // Brief wait between requests
        await page.waitForTimeout(1000);
      } catch (error) {
        console.log(`Error accessing ${route}:`, error);
        results.push({ route, status: 0, accessible: false });
      }
    }

    // Log summary
    const accessibleRoutes = results.filter(r => r.accessible).length;
    const totalRoutes = results.length;
    console.log(`Route accessibility: ${accessibleRoutes}/${totalRoutes} routes accessible`);
    
    // Log problematic routes
    const problematicRoutes = results.filter(r => !r.accessible);
    if (problematicRoutes.length > 0) {
      console.log('Problematic routes:', problematicRoutes);
    }
  });
});
