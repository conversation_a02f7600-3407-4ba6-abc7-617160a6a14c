"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"

// Accessibility Types
export interface TableAccessibilityProps {
  caption?: string
  summary?: string
  sortable?: boolean
  selectable?: boolean
  onKeyDown?: (e: React.KeyboardEvent) => void
  announcements?: boolean
}

export interface CellAccessibilityProps {
  rowIndex: number
  columnIndex: number
  isHeader?: boolean
  sortDirection?: "asc" | "desc" | "none"
  isSelected?: boolean
  isSelectable?: boolean
  onSelect?: () => void
  children: React.ReactNode
}

// Screen Reader Announcements
export function TableAnnouncement({ 
  message, 
  priority = "polite" 
}: { 
  message: string
  priority?: "polite" | "assertive" 
}) {
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {message}
    </div>
  )
}

// Accessible Table Caption
export function AccessibleTableCaption({
  children,
  className
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <caption className={cn("sr-only", className)}>
      {children}
    </caption>
  )
}

// Accessible Table Header
export function AccessibleTableHeader({
  children,
  sortable = false,
  sortDirection = "none",
  onSort,
  columnId,
  className
}: {
  children: React.ReactNode
  sortable?: boolean
  sortDirection?: "asc" | "desc" | "none"
  onSort?: () => void
  columnId?: string
  className?: string
}) {
  const getSortLabel = () => {
    if (!sortable) return ""
    
    switch (sortDirection) {
      case "asc":
        return "sorted ascending"
      case "desc":
        return "sorted descending"
      default:
        return "not sorted"
    }
  }

  const getAriaSort = () => {
    if (!sortable) return undefined
    
    switch (sortDirection) {
      case "asc":
        return "ascending"
      case "desc":
        return "descending"
      default:
        return "none"
    }
  }

  if (sortable) {
    return (
      <th
        className={cn("focus:outline-none focus:ring-2 focus:ring-primary", className)}
        aria-sort={getAriaSort()}
        tabIndex={0}
        onClick={onSort}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            e.preventDefault()
            onSort?.()
          }
        }}
        role="columnheader button"
        aria-label={`${children}, ${getSortLabel()}, activate to sort`}
      >
        {children}
      </th>
    )
  }

  return (
    <th className={className} role="columnheader">
      {children}
    </th>
  )
}

// Accessible Table Cell
export function AccessibleTableCell({
  rowIndex,
  columnIndex,
  isHeader = false,
  sortDirection,
  isSelected = false,
  isSelectable = false,
  onSelect,
  children,
  className
}: CellAccessibilityProps & { className?: string }) {
  const cellRef = React.useRef<HTMLTableCellElement>(null)

  const handleKeyDown = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case "Enter":
      case " ":
        if (isSelectable && onSelect) {
          e.preventDefault()
          onSelect()
        }
        break
      case "ArrowUp":
      case "ArrowDown":
      case "ArrowLeft":
      case "ArrowRight":
        // Handle arrow key navigation
        e.preventDefault()
        navigateTable(e.key, rowIndex, columnIndex)
        break
    }
  }

  const navigateTable = (key: string, row: number, col: number) => {
    const table = cellRef.current?.closest("table")
    if (!table) return

    let targetRow = row
    let targetCol = col

    switch (key) {
      case "ArrowUp":
        targetRow = Math.max(0, row - 1)
        break
      case "ArrowDown":
        targetRow = row + 1
        break
      case "ArrowLeft":
        targetCol = Math.max(0, col - 1)
        break
      case "ArrowRight":
        targetCol = col + 1
        break
    }

    const targetCell = table.querySelector(
      `[data-row="${targetRow}"][data-col="${targetCol}"]`
    ) as HTMLElement
    
    if (targetCell) {
      targetCell.focus()
    }
  }

  const CellComponent = isHeader ? "th" : "td"

  return (
    <CellComponent
      ref={cellRef}
      className={cn(
        "focus:outline-none focus:ring-2 focus:ring-primary",
        isSelectable && "cursor-pointer",
        isSelected && "bg-primary/10",
        className
      )}
      data-row={rowIndex}
      data-col={columnIndex}
      tabIndex={isSelectable ? 0 : -1}
      onClick={isSelectable ? onSelect : undefined}
      onKeyDown={handleKeyDown}
      aria-selected={isSelectable ? isSelected : undefined}
      role={isSelectable ? "gridcell" : "cell"}
    >
      {children}
    </CellComponent>
  )
}

// Table Navigation Hook
export function useTableNavigation({
  rowCount,
  columnCount,
  onCellSelect,
  onRowSelect
}: {
  rowCount: number
  columnCount: number
  onCellSelect?: (row: number, col: number) => void
  onRowSelect?: (row: number) => void
}) {
  const [focusedCell, setFocusedCell] = React.useState<{ row: number; col: number } | null>(null)

  const handleKeyDown = React.useCallback((e: React.KeyboardEvent) => {
    if (!focusedCell) return

    const { row, col } = focusedCell
    let newRow = row
    let newCol = col

    switch (e.key) {
      case "ArrowUp":
        e.preventDefault()
        newRow = Math.max(0, row - 1)
        break
      case "ArrowDown":
        e.preventDefault()
        newRow = Math.min(rowCount - 1, row + 1)
        break
      case "ArrowLeft":
        e.preventDefault()
        newCol = Math.max(0, col - 1)
        break
      case "ArrowRight":
        e.preventDefault()
        newCol = Math.min(columnCount - 1, col + 1)
        break
      case "Home":
        e.preventDefault()
        if (e.ctrlKey) {
          newRow = 0
          newCol = 0
        } else {
          newCol = 0
        }
        break
      case "End":
        e.preventDefault()
        if (e.ctrlKey) {
          newRow = rowCount - 1
          newCol = columnCount - 1
        } else {
          newCol = columnCount - 1
        }
        break
      case "PageUp":
        e.preventDefault()
        newRow = Math.max(0, row - 10)
        break
      case "PageDown":
        e.preventDefault()
        newRow = Math.min(rowCount - 1, row + 10)
        break
      case "Enter":
      case " ":
        e.preventDefault()
        onCellSelect?.(row, col)
        break
      case " ":
        if (e.shiftKey) {
          e.preventDefault()
          onRowSelect?.(row)
        }
        break
    }

    if (newRow !== row || newCol !== col) {
      setFocusedCell({ row: newRow, col: newCol })
      onCellSelect?.(newRow, newCol)
    }
  }, [focusedCell, rowCount, columnCount, onCellSelect, onRowSelect])

  return {
    focusedCell,
    setFocusedCell,
    handleKeyDown
  }
}

// Table Selection Hook
export function useTableSelection({
  totalRows,
  onSelectionChange
}: {
  totalRows: number
  onSelectionChange?: (selectedRows: Set<number>) => void
}) {
  const [selectedRows, setSelectedRows] = React.useState<Set<number>>(new Set())
  const [lastSelectedRow, setLastSelectedRow] = React.useState<number | null>(null)

  const selectRow = React.useCallback((rowIndex: number, extend = false) => {
    setSelectedRows(prev => {
      const newSelection = new Set(prev)
      
      if (extend && lastSelectedRow !== null) {
        // Range selection
        const start = Math.min(lastSelectedRow, rowIndex)
        const end = Math.max(lastSelectedRow, rowIndex)
        for (let i = start; i <= end; i++) {
          newSelection.add(i)
        }
      } else if (newSelection.has(rowIndex)) {
        newSelection.delete(rowIndex)
      } else {
        newSelection.add(rowIndex)
      }
      
      onSelectionChange?.(newSelection)
      return newSelection
    })
    
    setLastSelectedRow(rowIndex)
  }, [lastSelectedRow, onSelectionChange])

  const selectAll = React.useCallback(() => {
    const allRows = new Set(Array.from({ length: totalRows }, (_, i) => i))
    setSelectedRows(allRows)
    onSelectionChange?.(allRows)
  }, [totalRows, onSelectionChange])

  const clearSelection = React.useCallback(() => {
    setSelectedRows(new Set())
    onSelectionChange?.(new Set())
  }, [onSelectionChange])

  const isSelected = React.useCallback((rowIndex: number) => {
    return selectedRows.has(rowIndex)
  }, [selectedRows])

  const isAllSelected = selectedRows.size === totalRows && totalRows > 0
  const isIndeterminate = selectedRows.size > 0 && selectedRows.size < totalRows

  return {
    selectedRows,
    selectRow,
    selectAll,
    clearSelection,
    isSelected,
    isAllSelected,
    isIndeterminate
  }
}

// Accessible Table Wrapper
export function AccessibleTable({
  children,
  caption,
  summary,
  sortable = false,
  selectable = false,
  onKeyDown,
  announcements = true,
  className
}: TableAccessibilityProps & {
  children: React.ReactNode
  className?: string
}) {
  const tableRef = React.useRef<HTMLTableElement>(null)
  const [announcement, setAnnouncement] = React.useState("")

  // Announce table changes
  const announce = React.useCallback((message: string) => {
    if (announcements) {
      setAnnouncement(message)
      setTimeout(() => setAnnouncement(""), 1000)
    }
  }, [announcements])

  React.useEffect(() => {
    const table = tableRef.current
    if (!table) return

    const handleFocus = (e: FocusEvent) => {
      const target = e.target as HTMLElement
      if (target.tagName === "TH" || target.tagName === "TD") {
        const row = target.getAttribute("data-row")
        const col = target.getAttribute("data-col")
        if (row && col) {
          announce(`Row ${parseInt(row) + 1}, Column ${parseInt(col) + 1}`)
        }
      }
    }

    table.addEventListener("focus", handleFocus, true)
    return () => table.removeEventListener("focus", handleFocus, true)
  }, [announce])

  return (
    <div className="relative">
      <table
        ref={tableRef}
        className={cn("w-full", className)}
        role={selectable ? "grid" : "table"}
        aria-label={caption}
        aria-describedby={summary ? "table-summary" : undefined}
        onKeyDown={onKeyDown}
      >
        {caption && <AccessibleTableCaption>{caption}</AccessibleTableCaption>}
        {children}
      </table>
      
      {summary && (
        <div id="table-summary" className="sr-only">
          {summary}
        </div>
      )}
      
      {announcements && announcement && (
        <TableAnnouncement message={announcement} />
      )}
    </div>
  )
}

// Skip to Table Content Link
export function SkipToTable({ tableId }: { tableId: string }) {
  return (
    <a
      href={`#${tableId}`}
      className={cn(
        "absolute left-[-9999px] top-auto w-1 h-1 overflow-hidden",
        "focus:left-6 focus:top-6 focus:w-auto focus:h-auto focus:overflow-visible",
        "bg-primary text-primary-foreground px-4 py-2 rounded-md",
        "text-sm font-medium z-50 transition-all"
      )}
    >
      Skip to table content
    </a>
  )
}

// Table Toolbar for Screen Readers
export function TableToolbar({
  totalRows,
  selectedRows,
  onSelectAll,
  onClearSelection,
  children
}: {
  totalRows: number
  selectedRows: number
  onSelectAll?: () => void
  onClearSelection?: () => void
  children?: React.ReactNode
}) {
  return (
    <div
      role="toolbar"
      aria-label="Table actions"
      className="flex items-center gap-2 mb-4"
    >
      <div className="text-sm text-muted-foreground">
        {selectedRows > 0 ? (
          <span aria-live="polite">
            {selectedRows} of {totalRows} rows selected
          </span>
        ) : (
          <span>{totalRows} rows</span>
        )}
      </div>
      
      {onSelectAll && (
        <Button
          variant="outline"
          size="sm"
          onClick={onSelectAll}
          aria-label={`Select all ${totalRows} rows`}
        >
          Select All
        </Button>
      )}
      
      {selectedRows > 0 && onClearSelection && (
        <Button
          variant="outline"
          size="sm"
          onClick={onClearSelection}
          aria-label="Clear selection"
        >
          Clear Selection
        </Button>
      )}
      
      {children}
    </div>
  )
}

// Table Status for Screen Readers
export function TableStatus({
  loading,
  error,
  totalRows,
  filteredRows,
  sortColumn,
  sortDirection
}: {
  loading?: boolean
  error?: string
  totalRows: number
  filteredRows?: number
  sortColumn?: string
  sortDirection?: "asc" | "desc"
}) {
  const getStatusMessage = () => {
    if (loading) return "Loading table data"
    if (error) return `Error loading table: ${error}`
    
    let message = `Table with ${totalRows} rows`
    if (filteredRows !== undefined && filteredRows !== totalRows) {
      message += `, ${filteredRows} visible after filtering`
    }
    if (sortColumn && sortDirection) {
      message += `, sorted by ${sortColumn} ${sortDirection === "asc" ? "ascending" : "descending"}`
    }
    
    return message
  }

  return (
    <div
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
    >
      {getStatusMessage()}
    </div>
  )
}
