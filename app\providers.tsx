"use client"

import { NextAuthProvider } from "@/components/nextauth-provider"
import { LanguageProvider } from "@/components/language-provider"
import { Toaster } from "@/components/ui/toaster"

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <NextAuthProvider>
      <LanguageProvider>
        {children}
        <Toaster />
      </LanguageProvider>
    </NextAuthProvider>
  )
}
