import { test, expect } from '@playwright/test';

test.describe('Route Accessibility Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for navigation
    page.setDefaultTimeout(30000);
  });

  // All routes from the sidebar navigation
  const routes = [
    // Overview
    { path: '/dashboard', name: 'Dashboard', group: 'Overview' },
    
    // Sales & CRM
    { path: '/dashboard/customers', name: 'Customers', group: 'Sales & CRM' },
    { path: '/dashboard/companies', name: 'Companies', group: 'Sales & CRM' },
    { path: '/dashboard/leads', name: 'Leads', group: 'Sales & CRM' },
    { path: '/dashboard/opportunities', name: 'Opportunities', group: 'Sales & CRM' },
    { path: '/dashboard/deals', name: 'Deals', group: 'Sales & CRM' },
    { path: '/dashboard/proposals', name: 'Proposals', group: 'Sales & CRM' },
    { path: '/dashboard/vista-status', name: 'Vista Status', group: 'Sales & CRM' },
    { path: '/dashboard/shipping-status', name: 'Shipping Status', group: 'Sales & CRM' },
    { path: '/dashboard/inspection', name: 'Inspection', group: 'Sales & CRM' },
    
    // Management
    { path: '/dashboard/tasks', name: 'Tasks', group: 'Management' },
    { path: '/dashboard/reports', name: 'Reports', group: 'Management' },
    
    // Admin (will redirect to login for non-admin users)
    { path: '/dashboard/admin', name: 'Admin Dashboard', group: 'Admin' },
    { path: '/dashboard/admin/users', name: 'User Management', group: 'Admin' },
    { path: '/dashboard/admin/reports', name: 'System Reports', group: 'Admin' },
    { path: '/dashboard/settings', name: 'Settings', group: 'Admin' },
  ];

  test('should check all routes for 404 errors', async ({ page }) => {
    const results: { route: string; status: number; accessible: boolean; error?: string }[] = [];

    for (const route of routes) {
      try {
        console.log(`Testing route: ${route.path} (${route.name})`);
        
        const response = await page.goto(route.path);
        const status = response?.status() || 0;
        
        // For authenticated routes, we expect either:
        // - 200 (if accessible)
        // - 302/307 (redirect to login - this is OK)
        // - NOT 404 (which would indicate missing page)
        const accessible = status < 400;
        
        results.push({ 
          route: route.path, 
          status, 
          accessible,
        });
        
        console.log(`✅ ${route.path}: Status ${status} - ${accessible ? 'OK' : 'Issue'}`);
        
        // Brief wait between requests
        await page.waitForTimeout(1000);
        
      } catch (error) {
        console.log(`❌ Error accessing ${route.path}:`, error);
        results.push({ 
          route: route.path, 
          status: 0, 
          accessible: false, 
          error: String(error)
        });
      }
    }

    // Log summary
    const accessibleRoutes = results.filter(r => r.accessible).length;
    const totalRoutes = results.length;
    console.log(`\n📊 Route Accessibility Summary: ${accessibleRoutes}/${totalRoutes} routes accessible`);
    
    // Log problematic routes (404s and other errors)
    const problematicRoutes = results.filter(r => !r.accessible);
    if (problematicRoutes.length > 0) {
      console.log('\n🚨 Problematic routes:');
      problematicRoutes.forEach(route => {
        console.log(`  - ${route.route}: Status ${route.status}${route.error ? ` (${route.error})` : ''}`);
      });
    }

    // Group results by status
    const statusGroups = results.reduce((acc, result) => {
      const status = result.status;
      if (!acc[status]) acc[status] = [];
      acc[status].push(result.route);
      return acc;
    }, {} as Record<number, string[]>);

    console.log('\n📈 Routes by Status Code:');
    Object.entries(statusGroups).forEach(([status, routes]) => {
      console.log(`  ${status}: ${routes.length} routes`);
      routes.forEach(route => console.log(`    - ${route}`));
    });

    // The test passes if we have no 404s (missing pages)
    // Redirects to login (302/307) are expected and OK
    const missing404Routes = results.filter(r => r.status === 404);
    
    if (missing404Routes.length > 0) {
      console.log('\n❌ Found 404 routes (missing pages):');
      missing404Routes.forEach(route => {
        console.log(`  - ${route.route}`);
      });
    }

    // Assert that we have no 404 errors (missing pages)
    expect(missing404Routes.length).toBe(0);
    
    // Assert that we have a reasonable number of accessible routes
    // (Even with auth redirects, we should have some accessible routes)
    expect(accessibleRoutes).toBeGreaterThan(0);
  });

  test('should verify login page accessibility', async ({ page }) => {
    // The login page should always be accessible
    const response = await page.goto('/login');
    expect(response?.status()).toBe(200);
    
    // Should have login form elements
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
    await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"], button:has-text("Sign In")')).toBeVisible();
  });

  test('should verify homepage redirects correctly', async ({ page }) => {
    // Homepage should redirect to dashboard (which then redirects to login)
    await page.goto('/');

    // Should end up at login page with dashboard callback
    await page.waitForURL(/\/login/);
    expect(page.url()).toContain('/login');
    expect(page.url()).toContain('callbackUrl');
  });

  test('should test sidebar navigation links (when authenticated)', async ({ page }) => {
    // Note: This test would require authentication to work properly
    // For now, we'll test that the routes are accessible (they redirect to login as expected)

    const sidebarRoutes = [
      { name: 'Dashboard', path: '/dashboard' },
      { name: 'Customers', path: '/dashboard/customers' },
      { name: 'Companies', path: '/dashboard/companies' },
      { name: 'Leads', path: '/dashboard/leads' },
      { name: 'Opportunities', path: '/dashboard/opportunities' },
      { name: 'Deals', path: '/dashboard/deals' },
      { name: 'Proposals', path: '/dashboard/proposals' },
      { name: 'Tasks', path: '/dashboard/tasks' },
      { name: 'Reports', path: '/dashboard/reports' },
      // Note: Vista Status and Shipping Status should be hidden for non-admin users
      // Admin routes would require admin authentication
    ];

    for (const route of sidebarRoutes) {
      console.log(`Testing sidebar route: ${route.name} (${route.path})`);

      const response = await page.goto(route.path);
      const status = response?.status() || 0;

      // Should either be accessible (200) or redirect to login (not 404)
      expect(status).not.toBe(404);
      console.log(`✅ ${route.name}: Status ${status} - OK`);

      await page.waitForTimeout(500);
    }
  });

  test('should verify coming soon routes are accessible but show coming soon content', async ({ page }) => {
    const comingSoonRoutes = [
      { name: 'Vista Status', path: '/dashboard/vista-status' },
      { name: 'Shipping Status', path: '/dashboard/shipping-status' },
    ];

    for (const route of comingSoonRoutes) {
      console.log(`Testing coming soon route: ${route.name} (${route.path})`);

      const response = await page.goto(route.path);
      const status = response?.status() || 0;

      // Should be accessible (200) - not 404
      expect(status).toBe(200);

      // Should contain "Coming Soon" text
      await expect(page.locator('text=Coming Soon')).toBeVisible();
      console.log(`✅ ${route.name}: Status ${status} - Coming Soon page displayed`);

      await page.waitForTimeout(500);
    }
  });
});
