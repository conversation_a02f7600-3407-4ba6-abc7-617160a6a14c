"use client"

import { useEffect, useState } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'

interface RSCFallbackNavigationProps {
  children: React.ReactNode
}

export const RSCFallbackNavigation: React.FC<RSCFallbackNavigationProps> = ({ children }) => {
  const [hasNavigationError, setHasNavigationError] = useState(false)
  const [isRetrying, setIsRetrying] = useState(false)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // Listen for navigation errors
    const handleNavigationError = (event: any) => {
      console.error('Navigation error detected:', event)
      if (event.error?.message?.includes('payload') || event.error?.message?.includes('chunk')) {
        setHasNavigationError(true)
      }
    }

    // Listen for unhandled promise rejections (common with RSC payload errors)
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      if (event.reason?.message?.includes('payload') || event.reason?.message?.includes('chunk')) {
        console.error('RSC payload error detected:', event.reason)
        setHasNavigationError(true)
      }
    }

    window.addEventListener('error', handleNavigationError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)

    return () => {
      window.removeEventListener('error', handleNavigationError)
      window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    }
  }, [])

  const handleRetry = async () => {
    setIsRetrying(true)
    try {
      // Try to refresh the current route
      router.refresh()
      setTimeout(() => {
        setHasNavigationError(false)
        setIsRetrying(false)
      }, 1000)
    } catch (error) {
      console.error('Retry failed:', error)
      // Fallback to full page reload
      window.location.reload()
    }
  }

  const handleFallbackNavigation = (path: string) => {
    // Use window.location for reliable navigation when RSC fails
    window.location.href = path
  }

  if (hasNavigationError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-orange-50 to-red-50 p-4">
        <div className="max-w-lg w-full bg-white rounded-lg shadow-xl border border-orange-200 p-6">
          <div className="text-center">
            <AlertTriangle className="h-16 w-16 text-orange-500 mx-auto mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">Navigation System Error</h2>
            <p className="text-gray-600 mb-4">
              The page navigation system encountered an error. This is usually caused by a temporary issue with the server components.
            </p>
            <div className="mb-4 p-3 bg-orange-50 rounded-lg border border-orange-200">
              <p className="text-sm text-orange-800">
                <strong>Current page:</strong> {pathname}
              </p>
            </div>
            <div className="space-y-3">
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className="w-full px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : 'Retry Navigation'}
              </button>
              <button
                onClick={() => window.location.reload()}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh Page
              </button>
              <button
                onClick={() => handleFallbackNavigation('/dashboard')}
                className="w-full px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
              >
                <Home className="h-4 w-4" />
                Return to Dashboard
              </button>
            </div>
            <div className="mt-4 p-3 bg-gray-50 rounded-lg border">
              <p className="text-xs text-gray-600">
                If this problem persists, try clearing your browser cache or contact support.
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

export default RSCFallbackNavigation
