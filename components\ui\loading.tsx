"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { cva, type VariantProps } from "class-variance-authority"

const loadingVariants = cva(
  "inline-flex items-center justify-center",
  {
    variants: {
      variant: {
        spinner: "animate-spin rounded-full border-2 border-current border-t-transparent",
        dots: "space-x-1",
        pulse: "animate-pulse rounded-md bg-muted",
        skeleton: "animate-pulse rounded-md bg-muted",
      },
      size: {
        sm: "h-4 w-4",
        default: "h-6 w-6",
        lg: "h-8 w-8",
        xl: "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "spinner",
      size: "default",
    },
  }
)

export interface LoadingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  text?: string
}

const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ className, variant, size, text, ...props }, ref) => {
    if (variant === "dots") {
      return (
        <div
          ref={ref}
          className={cn("flex items-center justify-center", className)}
          {...props}
        >
          <div className="flex space-x-1">
            <div className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
            <div className="h-2 w-2 bg-current rounded-full animate-bounce"></div>
          </div>
          {text && <span className="ml-2 text-sm text-muted-foreground">{text}</span>}
        </div>
      )
    }

    return (
      <div
        ref={ref}
        className={cn("flex items-center justify-center", className)}
        {...props}
      >
        <div className={cn(loadingVariants({ variant, size }))} />
        {text && <span className="ml-2 text-sm text-muted-foreground">{text}</span>}
      </div>
    )
  }
)
Loading.displayName = "Loading"

// Preset loading components
function LoadingPage() {
  return (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center space-y-4">
        <Loading size="xl" />
        <p className="text-muted-foreground">Loading...</p>
      </div>
    </div>
  )
}

function LoadingCard() {
  return (
    <div className="rounded-lg border p-6 space-y-3">
      <div className="animate-pulse space-y-2">
        <div className="h-4 bg-muted rounded w-3/4"></div>
        <div className="h-4 bg-muted rounded w-1/2"></div>
        <div className="h-8 bg-muted rounded w-full"></div>
      </div>
    </div>
  )
}

function LoadingButton() {
  return (
    <div className="flex items-center justify-center">
      <Loading size="sm" />
      <span className="ml-2">Loading...</span>
    </div>
  )
}

export { Loading, LoadingPage, LoadingCard, LoadingButton, loadingVariants }
