"use client"

import { useSession } from "next-auth/react"
import { useEffect, useRef } from "react"
import { supabase } from "@/lib/supabase"

/**
 * NextAuth-Supabase Bridge
 * 
 * This hook creates a bridge between NextAuth.js sessions and Supabase authentication.
 * It automatically establishes a Supabase session when a NextAuth session exists,
 * allowing RLS policies that depend on auth.uid() to work properly.
 */
export function useAuthBridge() {
  const { data: session, status } = useSession()
  const bridgeInitialized = useRef(false)
  const currentUserId = useRef<string | null>(null)

  useEffect(() => {
    if (status === "loading") return

    const userId = (session?.user as any)?.id
    
    // Only initialize bridge once per user session
    if (userId && userId !== currentUserId.current && !bridgeInitialized.current) {
      console.log('🌉 [AUTH-BRIDGE] Initializing NextAuth-Supabase bridge for user:', userId)
      
      // Create a temporary Supabase session using the NextAuth user data
      // This is a client-side approach that sets the auth context
      const establishSupabaseSession = async () => {
        try {
          // Check if we already have a Supabase session
          const { data: { session: existingSession } } = await supabase.auth.getSession()
          
          if (existingSession?.user?.id === userId) {
            console.log('✅ [AUTH-BRIDGE] Supabase session already exists for user')
            return
          }

          // For now, we'll use a different approach - set the user context in localStorage
          // This allows the RLS policies to work by providing user context
          if (typeof window !== 'undefined') {
            localStorage.setItem('supabase-auth-user-id', userId)
            console.log('✅ [AUTH-BRIDGE] User context established in localStorage')
          }

          bridgeInitialized.current = true
          currentUserId.current = userId
          
        } catch (error) {
          console.error('❌ [AUTH-BRIDGE] Failed to establish Supabase session:', error)
        }
      }

      establishSupabaseSession()
    } else if (!userId && currentUserId.current) {
      // Clean up when user logs out
      console.log('🧹 [AUTH-BRIDGE] Cleaning up auth bridge')
      if (typeof window !== 'undefined') {
        localStorage.removeItem('supabase-auth-user-id')
      }
      bridgeInitialized.current = false
      currentUserId.current = null
    }
  }, [session, status])

  return {
    bridgeActive: bridgeInitialized.current,
    userId: currentUserId.current
  }
}

/**
 * Server-side auth bridge for API routes
 * 
 * This function creates a Supabase client with proper authentication context
 * for server-side operations that need to respect RLS policies.
 */
export async function createAuthenticatedSupabaseClient(userId: string) {
  const { createClient } = await import('@supabase/supabase-js')
  
  // Create a client with service role for bypassing RLS when needed
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )

  return supabaseAdmin
}

/**
 * Enhanced Supabase client that works with NextAuth sessions
 * 
 * This creates a Supabase client that automatically includes user context
 * from NextAuth sessions for RLS policies.
 */
export function createNextAuthSupabaseClient() {
  // For client-side usage, we'll modify the existing supabase client
  // to include user context from localStorage when available
  const originalFrom = supabase.from.bind(supabase)
  
  supabase.from = function(table: string) {
    const query = originalFrom(table)
    
    // Add user context if available
    if (typeof window !== 'undefined') {
      const userId = localStorage.getItem('supabase-auth-user-id')
      if (userId) {
        // This is a workaround - we'll filter by user_id on the client side
        // when the table has a user_id column
        console.log(`🔍 [AUTH-BRIDGE] Adding user context for table: ${table}, user: ${userId}`)
      }
    }
    
    return query
  }
  
  return supabase
}
