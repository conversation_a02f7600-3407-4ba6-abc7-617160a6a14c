"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  TrendingUp, TrendingDown, BarChart, PieChart, LineChart,
  Activity, Target, DollarSign, Users, Calendar, ArrowUp,
  ArrowDown, Minus, MoreHorizontal, Download, RefreshCw
} from "lucide-react"
import { cn } from "@/lib/utils"

// Data Types
export interface MetricData {
  label: string
  value: number | string
  change?: number
  changeType?: "increase" | "decrease" | "neutral"
  format?: "number" | "currency" | "percentage"
  target?: number
  icon?: React.ComponentType<{ className?: string }>
  color?: string
}

export interface ChartDataPoint {
  label: string
  value: number
  color?: string
  percentage?: number
}

export interface TimeSeriesData {
  date: string
  value: number
  label?: string
}

export interface TableSummaryData {
  totalRecords: number
  filteredRecords: number
  selectedRecords: number
  metrics: MetricData[]
  distribution: ChartDataPoint[]
  trends: TimeSeriesData[]
}

// Metric Card Component
export function MetricCard({
  metric,
  size = "default",
  showTarget = false,
  className
}: {
  metric: MetricData
  size?: "sm" | "default" | "lg"
  showTarget?: boolean
  className?: string
}) {
  const Icon = metric.icon
  
  const formatValue = (value: number | string, format?: string) => {
    if (typeof value === "string") return value
    
    switch (format) {
      case "currency":
        return new Intl.NumberFormat("en-US", {
          style: "currency",
          currency: "USD",
          minimumFractionDigits: 0,
          maximumFractionDigits: 0
        }).format(value)
      case "percentage":
        return `${value.toFixed(1)}%`
      case "number":
        return new Intl.NumberFormat("en-US").format(value)
      default:
        return value.toString()
    }
  }

  const getChangeIcon = () => {
    if (!metric.change) return null
    
    switch (metric.changeType) {
      case "increase":
        return <ArrowUp className="h-3 w-3" />
      case "decrease":
        return <ArrowDown className="h-3 w-3" />
      default:
        return <Minus className="h-3 w-3" />
    }
  }

  const getChangeColor = () => {
    switch (metric.changeType) {
      case "increase":
        return "text-success"
      case "decrease":
        return "text-destructive"
      default:
        return "text-muted-foreground"
    }
  }

  const sizeClasses = {
    sm: {
      card: "p-3",
      title: "text-xs",
      value: "text-lg",
      change: "text-xs",
      icon: "h-4 w-4"
    },
    default: {
      card: "p-4",
      title: "text-sm",
      value: "text-2xl",
      change: "text-sm",
      icon: "h-5 w-5"
    },
    lg: {
      card: "p-6",
      title: "text-base",
      value: "text-3xl",
      change: "text-base",
      icon: "h-6 w-6"
    }
  }

  const classes = sizeClasses[size]

  return (
    <Card className={cn("hover-lift transition-all duration-normal", className)}>
      <CardContent className={classes.card}>
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className={cn("font-medium text-muted-foreground", classes.title)}>
              {metric.label}
            </p>
            <p className={cn("font-bold text-foreground", classes.value)}>
              {formatValue(metric.value, metric.format)}
            </p>
            
            {metric.change !== undefined && (
              <div className={cn("flex items-center gap-1", classes.change, getChangeColor())}>
                {getChangeIcon()}
                <span>
                  {metric.change > 0 ? "+" : ""}{metric.change.toFixed(1)}%
                </span>
                <span className="text-muted-foreground">vs last period</span>
              </div>
            )}
            
            {showTarget && metric.target && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Target</span>
                  <span>{formatValue(metric.target, metric.format)}</span>
                </div>
                <Progress 
                  value={typeof metric.value === "number" ? (metric.value / metric.target) * 100 : 0} 
                  className="h-1"
                />
              </div>
            )}
          </div>
          
          {Icon && (
            <div className={cn(
              "p-2 rounded-lg",
              metric.color ? `bg-${metric.color}/10` : "bg-primary/10"
            )}>
              <Icon className={cn(
                classes.icon,
                metric.color ? `text-${metric.color}` : "text-primary"
              )} />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Simple Bar Chart Component
export function SimpleBarChart({
  data,
  title,
  height = 200,
  showValues = false,
  className
}: {
  data: ChartDataPoint[]
  title?: string
  height?: number
  showValues?: boolean
  className?: string
}) {
  const maxValue = Math.max(...data.map(d => d.value))
  
  return (
    <Card className={className}>
      {title && (
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent>
        <div className="space-y-3" style={{ height }}>
          {data.map((item, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-muted-foreground">{item.label}</span>
                {showValues && (
                  <span className="font-medium">{item.value.toLocaleString()}</span>
                )}
              </div>
              <div className="relative">
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={cn(
                      "h-2 rounded-full transition-all duration-500",
                      item.color ? `bg-${item.color}` : "bg-primary"
                    )}
                    style={{ width: `${(item.value / maxValue) * 100}%` }}
                  />
                </div>
                {item.percentage && (
                  <span className="absolute right-0 top-0 text-xs text-muted-foreground">
                    {item.percentage.toFixed(1)}%
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

// Simple Pie Chart Component (using CSS)
export function SimplePieChart({
  data,
  title,
  size = 120,
  showLegend = true,
  className
}: {
  data: ChartDataPoint[]
  title?: string
  size?: number
  showLegend?: boolean
  className?: string
}) {
  const total = data.reduce((sum, item) => sum + item.value, 0)
  const dataWithPercentages = data.map(item => ({
    ...item,
    percentage: (item.value / total) * 100
  }))

  // Generate CSS conic-gradient
  let cumulativePercentage = 0
  const gradientStops = dataWithPercentages.map((item, index) => {
    const start = cumulativePercentage
    cumulativePercentage += item.percentage
    const color = item.color || `hsl(${(index * 360) / data.length}, 70%, 50%)`
    return `${color} ${start}% ${cumulativePercentage}%`
  }).join(", ")

  const colors = dataWithPercentages.map((item, index) => 
    item.color || `hsl(${(index * 360) / data.length}, 70%, 50%)`
  )

  return (
    <Card className={className}>
      {title && (
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
      )}
      <CardContent>
        <div className="flex items-center gap-4">
          <div
            className="rounded-full flex-shrink-0"
            style={{
              width: size,
              height: size,
              background: `conic-gradient(${gradientStops})`
            }}
          />
          
          {showLegend && (
            <div className="space-y-2 flex-1">
              {dataWithPercentages.map((item, index) => (
                <div key={index} className="flex items-center gap-2 text-xs">
                  <div
                    className="w-3 h-3 rounded-full flex-shrink-0"
                    style={{ backgroundColor: colors[index] }}
                  />
                  <span className="text-muted-foreground flex-1">{item.label}</span>
                  <span className="font-medium">{item.percentage.toFixed(1)}%</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Trend Indicator Component
export function TrendIndicator({
  data,
  title,
  period = "7d",
  className
}: {
  data: TimeSeriesData[]
  title?: string
  period?: string
  className?: string
}) {
  const latestValue = data[data.length - 1]?.value || 0
  const previousValue = data[data.length - 2]?.value || 0
  const change = previousValue !== 0 ? ((latestValue - previousValue) / previousValue) * 100 : 0
  const isPositive = change > 0
  const isNeutral = Math.abs(change) < 0.1

  // Simple sparkline using CSS
  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const range = maxValue - minValue

  const points = data.map((point, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = range > 0 ? ((maxValue - point.value) / range) * 100 : 50
    return `${x},${y}`
  }).join(" ")

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div>
              {title && <p className="text-sm text-muted-foreground">{title}</p>}
              <p className="text-lg font-semibold">{latestValue.toLocaleString()}</p>
            </div>
            <div className={cn(
              "flex items-center gap-1 text-sm",
              isNeutral ? "text-muted-foreground" : isPositive ? "text-success" : "text-destructive"
            )}>
              {!isNeutral && (
                isPositive ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />
              )}
              <span>{change > 0 ? "+" : ""}{change.toFixed(1)}%</span>
            </div>
          </div>
          
          <div className="relative h-8 w-full">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <polyline
                points={points}
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                className={cn(
                  isNeutral ? "text-muted-foreground" : isPositive ? "text-success" : "text-destructive"
                )}
              />
            </svg>
          </div>
          
          <p className="text-xs text-muted-foreground">Last {period}</p>
        </div>
      </CardContent>
    </Card>
  )
}

// Table Summary Component
export function TableSummary({
  data,
  title = "Data Summary",
  onRefresh,
  onExport,
  className
}: {
  data: TableSummaryData
  title?: string
  onRefresh?: () => void
  onExport?: () => void
  className?: string
}) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm">{title}</CardTitle>
          <div className="flex items-center gap-2">
            {onRefresh && (
              <Button variant="ghost" size="sm" onClick={onRefresh}>
                <RefreshCw className="h-3 w-3" />
              </Button>
            )}
            {onExport && (
              <Button variant="ghost" size="sm" onClick={onExport}>
                <Download className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Record Counts */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className="text-lg font-semibold">{data.totalRecords.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">Total</p>
          </div>
          <div>
            <p className="text-lg font-semibold">{data.filteredRecords.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">Filtered</p>
          </div>
          <div>
            <p className="text-lg font-semibold">{data.selectedRecords.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">Selected</p>
          </div>
        </div>

        {/* Key Metrics */}
        {data.metrics.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Key Metrics
            </h4>
            <div className="grid grid-cols-1 gap-2">
              {data.metrics.slice(0, 3).map((metric, index) => (
                <div key={index} className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">{metric.label}</span>
                  <span className="font-medium">
                    {typeof metric.value === "number" 
                      ? metric.value.toLocaleString() 
                      : metric.value
                    }
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Distribution */}
        {data.distribution.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Distribution
            </h4>
            <div className="space-y-1">
              {data.distribution.slice(0, 4).map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="flex-1">
                    <div className="flex justify-between text-xs mb-1">
                      <span className="text-muted-foreground">{item.label}</span>
                      <span className="font-medium">{item.value}</span>
                    </div>
                    <Progress value={item.percentage || 0} className="h-1" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Data Insights Component
export function DataInsights({
  insights,
  className
}: {
  insights: Array<{
    type: "success" | "warning" | "info" | "error"
    title: string
    description: string
    action?: {
      label: string
      onClick: () => void
    }
  }>
  className?: string
}) {
  const getInsightIcon = (type: string) => {
    switch (type) {
      case "success":
        return <TrendingUp className="h-4 w-4 text-success" />
      case "warning":
        return <TrendingDown className="h-4 w-4 text-warning" />
      case "error":
        return <Activity className="h-4 w-4 text-destructive" />
      default:
        return <Target className="h-4 w-4 text-info" />
    }
  }

  const getInsightColor = (type: string) => {
    switch (type) {
      case "success":
        return "border-l-success bg-success/5"
      case "warning":
        return "border-l-warning bg-warning/5"
      case "error":
        return "border-l-destructive bg-destructive/5"
      default:
        return "border-l-info bg-info/5"
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm">Data Insights</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {insights.map((insight, index) => (
          <div
            key={index}
            className={cn(
              "p-3 border-l-2 rounded-r-lg",
              getInsightColor(insight.type)
            )}
          >
            <div className="flex items-start gap-3">
              {getInsightIcon(insight.type)}
              <div className="flex-1 space-y-1">
                <p className="text-sm font-medium">{insight.title}</p>
                <p className="text-xs text-muted-foreground">{insight.description}</p>
                {insight.action && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2 text-xs"
                    onClick={insight.action.onClick}
                  >
                    {insight.action.label}
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

// Trend Chart Component
interface TrendChartProps {
  data: TimeSeriesData[]
  title?: string
  height?: number
  color?: string
  showGrid?: boolean
  showTooltip?: boolean
}

export function TrendChart({
  data,
  title,
  height = 200,
  color = "#3B82F6",
  showGrid = true,
  showTooltip = true
}: TrendChartProps) {
  const maxValue = Math.max(...data.map(d => d.value))
  const minValue = Math.min(...data.map(d => d.value))
  const range = maxValue - minValue

  // Generate SVG path for the trend line
  const pathData = data.map((point, index) => {
    const x = (index / (data.length - 1)) * 100
    const y = 100 - ((point.value - minValue) / range) * 100
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`
  }).join(' ')

  return (
    <div className="w-full">
      {title && (
        <h4 className="text-sm font-medium mb-2">{title}</h4>
      )}
      <div className="relative" style={{ height }}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          className="absolute inset-0"
        >
          {/* Grid lines */}
          {showGrid && (
            <g className="opacity-20">
              {[0, 25, 50, 75, 100].map(y => (
                <line
                  key={y}
                  x1="0"
                  y1={y}
                  x2="100"
                  y2={y}
                  stroke="currentColor"
                  strokeWidth="0.5"
                />
              ))}
            </g>
          )}
          
          {/* Trend line */}
          <path
            d={pathData}
            fill="none"
            stroke={color}
            strokeWidth="2"
            vectorEffect="non-scaling-stroke"
          />
          
          {/* Data points */}
          {data.map((point, index) => {
            const x = (index / (data.length - 1)) * 100
            const y = 100 - ((point.value - minValue) / range) * 100
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="2"
                fill={color}
                vectorEffect="non-scaling-stroke"
              />
            )
          })}
        </svg>
        
        {/* Value labels */}
        <div className="absolute inset-0 flex items-end justify-between text-xs text-muted-foreground">
          {data.map((point, index) => (
            <div key={index} className="flex flex-col items-center">
              <span className="mb-1">{point.value.toLocaleString()}</span>
              <span>{point.label || point.date}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
