# Create .env.local file with Supabase credentials
$envContent = @"
NEXT_PUBLIC_SUPABASE_URL=https://ojhtdwrzolfwbiwrprok.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9qaHRkd3J6b2xmd2Jpd3Jwcm9rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MDM3NzQ1NzAsImV4cCI6MjAxOTM1MDU3MH0.Wd_GHxqDHZKBXc9HqaO3YYwG5QUvAVnE3KAx6HgsDtM
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9qaHRkd3J6b2xmd2Jpd3Jwcm9rIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcwMzc3NDU3MCwiZXhwIjoyMDE5MzUwNTcwfQ.BmbnY1SWB-xC4qBPyqq9nJZM_0Ck_P_gKVzJvGqQXYE
DB_PASSWORD=2Icaf90uVwuYzSlA
"@

# Write the content to .env.local
$envContent | Out-File -FilePath ".env.local" -Encoding UTF8

Write-Host "✅ Created .env.local file with Supabase credentials" 