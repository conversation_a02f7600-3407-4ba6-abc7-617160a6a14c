import { useState, useEffect, useMemo } from 'react'
import { useOptimizedData } from '@/hooks/use-optimized-data'
import { 
  PipelineStageData, 
  LeadSourceData, 
  CustomerDistributionData, 
  RevenueData,
  ActivityData,
  TimeSeriesDataPoint,
  ChartFilters 
} from '@/components/charts/types'
import { CHART_COLORS } from '@/components/charts/chart-config'

// Hook for Sales Pipeline Analytics (Updated to use opportunities instead of deals)
export function usePipelineData(filters?: ChartFilters) {
  const { data: opportunities, loading } = useOptimizedData<any>({
    table: "opportunities",
    requiresAuth: true,
    realtime: true,
  })

  const pipelineData = useMemo((): PipelineStageData[] => {
    if (!opportunities || opportunities.length === 0) return []

    const stageGroups = opportunities.reduce((acc: any, opportunity: any) => {
      const stage = opportunity.stage || 'Unknown'
      if (!acc[stage]) {
        acc[stage] = { count: 0, totalValue: 0, totalProbability: 0 }
      }
      acc[stage].count += 1
      acc[stage].totalValue += opportunity.value || 0
      acc[stage].totalProbability += opportunity.probability || 0
      return acc
    }, {})

    return Object.entries(stageGroups).map(([stage, data]: [string, any]) => ({
      stage,
      count: data.count,
      value: data.totalValue,
      probability: data.totalProbability / data.count,
      color: CHART_COLORS.pipeline[stage.toLowerCase().replace(' ', '-') as keyof typeof CHART_COLORS.pipeline] || CHART_COLORS.muted
    }))
  }, [opportunities])

  return { data: pipelineData, loading }
}

// Hook for Lead Source Analytics
export function useLeadSourceData(filters?: ChartFilters) {
  const { data: leads, loading } = useOptimizedData<any>({
    table: "leads",
    requiresAuth: true,
    realtime: true,
  })

  const leadSourceData = useMemo((): LeadSourceData[] => {
    if (!leads || leads.length === 0) return []

    const sourceGroups = leads.reduce((acc: any, lead: any) => {
      const source = lead.source || 'Other'
      if (!acc[source]) {
        acc[source] = { total: 0, converted: 0 }
      }
      acc[source].total += 1
      if (lead.status === 'Converted') {
        acc[source].converted += 1
      }
      return acc
    }, {})

    return Object.entries(sourceGroups).map(([source, data]: [string, any]) => ({
      source,
      count: data.total,
      converted: data.converted,
      conversionRate: data.total > 0 ? (data.converted / data.total) * 100 : 0,
      color: CHART_COLORS.leadSources[source.toLowerCase().replace(' ', '-') as keyof typeof CHART_COLORS.leadSources] || CHART_COLORS.muted
    }))
  }, [leads])

  return { data: leadSourceData, loading }
}

// Hook for Customer Distribution Analytics
export function useCustomerDistributionData(filters?: ChartFilters) {
  const { data: customers, loading } = useOptimizedData<any>({
    table: "customers",
    requiresAuth: true,
    realtime: true,
  })

  const distributionData = useMemo((): CustomerDistributionData[] => {
    if (!customers || customers.length === 0) return []

    const tierGroups = customers.reduce((acc: any, customer: any) => {
      const tier = customer.customer_tier || 'Bronze'
      acc[tier] = (acc[tier] || 0) + 1
      return acc
    }, {})

    const total = customers.length

    return Object.entries(tierGroups).map(([tier, count]: [string, any]) => ({
      category: tier,
      count,
      percentage: (count / total) * 100,
      color: CHART_COLORS.customerTiers[tier.toLowerCase() as keyof typeof CHART_COLORS.customerTiers] || CHART_COLORS.muted
    }))
  }, [customers])

  return { data: distributionData, loading }
}

// Hook for Revenue Analytics (Updated to use opportunities instead of deals)
export function useRevenueData(filters?: ChartFilters) {
  const { data: opportunities, loading } = useOptimizedData<any>({
    table: "opportunities",
    requiresAuth: true,
    realtime: true,
  })

  const revenueData = useMemo((): RevenueData[] => {
    if (!opportunities || opportunities.length === 0) return []

    // Group opportunities by month for revenue trends (using won opportunities)
    const monthlyRevenue = opportunities
      .filter((opportunity: any) => opportunity.stage === 'closed_won' && opportunity.expected_close_date)
      .reduce((acc: any, opportunity: any) => {
        const date = new Date(opportunity.expected_close_date)
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        acc[monthKey] = (acc[monthKey] || 0) + (opportunity.value || 0)
        return acc
      }, {})

    return Object.entries(monthlyRevenue)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([period, revenue]: [string, any]) => ({
        period,
        revenue,
        target: revenue * 1.1, // 10% growth target
        growth: 0 // Calculate based on previous period
      }))
  }, [opportunities])

  return { data: revenueData, loading }
}

// Hook for Activity Analytics
export function useActivityData(filters?: ChartFilters) {
  const { data: tasks, loading } = useOptimizedData<any>({
    table: "tasks",
    requiresAuth: true,
    realtime: true,
  })

  const activityData = useMemo((): ActivityData[] => {
    if (!tasks || tasks.length === 0) return []

    const activityGroups = tasks.reduce((acc: any, task: any) => {
      const type = task.priority || 'Medium'
      if (!acc[type]) {
        acc[type] = { completed: 0, pending: 0, overdue: 0 }
      }
      
      if (task.status === 'Completed') {
        acc[type].completed += 1
      } else if (task.due_date && new Date(task.due_date) < new Date()) {
        acc[type].overdue += 1
      } else {
        acc[type].pending += 1
      }
      
      return acc
    }, {})

    return Object.entries(activityGroups).map(([type, data]: [string, any]) => ({
      type,
      completed: data.completed,
      pending: data.pending,
      overdue: data.overdue
    }))
  }, [tasks])

  return { data: activityData, loading }
}

// Hook for Time Series Data (General Purpose)
export function useTimeSeriesData(table: string, dateField: string, valueField: string, filters?: ChartFilters) {
  const { data, loading } = useOptimizedData<any>({
    table,
    requiresAuth: true,
    realtime: true,
  })

  const timeSeriesData = useMemo((): TimeSeriesDataPoint[] => {
    if (!data || data.length === 0) return []

    const groupedData = data.reduce((acc: any, item: any) => {
      if (!item[dateField]) return acc
      
      const date = new Date(item[dateField]).toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + (item[valueField] || 1)
      return acc
    }, {})

    return Object.entries(groupedData)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([date, value]: [string, any]) => ({
        date,
        value,
        label: new Date(date).toLocaleDateString()
      }))
  }, [data, dateField, valueField])

  return { data: timeSeriesData, loading }
}
