"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { 
  Accessibility, CheckCircle, AlertCircle, XCircle, 
  Eye, Keyboard, Volume2, MousePointer, Zap, Settings
} from "lucide-react"
import { cn } from "@/lib/utils"

// Accessibility Test Types
export interface AccessibilityTest {
  id: string
  name: string
  category: "visual" | "keyboard" | "screen-reader" | "motor" | "cognitive"
  description: string
  wcagLevel: "A" | "AA" | "AAA"
  wcagCriteria: string
  status: "pass" | "fail" | "warning" | "not-tested"
  automated: boolean
  result?: {
    score: number
    issues: AccessibilityIssue[]
    recommendations: string[]
  }
}

export interface AccessibilityIssue {
  id: string
  severity: "critical" | "serious" | "moderate" | "minor"
  element: string
  description: string
  wcagCriteria: string
  recommendation: string
  codeExample?: string
}

export interface AccessibilityReport {
  overall: {
    score: number
    level: "A" | "AA" | "AAA" | "fail"
    passedTests: number
    totalTests: number
  }
  categories: {
    visual: { score: number; tests: AccessibilityTest[] }
    keyboard: { score: number; tests: AccessibilityTest[] }
    screenReader: { score: number; tests: AccessibilityTest[] }
    motor: { score: number; tests: AccessibilityTest[] }
    cognitive: { score: number; tests: AccessibilityTest[] }
  }
  issues: AccessibilityIssue[]
  lastTested: Date
}

// Mock accessibility test data
const mockAccessibilityReport: AccessibilityReport = {
  overall: {
    score: 87,
    level: "AA",
    passedTests: 26,
    totalTests: 30
  },
  categories: {
    visual: {
      score: 92,
      tests: [
        {
          id: "color-contrast",
          name: "Color Contrast",
          category: "visual",
          description: "Text has sufficient contrast against background",
          wcagLevel: "AA",
          wcagCriteria: "1.4.3",
          status: "pass",
          automated: true,
          result: { score: 95, issues: [], recommendations: [] }
        },
        {
          id: "focus-indicators",
          name: "Focus Indicators",
          category: "visual",
          description: "Interactive elements have visible focus indicators",
          wcagLevel: "AA",
          wcagCriteria: "2.4.7",
          status: "warning",
          automated: false,
          result: { 
            score: 85, 
            issues: [
              {
                id: "focus-1",
                severity: "moderate",
                element: "button.ghost",
                description: "Ghost buttons have low contrast focus indicators",
                wcagCriteria: "2.4.7",
                recommendation: "Increase focus ring contrast or add background color on focus"
              }
            ], 
            recommendations: ["Enhance focus ring visibility", "Test with high contrast mode"] 
          }
        }
      ]
    },
    keyboard: {
      score: 88,
      tests: [
        {
          id: "keyboard-navigation",
          name: "Keyboard Navigation",
          category: "keyboard",
          description: "All interactive elements are keyboard accessible",
          wcagLevel: "A",
          wcagCriteria: "2.1.1",
          status: "pass",
          automated: false
        },
        {
          id: "tab-order",
          name: "Tab Order",
          category: "keyboard",
          description: "Tab order follows logical sequence",
          wcagLevel: "A",
          wcagCriteria: "2.4.3",
          status: "pass",
          automated: false
        }
      ]
    },
    screenReader: {
      score: 82,
      tests: [
        {
          id: "aria-labels",
          name: "ARIA Labels",
          category: "screen-reader",
          description: "Interactive elements have appropriate ARIA labels",
          wcagLevel: "A",
          wcagCriteria: "4.1.2",
          status: "warning",
          automated: true,
          result: {
            score: 80,
            issues: [
              {
                id: "aria-1",
                severity: "serious",
                element: "button[data-testid='menu-toggle']",
                description: "Button missing accessible name",
                wcagCriteria: "4.1.2",
                recommendation: "Add aria-label or aria-labelledby attribute"
              }
            ],
            recommendations: ["Add missing ARIA labels", "Test with screen readers"]
          }
        }
      ]
    },
    motor: {
      score: 90,
      tests: [
        {
          id: "touch-targets",
          name: "Touch Target Size",
          category: "motor",
          description: "Touch targets are at least 44x44 pixels",
          wcagLevel: "AA",
          wcagCriteria: "2.5.5",
          status: "pass",
          automated: true
        }
      ]
    },
    cognitive: {
      score: 85,
      tests: [
        {
          id: "consistent-navigation",
          name: "Consistent Navigation",
          category: "cognitive",
          description: "Navigation is consistent across pages",
          wcagLevel: "AA",
          wcagCriteria: "3.2.3",
          status: "pass",
          automated: false
        }
      ]
    }
  },
  issues: [],
  lastTested: new Date()
}

// Accessibility Testing Component
export function AccessibilityTesting({
  report = mockAccessibilityReport,
  onRunTests,
  onFixIssue,
  className
}: {
  report?: AccessibilityReport
  onRunTests?: () => Promise<void>
  onFixIssue?: (issueId: string) => void
  className?: string
}) {
  const [selectedCategory, setSelectedCategory] = React.useState<string>("all")
  const [isRunning, setIsRunning] = React.useState(false)

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pass": return <CheckCircle className="h-4 w-4 text-green-600" />
      case "warning": return <AlertCircle className="h-4 w-4 text-yellow-600" />
      case "fail": return <XCircle className="h-4 w-4 text-red-600" />
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "bg-red-100 text-red-800 border-red-200"
      case "serious": return "bg-orange-100 text-orange-800 border-orange-200"
      case "moderate": return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "minor": return "bg-blue-100 text-blue-800 border-blue-200"
      default: return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "visual": return <Eye className="h-4 w-4" />
      case "keyboard": return <Keyboard className="h-4 w-4" />
      case "screen-reader": return <Volume2 className="h-4 w-4" />
      case "motor": return <MousePointer className="h-4 w-4" />
      case "cognitive": return <Settings className="h-4 w-4" />
      default: return <Accessibility className="h-4 w-4" />
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600"
    if (score >= 80) return "text-yellow-600"
    if (score >= 70) return "text-orange-600"
    return "text-red-600"
  }

  const handleRunTests = async () => {
    setIsRunning(true)
    try {
      await onRunTests?.()
    } finally {
      setIsRunning(false)
    }
  }

  const allTests = Object.values(report.categories).flatMap(category => category.tests)
  const filteredTests = selectedCategory === "all" 
    ? allTests 
    : report.categories[selectedCategory as keyof typeof report.categories]?.tests || []

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="heading-2 text-foreground flex items-center gap-2">
            <Accessibility className="h-6 w-6" />
            Accessibility Testing
          </h2>
          <p className="body-small text-muted-foreground mt-1">
            WCAG 2.1 compliance testing and validation
          </p>
        </div>
        <Button 
          onClick={handleRunTests} 
          disabled={isRunning}
          className="gap-2"
        >
          <Zap className="h-4 w-4" />
          {isRunning ? "Running Tests..." : "Run Tests"}
        </Button>
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Overall Accessibility Score</span>
            <Badge 
              variant={report.overall.level === "AAA" ? "default" : "secondary"}
              className={cn(
                report.overall.level === "AAA" && "bg-green-100 text-green-800",
                report.overall.level === "AA" && "bg-blue-100 text-blue-800",
                report.overall.level === "A" && "bg-yellow-100 text-yellow-800"
              )}
            >
              WCAG {report.overall.level}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className={cn("text-4xl font-bold", getScoreColor(report.overall.score))}>
                {report.overall.score}%
              </div>
              <p className="text-sm text-muted-foreground">
                {report.overall.passedTests} of {report.overall.totalTests} tests passed
              </p>
            </div>
            
            <Progress value={report.overall.score} className="h-2" />
            
            <div className="grid grid-cols-5 gap-4 text-center">
              {Object.entries(report.categories).map(([category, data]) => (
                <div key={category}>
                  <div className="flex items-center justify-center mb-1">
                    {getCategoryIcon(category)}
                  </div>
                  <div className={cn("text-lg font-semibold", getScoreColor(data.score))}>
                    {data.score}%
                  </div>
                  <p className="text-xs text-muted-foreground capitalize">
                    {category.replace(/([A-Z])/g, ' $1').trim()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        <Button
          variant={selectedCategory === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setSelectedCategory("all")}
        >
          All Tests
        </Button>
        {Object.entries(report.categories).map(([category, data]) => (
          <Button
            key={category}
            variant={selectedCategory === category ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="gap-2"
          >
            {getCategoryIcon(category)}
            {category.charAt(0).toUpperCase() + category.slice(1).replace(/([A-Z])/g, ' $1')}
            <Badge variant="secondary" className="ml-1">
              {data.tests.length}
            </Badge>
          </Button>
        ))}
      </div>

      {/* Test Results */}
      <div className="space-y-4">
        {filteredTests.map((test) => (
          <Card key={test.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(test.status)}
                    <h4 className="font-medium">{test.name}</h4>
                    <Badge variant="outline" className="text-xs">
                      WCAG {test.wcagLevel}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {test.wcagCriteria}
                    </Badge>
                    {test.automated && (
                      <Badge variant="secondary" className="text-xs">
                        Automated
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {test.description}
                  </p>
                  
                  {test.result && test.result.issues.length > 0 && (
                    <div className="space-y-2 mt-3">
                      <h5 className="text-sm font-medium">Issues Found:</h5>
                      {test.result.issues.map((issue) => (
                        <div key={issue.id} className="p-3 border rounded-lg bg-muted/30">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge className={getSeverityColor(issue.severity)}>
                                  {issue.severity}
                                </Badge>
                                <code className="text-xs bg-muted px-1 rounded">
                                  {issue.element}
                                </code>
                              </div>
                              <p className="text-sm">{issue.description}</p>
                              <p className="text-xs text-muted-foreground mt-1">
                                <strong>Fix:</strong> {issue.recommendation}
                              </p>
                              {issue.codeExample && (
                                <pre className="text-xs bg-muted p-2 rounded mt-2 overflow-x-auto">
                                  <code>{issue.codeExample}</code>
                                </pre>
                              )}
                            </div>
                            {onFixIssue && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => onFixIssue(issue.id)}
                              >
                                Fix
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {test.result && test.result.recommendations.length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-sm font-medium mb-1">Recommendations:</h5>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {test.result.recommendations.map((rec, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-primary">•</span>
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
                
                {test.result && (
                  <div className="text-right">
                    <div className={cn("text-lg font-semibold", getScoreColor(test.result.score))}>
                      {test.result.score}%
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Testing Guidelines */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Guidelines</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Keyboard className="h-4 w-4" />
                Keyboard Testing
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Tab through all interactive elements</li>
                <li>• Test arrow key navigation in menus</li>
                <li>• Verify Enter/Space activate buttons</li>
                <li>• Check Escape closes modals/menus</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Volume2 className="h-4 w-4" />
                Screen Reader Testing
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Test with NVDA, JAWS, or VoiceOver</li>
                <li>• Verify all content is announced</li>
                <li>• Check heading structure (H1-H6)</li>
                <li>• Test form labels and instructions</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Visual Testing
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Check color contrast ratios</li>
                <li>• Test with high contrast mode</li>
                <li>• Verify focus indicators are visible</li>
                <li>• Test at 200% zoom level</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <MousePointer className="h-4 w-4" />
                Motor Testing
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Verify touch targets are 44x44px</li>
                <li>• Test with voice control software</li>
                <li>• Check for adequate spacing</li>
                <li>• Test drag and drop alternatives</li>
              </ul>
            </div>
          </div>
          
          <Separator />
          
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              Last tested: {report.lastTested.toLocaleDateString()} at {report.lastTested.toLocaleTimeString()}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
