"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

// Accessibility Types
export interface AccessibilityProps {
  "aria-label"?: string
  "aria-labelledby"?: string
  "aria-describedby"?: string
  "aria-required"?: boolean
  "aria-invalid"?: boolean
  "aria-expanded"?: boolean
  "aria-controls"?: string
  "aria-activedescendant"?: string
  role?: string
  tabIndex?: number
}

export interface FormAccessibilityConfig {
  announceErrors?: boolean
  announceSuccess?: boolean
  focusFirstError?: boolean
  skipLinks?: boolean
  highContrast?: boolean
  reducedMotion?: boolean
}

// Screen Reader Announcements
export function ScreenReaderAnnouncement({ 
  message, 
  priority = "polite" 
}: { 
  message: string
  priority?: "polite" | "assertive" 
}) {
  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {message}
    </div>
  )
}

// Skip Link Component
export function SkipLink({ 
  href, 
  children 
}: { 
  href: string
  children: React.ReactNode 
}) {
  return (
    <a
      href={href}
      className={cn(
        "absolute left-[-9999px] top-auto w-1 h-1 overflow-hidden",
        "focus:left-6 focus:top-6 focus:w-auto focus:h-auto focus:overflow-visible",
        "bg-primary text-primary-foreground px-4 py-2 rounded-md",
        "text-sm font-medium z-50 transition-all"
      )}
    >
      {children}
    </a>
  )
}

// Focus Management Hook
export function useFocusManagement() {
  const focusFirstError = React.useCallback((formElement: HTMLElement) => {
    const firstError = formElement.querySelector('[aria-invalid="true"]') as HTMLElement
    if (firstError) {
      firstError.focus()
      firstError.scrollIntoView({ behavior: "smooth", block: "center" })
    }
  }, [])

  const focusElement = React.useCallback((selector: string) => {
    const element = document.querySelector(selector) as HTMLElement
    if (element) {
      element.focus()
      element.scrollIntoView({ behavior: "smooth", block: "center" })
    }
  }, [])

  const trapFocus = React.useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== "Tab") return

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement.focus()
          e.preventDefault()
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement.focus()
          e.preventDefault()
        }
      }
    }

    container.addEventListener("keydown", handleTabKey)
    firstElement?.focus()

    return () => {
      container.removeEventListener("keydown", handleTabKey)
    }
  }, [])

  return {
    focusFirstError,
    focusElement,
    trapFocus
  }
}

// Keyboard Navigation Hook
export function useKeyboardNavigation() {
  const handleArrowNavigation = React.useCallback((
    e: React.KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    onIndexChange: (index: number) => void
  ) => {
    switch (e.key) {
      case "ArrowDown":
        e.preventDefault()
        const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0
        onIndexChange(nextIndex)
        items[nextIndex]?.focus()
        break
      case "ArrowUp":
        e.preventDefault()
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1
        onIndexChange(prevIndex)
        items[prevIndex]?.focus()
        break
      case "Home":
        e.preventDefault()
        onIndexChange(0)
        items[0]?.focus()
        break
      case "End":
        e.preventDefault()
        const lastIndex = items.length - 1
        onIndexChange(lastIndex)
        items[lastIndex]?.focus()
        break
    }
  }, [])

  return { handleArrowNavigation }
}

// Accessible Form Field Wrapper
export function AccessibleFormField({
  children,
  label,
  description,
  error,
  success,
  required = false,
  className
}: {
  children: React.ReactElement
  label?: string
  description?: string
  error?: string
  success?: string
  required?: boolean
  className?: string
}) {
  const fieldId = React.useId()
  const descriptionId = React.useId()
  const errorId = React.useId()
  const successId = React.useId()

  const describedBy = [
    description && descriptionId,
    error && errorId,
    success && successId
  ].filter(Boolean).join(" ")

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label 
          htmlFor={fieldId}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {label}
          {required && (
            <span className="text-destructive ml-1" aria-label="required">
              *
            </span>
          )}
        </label>
      )}
      
      {description && (
        <p id={descriptionId} className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
      
      {React.cloneElement(children, {
        id: fieldId,
        "aria-describedby": describedBy || undefined,
        "aria-required": required,
        "aria-invalid": !!error
      })}
      
      {error && (
        <p 
          id={errorId} 
          className="text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {error}
        </p>
      )}
      
      {success && !error && (
        <p 
          id={successId} 
          className="text-xs text-success"
          role="status"
          aria-live="polite"
        >
          {success}
        </p>
      )}
    </div>
  )
}

// High Contrast Mode Detection
export function useHighContrastMode() {
  const [isHighContrast, setIsHighContrast] = React.useState(false)

  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-contrast: high)")
    setIsHighContrast(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches)
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  return isHighContrast
}

// Reduced Motion Detection
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false)

  React.useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)")
    setPrefersReducedMotion(mediaQuery.matches)

    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches)
    }

    mediaQuery.addEventListener("change", handleChange)
    return () => mediaQuery.removeEventListener("change", handleChange)
  }, [])

  return prefersReducedMotion
}

// Form Accessibility Provider
export const FormAccessibilityContext = React.createContext<FormAccessibilityConfig>({
  announceErrors: true,
  announceSuccess: true,
  focusFirstError: true,
  skipLinks: true,
  highContrast: false,
  reducedMotion: false
})

export function FormAccessibilityProvider({
  children,
  config
}: {
  children: React.ReactNode
  config?: Partial<FormAccessibilityConfig>
}) {
  const isHighContrast = useHighContrastMode()
  const prefersReducedMotion = useReducedMotion()

  const contextValue = React.useMemo(() => ({
    announceErrors: true,
    announceSuccess: true,
    focusFirstError: true,
    skipLinks: true,
    highContrast: isHighContrast,
    reducedMotion: prefersReducedMotion,
    ...config
  }), [isHighContrast, prefersReducedMotion, config])

  return (
    <FormAccessibilityContext.Provider value={contextValue}>
      {children}
    </FormAccessibilityContext.Provider>
  )
}

export function useFormAccessibility() {
  const context = React.useContext(FormAccessibilityContext)
  if (!context) {
    throw new Error("useFormAccessibility must be used within FormAccessibilityProvider")
  }
  return context
}

// ARIA Live Region for Form Announcements
export function FormAnnouncementRegion() {
  const [announcement, setAnnouncement] = React.useState("")
  const [priority, setPriority] = React.useState<"polite" | "assertive">("polite")

  React.useEffect(() => {
    const handleFormAnnouncement = (event: CustomEvent) => {
      setAnnouncement(event.detail.message)
      setPriority(event.detail.priority || "polite")
      
      // Clear announcement after a delay
      setTimeout(() => setAnnouncement(""), 1000)
    }

    window.addEventListener("form-announcement", handleFormAnnouncement as EventListener)
    return () => {
      window.removeEventListener("form-announcement", handleFormAnnouncement as EventListener)
    }
  }, [])

  return (
    <div
      aria-live={priority}
      aria-atomic="true"
      className="sr-only"
    >
      {announcement}
    </div>
  )
}

// Utility function to announce messages
export function announceToScreenReader(message: string, priority: "polite" | "assertive" = "polite") {
  const event = new CustomEvent("form-announcement", {
    detail: { message, priority }
  })
  window.dispatchEvent(event)
}

// Accessible Form Validation Messages
export function ValidationMessage({
  type,
  message,
  fieldName
}: {
  type: "error" | "success" | "warning"
  message: string
  fieldName?: string
}) {
  const id = React.useId()
  
  React.useEffect(() => {
    if (type === "error") {
      announceToScreenReader(`${fieldName ? `${fieldName}: ` : ""}${message}`, "assertive")
    } else if (type === "success") {
      announceToScreenReader(`${fieldName ? `${fieldName}: ` : ""}${message}`, "polite")
    }
  }, [message, type, fieldName])

  const roleMap = {
    error: "alert",
    success: "status",
    warning: "alert"
  }

  const colorMap = {
    error: "text-destructive",
    success: "text-success",
    warning: "text-warning"
  }

  return (
    <p
      id={id}
      role={roleMap[type]}
      aria-live={type === "error" ? "assertive" : "polite"}
      className={cn("text-xs", colorMap[type])}
    >
      {message}
    </p>
  )
}

// Accessible Form Group with Fieldset
export function AccessibleFormGroup({
  legend,
  description,
  children,
  className
}: {
  legend: string
  description?: string
  children: React.ReactNode
  className?: string
}) {
  const descriptionId = React.useId()

  return (
    <fieldset 
      className={cn("space-y-4", className)}
      aria-describedby={description ? descriptionId : undefined}
    >
      <legend className="text-sm font-medium leading-none">
        {legend}
      </legend>
      
      {description && (
        <p id={descriptionId} className="text-xs text-muted-foreground">
          {description}
        </p>
      )}
      
      {children}
    </fieldset>
  )
}
