import { CustomerFormData } from '@/app/types/customer'
import { CustomerExportOptions, CustomerImportMapping } from '@/components/customers/customer-import-export'

// File processing utilities
export class FileProcessor {
  // Parse CSV content
  static parseCSV(content: string, delimiter: string = ','): string[][] {
    const lines = content.split('\n').filter(line => line.trim())
    const result: string[][] = []
    
    for (const line of lines) {
      const row: string[] = []
      let current = ''
      let inQuotes = false
      let i = 0
      
      while (i < line.length) {
        const char = line[i]
        const nextChar = line[i + 1]
        
        if (char === '"') {
          if (inQuotes && nextChar === '"') {
            // Escaped quote
            current += '"'
            i += 2
            continue
          } else {
            // Toggle quote state
            inQuotes = !inQuotes
          }
        } else if (char === delimiter && !inQuotes) {
          // End of field
          row.push(current.trim())
          current = ''
        } else {
          current += char
        }
        
        i++
      }
      
      // Add the last field
      row.push(current.trim())
      result.push(row)
    }
    
    return result
  }

  // Parse JSON content
  static parseJSON(content: string): any[] {
    try {
      const data = JSON.parse(content)
      if (!Array.isArray(data)) {
        throw new Error('JSON must contain an array of objects')
      }
      return data
    } catch (error) {
      throw new Error(`Invalid JSON format: ${(error as Error).message}`)
    }
  }

  // Parse Excel content (simplified - in production use a library like xlsx)
  static parseExcel(content: string): string[][] {
    // For now, treat as tab-separated values
    return this.parseCSV(content, '\t')
  }

  // Detect file format
  static detectFormat(filename: string): 'csv' | 'json' | 'excel' | 'unknown' {
    const extension = filename.toLowerCase().split('.').pop()
    
    switch (extension) {
      case 'csv':
        return 'csv'
      case 'json':
        return 'json'
      case 'xlsx':
      case 'xls':
        return 'excel'
      default:
        return 'unknown'
    }
  }

  // Validate file size
  static validateFileSize(file: File, maxSizeMB: number = 10): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    return file.size <= maxSizeBytes
  }

  // Detect column types from sample data
  static detectColumnTypes(data: string[][], headerIndex: number = 0): Record<string, string> {
    const headers = data[headerIndex]
    const types: Record<string, string> = {}
    
    headers.forEach((header, columnIndex) => {
      const sampleValues = data
        .slice(headerIndex + 1, headerIndex + 11) // Take up to 10 samples
        .map(row => row[columnIndex])
        .filter(value => value && value.trim() !== '')
      
      if (sampleValues.length === 0) {
        types[header] = 'text'
        return
      }
      
      // Check for email
      if (sampleValues.every(val => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val))) {
        types[header] = 'email'
      }
      // Check for numbers
      else if (sampleValues.every(val => !isNaN(Number(val.replace(/[,$]/g, ''))))) {
        types[header] = 'number'
      }
      // Check for dates
      else if (sampleValues.every(val => !isNaN(Date.parse(val)))) {
        types[header] = 'date'
      }
      // Check for boolean
      else if (sampleValues.every(val => ['true', 'false', '1', '0', 'yes', 'no'].includes(val.toLowerCase()))) {
        types[header] = 'boolean'
      }
      // Default to text
      else {
        types[header] = 'text'
      }
    })
    
    return types
  }
}

// Data validation utilities
export class DataValidator {
  // Validate email format
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Validate phone number format
  static isValidPhone(phone: string): boolean {
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '')
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
    return phoneRegex.test(cleanPhone)
  }

  // Validate URL format
  static isValidURL(url: string): boolean {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  // Validate required fields
  static validateRequired(value: any): boolean {
    return value !== null && value !== undefined && value.toString().trim() !== ''
  }

  // Validate customer data completeness
  static validateCustomerData(customer: Partial<CustomerFormData>): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Required fields
    const requiredFields = ['contact_person', 'email', 'company', 'city', 'country']
    
    requiredFields.forEach(field => {
      if (!this.validateRequired(customer[field as keyof CustomerFormData])) {
        errors.push(`${field} is required`)
      }
    })

    // Email validation
    if (customer.email && !this.isValidEmail(customer.email)) {
      errors.push('Invalid email format')
    }

    // Phone validation
    if (customer.phone && !this.isValidPhone(customer.phone)) {
      warnings.push('Phone number format may be invalid')
    }

    if (customer.mobile && !this.isValidPhone(customer.mobile)) {
      warnings.push('Mobile number format may be invalid')
    }

    // Website validation
    if (customer.website && !this.isValidURL(customer.website)) {
      warnings.push('Website URL format may be invalid')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Data transformation utilities
export class DataTransformer {
  // Transform raw data based on mapping
  static transformImportData(
    rawData: any[],
    mapping: CustomerImportMapping,
    headers: string[]
  ): CustomerFormData[] {
    return rawData.map(row => {
      const customer: Partial<CustomerFormData> = {
        source: 'Other',
        status: 'Active'
      }

      Object.entries(mapping).forEach(([sourceColumn, config]) => {
        const columnIndex = headers.indexOf(sourceColumn)
        if (columnIndex === -1) return

        const rawValue = Array.isArray(row) ? row[columnIndex] : row[sourceColumn]
        
        if (rawValue !== undefined && rawValue !== null && rawValue !== '') {
          let transformedValue = this.transformValue(rawValue, config.type)
          
          // Apply custom transformation if provided
          if (config.transform) {
            transformedValue = config.transform(transformedValue)
          }
          
          if (transformedValue !== null) {
            customer[config.targetField] = transformedValue
          }
        }
      })

      // Set defaults
      if (!customer.customer_tier) customer.customer_tier = 'Bronze'
      if (!customer.currency_preference) customer.currency_preference = 'USD'
      if (!customer.company_size) customer.company_size = 'Small'
      if (!customer.payment_terms) customer.payment_terms = '30 Days'

      return customer as CustomerFormData
    })
  }

  // Transform value based on type
  static transformValue(value: any, type: string): any {
    if (value === null || value === undefined || value === '') {
      return null
    }

    const stringValue = value.toString().trim()

    switch (type) {
      case 'number':
        const num = parseFloat(stringValue.replace(/[,$]/g, ''))
        return isNaN(num) ? null : num

      case 'boolean':
        const lowerValue = stringValue.toLowerCase()
        return ['true', '1', 'yes', 'y', 'on'].includes(lowerValue)

      case 'date':
        const date = new Date(stringValue)
        return isNaN(date.getTime()) ? null : date.toISOString().split('T')[0]

      case 'email':
        return stringValue.toLowerCase()

      case 'array':
        return stringValue.split(',').map((item: string) => item.trim()).filter(Boolean)

      case 'text':
      default:
        return stringValue
    }
  }

  // Transform export data
  static transformExportData(
    customers: CustomerFormData[],
    options: CustomerExportOptions
  ): any[] {
    return customers.map(customer => {
      const row: any = {}

      options.columns.forEach(columnId => {
        let value = customer[columnId as keyof CustomerFormData]

        // Format dates
        if (columnId.includes('date') || columnId.includes('since')) {
          if (value && typeof value === 'string') {
            const date = new Date(value)
            switch (options.dateFormat) {
              case 'US':
                value = date.toLocaleDateString('en-US')
                break
              case 'EU':
                value = date.toLocaleDateString('en-GB')
                break
              default:
                value = date.toISOString().split('T')[0]
            }
          }
        }

        // Format currency
        if (columnId.includes('volume') || columnId.includes('limit')) {
          if (typeof value === 'number') {
            value = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: customer.currency_preference || 'USD'
            }).format(value)
          }
        }

        // Format arrays
        if (Array.isArray(value)) {
          value = value.join(', ')
        }

        // Create column label
        const columnLabel = columnId
          .replace(/_/g, ' ')
          .replace(/\b\w/g, l => l.toUpperCase())

        row[columnLabel] = value || ''
      })

      return row
    })
  }
}

// Export format utilities
export class ExportFormatter {
  // Convert data to CSV
  static toCSV(data: any[], options: CustomerExportOptions): string {
    if (data.length === 0) return ''

    const delimiter = options.delimiter || ','
    const headers = Object.keys(data[0])
    
    let csv = ''
    
    // Add headers if requested
    if (options.includeHeaders) {
      csv += headers.join(delimiter) + '\n'
    }
    
    // Add data rows
    data.forEach(row => {
      const values = headers.map(header => {
        let value = row[header] || ''
        
        // Escape values that contain the delimiter or quotes
        if (typeof value === 'string' && (
          value.includes(delimiter) || 
          value.includes('"') || 
          value.includes('\n')
        )) {
          value = '"' + value.replace(/"/g, '""') + '"'
        }
        
        return value
      })
      csv += values.join(delimiter) + '\n'
    })
    
    return csv
  }

  // Convert data to JSON
  static toJSON(data: any[], options: CustomerExportOptions): string {
    return JSON.stringify(data, null, 2)
  }

  // Convert data to Excel format (simplified)
  static toExcel(data: any[], options: CustomerExportOptions): string {
    // For a full Excel implementation, use a library like 'xlsx'
    // For now, return tab-separated values
    return this.toCSV(data, { ...options, delimiter: '\t' })
  }

  // Get appropriate MIME type
  static getMimeType(format: string): string {
    switch (format) {
      case 'csv':
        return 'text/csv'
      case 'json':
        return 'application/json'
      case 'excel':
        return 'application/vnd.ms-excel'
      default:
        return 'text/plain'
    }
  }

  // Generate filename
  static generateFilename(format: string, prefix: string = 'customers_export'): string {
    const timestamp = new Date().toISOString().split('T')[0]
    const extension = format === 'excel' ? 'xls' : format
    return `${prefix}_${timestamp}.${extension}`
  }
}

// Progress tracking utilities
export class ProgressTracker {
  private total: number
  private processed: number
  private successful: number
  private failed: number
  private onUpdate?: (progress: any) => void

  constructor(total: number, onUpdate?: (progress: any) => void) {
    this.total = total
    this.processed = 0
    this.successful = 0
    this.failed = 0
    this.onUpdate = onUpdate
  }

  increment(success: boolean = true) {
    this.processed++
    if (success) {
      this.successful++
    } else {
      this.failed++
    }
    
    this.updateProgress()
  }

  private updateProgress() {
    const progress = {
      total: this.total,
      processed: this.processed,
      successful: this.successful,
      failed: this.failed,
      percentage: Math.round((this.processed / this.total) * 100)
    }
    
    this.onUpdate?.(progress)
  }

  getProgress() {
    return {
      total: this.total,
      processed: this.processed,
      successful: this.successful,
      failed: this.failed,
      percentage: Math.round((this.processed / this.total) * 100)
    }
  }
}
