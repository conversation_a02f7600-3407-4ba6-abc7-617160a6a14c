'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from './auth-provider'

interface PerformanceStats {
  avgAuthTime: number
  avgSessionFetchTime: number
  avgRoleFetchTime: number
  successRate: number
  totalRetries: number
  lastUpdated: string
}

export function PerformanceDashboard() {
  // TODO: Re-enable after auth-provider performance metrics are restored
  const performanceMetrics = null // const { performanceMetrics } = useAuth()
  const [stats, setStats] = useState<PerformanceStats>({
    avgAuthTime: 0,
    avgSessionFetchTime: 0,
    avgRoleFetchTime: 0,
    successRate: 0,
    totalRetries: 0,
    lastUpdated: new Date().toLocaleTimeString()
  })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // TODO: Re-enable after auth-provider performance metrics are restored
    if (!performanceMetrics) return

    // Update stats when performance metrics change
    setStats(prev => ({
      avgAuthTime: (performanceMetrics as any)?.totalAuthTime || prev.avgAuthTime,
      avgSessionFetchTime: (performanceMetrics as any)?.sessionFetchTime || prev.avgSessionFetchTime,
      avgRoleFetchTime: (performanceMetrics as any)?.roleFetchTime || prev.avgRoleFetchTime,
      successRate: (performanceMetrics as any)?.successRate || prev.successRate,
      totalRetries: (performanceMetrics as any)?.retryCount || prev.totalRetries,
      lastUpdated: new Date().toLocaleTimeString()
    }))
  }, [performanceMetrics])

  // Only show in development or when explicitly enabled
  const shouldShow = process.env.NODE_ENV === 'development' || 
                    typeof window !== 'undefined' && window.location.search.includes('perf=true')

  if (!shouldShow && !isVisible) return null

  const getPerformanceStatus = (time: number) => {
    if (time < 2000) return { status: 'excellent', color: 'text-green-600', bg: 'bg-green-50' }
    if (time < 5000) return { status: 'good', color: 'text-yellow-600', bg: 'bg-yellow-50' }
    if (time < 10000) return { status: 'fair', color: 'text-orange-600', bg: 'bg-orange-50' }
    return { status: 'poor', color: 'text-red-600', bg: 'bg-red-50' }
  }

  const authStatus = getPerformanceStatus(stats.avgAuthTime)
  const sessionStatus = getPerformanceStatus(stats.avgSessionFetchTime)
  const roleStatus = getPerformanceStatus(stats.avgRoleFetchTime)

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!isVisible ? (
        <button
          onClick={() => setIsVisible(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg shadow-lg text-sm font-medium"
        >
          📊 Performance
        </button>
      ) : (
        <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 w-80 max-h-96 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-lg font-semibold text-gray-900">🚀 Auth Performance</h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>

          <div className="space-y-3">
            {/* Total Authentication Time */}
            <div className={`p-3 rounded-lg ${authStatus.bg}`}>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Total Auth Time</span>
                <span className={`text-sm font-bold ${authStatus.color}`}>
                  {stats.avgAuthTime > 0 ? `${stats.avgAuthTime}ms` : 'N/A'}
                </span>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Status: {authStatus.status}
              </div>
            </div>

            {/* Session Fetch Time */}
            <div className={`p-3 rounded-lg ${sessionStatus.bg}`}>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Session Fetch</span>
                <span className={`text-sm font-bold ${sessionStatus.color}`}>
                  {stats.avgSessionFetchTime > 0 ? `${stats.avgSessionFetchTime}ms` : 'N/A'}
                </span>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Status: {sessionStatus.status}
              </div>
            </div>

            {/* Role Fetch Time */}
            <div className={`p-3 rounded-lg ${roleStatus.bg}`}>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Role Fetch</span>
                <span className={`text-sm font-bold ${roleStatus.color}`}>
                  {stats.avgRoleFetchTime > 0 ? `${stats.avgRoleFetchTime}ms` : 'N/A'}
                </span>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Status: {roleStatus.status}
              </div>
            </div>

            {/* Success Rate */}
            <div className="p-3 rounded-lg bg-blue-50">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Success Rate</span>
                <span className="text-sm font-bold text-blue-600">
                  {(stats.successRate * 100).toFixed(1)}%
                </span>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                Retries: {stats.totalRetries}
              </div>
            </div>

            {/* Performance Indicators */}
            <div className="border-t pt-3">
              <div className="text-xs text-gray-500 space-y-1">
                <div>Last Updated: {stats.lastUpdated}</div>
                <div className="flex items-center space-x-2">
                  <span>Optimization:</span>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                    ✅ Active
                  </span>
                </div>
              </div>
            </div>

            {/* Performance Tips */}
            {(stats.avgAuthTime > 10000 || stats.totalRetries > 5) && (
              <div className="border-t pt-3">
                <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
                  <strong>Performance Alert:</strong> High latency detected. 
                  Check network connection or Supabase status.
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// Hook for accessing performance metrics
export function usePerformanceMetrics() {
  // TODO: Re-enable after auth-provider performance metrics are restored
  const performanceMetrics = null // const { performanceMetrics } = useAuth()
  
  return {
    metrics: performanceMetrics,
    isOptimized: ((performanceMetrics as any)?.successRate || 0) > 0.8,
    hasHighLatency: ((performanceMetrics as any)?.totalAuthTime || 0) > 10000,
    needsOptimization: ((performanceMetrics as any)?.retryCount || 0) > 5
  }
}
