# Nawras CRM - Project Architecture Documentation

## Overview

Nawras CRM is a comprehensive Customer Relationship Management system built with modern web technologies. This document provides a complete architectural overview of the application structure, patterns, and conventions.

## Technology Stack

### Core Technologies
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript 5.0+
- **Styling**: Tailwind CSS 3.0+ with CSS Variables
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand 5.0+
- **UI Components**: Radix UI + Custom Components
- **Testing**: Playwright (E2E), Jest (Unit)
- **Build Tool**: Next.js built-in bundler
- **Deployment**: Vercel

### Key Dependencies
- **@supabase/supabase-js**: Database and auth client
- **@tanstack/react-query**: Server state management
- **lucide-react**: Icon library
- **date-fns**: Date manipulation
- **sonner**: Toast notifications
- **vaul**: Drawer components
- **cmdk**: Command palette

## Project Structure

```
crmnew-main/
├── app/                          # Next.js App Router
│   ├── api/                      # API Routes
│   │   ├── customers/            # Customer API endpoints
│   │   ├── deals/                # Deal API endpoints
│   │   └── health/               # Health check endpoints
│   ├── dashboard/                # Dashboard pages
│   │   ├── companies/            # Companies module
│   │   ├── customers/            # Customers module
│   │   ├── deals/                # Deals module
│   │   ├── leads/                # Leads module
│   │   ├── opportunities/        # Opportunities module
│   │   ├── proposals/            # Proposals module
│   │   ├── reports/              # Reports module
│   │   ├── settings/             # Settings module
│   │   ├── tasks/                # Tasks module
│   │   ├── users/                # User management module
│   │   ├── layout.tsx            # Dashboard layout
│   │   └── page.tsx              # Dashboard home page
│   ├── login/                    # Authentication pages
│   ├── types/                    # Type definitions
│   ├── globals.css               # Global styles
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Landing page
├── components/                   # React components
│   ├── accessibility/            # Accessibility components
│   ├── customers/                # Customer-specific components
│   ├── deals/                    # Deal-specific components
│   ├── forms/                    # Form system components
│   ├── layout/                   # Layout components
│   ├── mobile/                   # Mobile-specific components
│   ├── performance/              # Performance monitoring
│   ├── tables/                   # Table components
│   ├── ui/                       # Base UI components
│   ├── app-sidebar.tsx           # Main navigation sidebar
│   ├── auth-provider.tsx         # Authentication context
│   ├── theme-provider.tsx        # Theme management
│   └── language-provider.tsx     # Internationalization
├── hooks/                        # Custom React hooks
│   ├── use-mobile.ts             # Mobile detection
│   ├── use-optimized-data.ts     # Data fetching optimization
│   ├── use-performance.ts        # Performance monitoring
│   ├── use-role.tsx              # Role-based access control
│   └── use-toast.ts              # Toast notifications
├── lib/                          # Utility libraries
│   ├── countries.ts              # Country data
│   ├── database.types.ts         # Database type definitions
│   ├── error-handler.ts          # Error handling utilities
│   ├── performance-monitor.ts    # Performance monitoring
│   ├── supabase.ts               # Supabase client configuration
│   └── utils.ts                  # General utilities
├── docs/                         # Documentation
├── scripts/                      # Database scripts and utilities
├── supabase/                     # Supabase configuration
├── __tests__/                    # Test files
└── playwright.config.ts          # Playwright configuration
```

## Architecture Patterns

### 1. Component Architecture

#### Component Hierarchy
```
App Layout (Root)
├── Theme Provider
├── Auth Provider
├── Language Provider
└── Dashboard Layout
    ├── Sidebar Navigation
    ├── Header with Breadcrumbs
    └── Main Content Area
        └── Module-specific Pages
```

#### Component Categories
- **Layout Components**: Handle page structure and navigation
- **Feature Components**: Module-specific business logic components
- **UI Components**: Reusable, generic UI elements
- **Form Components**: Standardized form system
- **Provider Components**: Context providers for global state

### 2. Data Flow Architecture

#### State Management Strategy
- **Server State**: React Query for API data caching and synchronization
- **Client State**: Zustand for local application state
- **Form State**: Custom useForm hook with validation
- **Auth State**: Context-based authentication state
- **Theme State**: Context-based theme management

#### Data Fetching Patterns
```typescript
// Optimized data fetching with caching
const { data, loading, error } = useOptimizedData({
  table: "customers",
  requiresAuth: true,
  realtime: true,
  cacheKey: "customers-list"
})
```

### 3. Authentication & Authorization

#### Authentication Flow
1. User accesses protected route
2. Auth provider checks session
3. Redirect to login if unauthenticated
4. Supabase handles authentication
5. User context updated with session data

#### Role-Based Access Control
```typescript
// Role checking pattern
const { isAdmin, canViewAllData } = useRole()

// Conditional rendering based on permissions
{isAdmin && <AdminOnlyComponent />}
```

## Module Documentation

### 1. Dashboard Module
**Location**: `app/dashboard/`
**Purpose**: Main application interface with navigation and module access
**Key Components**:
- `layout.tsx`: Dashboard layout with sidebar and header
- `page.tsx`: Dashboard home with metrics and quick actions

### 2. Customers Module
**Location**: `app/dashboard/customers/`, `components/customers/`
**Purpose**: Customer relationship management
**Key Features**:
- Customer CRUD operations
- Advanced search and filtering
- Customer analytics and insights
- Address management
- File uploads and document management

### 3. Deals Module
**Location**: `app/dashboard/deals/`, `components/deals/`
**Purpose**: Sales pipeline and deal management
**Key Features**:
- Kanban board interface
- Deal creation and editing
- Pipeline analytics
- Deal stages and progression tracking

### 4. Companies Module
**Location**: `app/dashboard/companies/`
**Purpose**: Company and organization management
**Key Features**:
- Company profiles
- Relationship mapping
- Business information tracking

### 5. Leads Module
**Location**: `app/dashboard/leads/`
**Purpose**: Lead generation and qualification
**Key Features**:
- Lead capture and tracking
- Lead scoring and qualification
- Conversion to customers

### 6. Opportunities Module
**Location**: `app/dashboard/opportunities/`
**Purpose**: Sales opportunity tracking
**Key Features**:
- Opportunity pipeline
- Revenue forecasting
- Probability tracking

### 7. User Management Module
**Location**: `app/dashboard/users/`
**Purpose**: User administration and role management
**Key Features**:
- User CRUD operations
- Role assignment
- Permission management
- Admin-only access controls

## Development Conventions

### 1. File Naming Conventions
- **Components**: PascalCase (e.g., `CustomerForm.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useCustomerData.ts`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Types**: PascalCase (e.g., `CustomerFormData`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS`)

### 2. Import/Export Patterns
```typescript
// Preferred import pattern
import { Button } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider"
import { CustomerFormData } from "@/app/types/customer"

// Component export pattern
export function CustomerForm({ onSubmit }: CustomerFormProps) {
  // Component implementation
}

// Default export for pages
export default function CustomersPage() {
  // Page implementation
}
```

### 3. Component Structure
```typescript
// Standard component structure
interface ComponentProps {
  // Props definition
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // Hooks
  const [state, setState] = useState()
  const { data } = useQuery()
  
  // Event handlers
  const handleAction = () => {
    // Handler implementation
  }
  
  // Effects
  useEffect(() => {
    // Effect implementation
  }, [])
  
  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}
```

## Performance Optimization

### 1. Code Splitting
- Automatic route-based code splitting via Next.js
- Dynamic imports for heavy components
- Lazy loading for non-critical features

### 2. Data Optimization
- React Query for intelligent caching
- Optimistic updates for better UX
- Real-time subscriptions for live data

### 3. Bundle Optimization
- Tree shaking for unused code elimination
- Image optimization with Next.js Image component
- Font optimization with Next.js Font optimization

## Security Considerations

### 1. Authentication Security
- Secure session management with Supabase
- Automatic token refresh
- Secure cookie handling

### 2. Data Security
- Row Level Security (RLS) policies in Supabase
- Input validation and sanitization
- CSRF protection

### 3. API Security
- Authentication required for all API routes
- Input validation on server side
- Rate limiting for API endpoints

## Testing Strategy

### 1. End-to-End Testing
- Playwright for comprehensive E2E testing
- All 11 CRM modules tested systematically
- Role-based access control testing
- Performance testing with specific targets

### 2. Component Testing
- Jest for unit testing
- React Testing Library for component testing
- Mock implementations for external dependencies

### 3. Integration Testing
- API endpoint testing
- Database integration testing
- Authentication flow testing

## Deployment & DevOps

### 1. Deployment Pipeline
- GitHub repository: `mrTamtamnew/crmnew`
- Automatic deployment to Vercel
- Live site: `https://sales.nawrasinchina.com/`

### 2. Environment Configuration
- Development: Local development with Supabase
- Production: Vercel deployment with production Supabase

### 3. Monitoring
- Performance monitoring with Web Vitals
- Error tracking and logging
- Real-time application monitoring

## Future Enhancements

### 1. Planned Features
- Advanced analytics dashboard
- Mobile application
- API integrations
- Workflow automation

### 2. Technical Improvements
- Progressive Web App (PWA) capabilities
- Advanced caching strategies
- Microservices architecture consideration
- GraphQL API implementation

## Support & Maintenance

### 1. Documentation Updates
- Regular documentation reviews
- Architecture decision records
- API documentation maintenance

### 2. Code Quality
- ESLint and Prettier configuration
- TypeScript strict mode
- Code review processes
- Automated testing requirements

This architecture documentation serves as the foundation for understanding and contributing to the Nawras CRM application. It should be updated as the application evolves and new patterns are established.
