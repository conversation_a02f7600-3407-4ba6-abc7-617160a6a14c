import { useCallback, useEffect, useRef, useState } from 'react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  dataProcessingTime: number
  totalTime: number
}

interface ChartPerformanceOptions {
  enableLogging?: boolean
  warningThreshold?: number // milliseconds
  errorThreshold?: number // milliseconds
}

export function useChartPerformance(
  chartName: string,
  options: ChartPerformanceOptions = {}
) {
  const {
    enableLogging = true,
    warningThreshold = 3000, // 3 seconds
    errorThreshold = 5000 // 5 seconds
  } = options

  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const startTimeRef = useRef<number>(0)
  const renderStartRef = useRef<number>(0)
  const dataStartRef = useRef<number>(0)

  // Start performance measurement
  const startMeasurement = useCallback(() => {
    startTimeRef.current = performance.now()
    setIsLoading(true)
    setMetrics(null)
  }, [])

  // Mark data processing start
  const startDataProcessing = useCallback(() => {
    dataStartRef.current = performance.now()
  }, [])

  // Mark data processing end
  const endDataProcessing = useCallback(() => {
    const dataProcessingTime = performance.now() - dataStartRef.current
    return dataProcessingTime
  }, [])

  // Mark render start
  const startRender = useCallback(() => {
    renderStartRef.current = performance.now()
  }, [])

  // Mark render end
  const endRender = useCallback(() => {
    const renderTime = performance.now() - renderStartRef.current
    return renderTime
  }, [])

  // End performance measurement
  const endMeasurement = useCallback(() => {
    const endTime = performance.now()
    const totalTime = endTime - startTimeRef.current
    const loadTime = totalTime
    const renderTime = endRender()
    const dataProcessingTime = endDataProcessing()

    const newMetrics: PerformanceMetrics = {
      loadTime,
      renderTime,
      dataProcessingTime,
      totalTime
    }

    setMetrics(newMetrics)
    setIsLoading(false)

    if (enableLogging) {
      logPerformance(newMetrics)
    }

    return newMetrics
  }, [enableLogging, endRender, endDataProcessing])

  // Log performance metrics
  const logPerformance = useCallback((metrics: PerformanceMetrics) => {
    const { totalTime, loadTime, renderTime, dataProcessingTime } = metrics

    if (totalTime > errorThreshold) {
      console.error(`🚨 Chart ${chartName} performance critical:`, {
        totalTime: `${totalTime.toFixed(2)}ms`,
        loadTime: `${loadTime.toFixed(2)}ms`,
        renderTime: `${renderTime.toFixed(2)}ms`,
        dataProcessingTime: `${dataProcessingTime.toFixed(2)}ms`,
        threshold: `${errorThreshold}ms`
      })
    } else if (totalTime > warningThreshold) {
      console.warn(`⚠️ Chart ${chartName} performance warning:`, {
        totalTime: `${totalTime.toFixed(2)}ms`,
        loadTime: `${loadTime.toFixed(2)}ms`,
        renderTime: `${renderTime.toFixed(2)}ms`,
        dataProcessingTime: `${dataProcessingTime.toFixed(2)}ms`,
        threshold: `${warningThreshold}ms`
      })
    } else {
      console.log(`✅ Chart ${chartName} performance good:`, {
        totalTime: `${totalTime.toFixed(2)}ms`,
        loadTime: `${loadTime.toFixed(2)}ms`,
        renderTime: `${renderTime.toFixed(2)}ms`,
        dataProcessingTime: `${dataProcessingTime.toFixed(2)}ms`
      })
    }
  }, [chartName, warningThreshold, errorThreshold])

  // Get performance status
  const getPerformanceStatus = useCallback(() => {
    if (!metrics) return 'unknown'
    
    if (metrics.totalTime > errorThreshold) return 'critical'
    if (metrics.totalTime > warningThreshold) return 'warning'
    return 'good'
  }, [metrics, warningThreshold, errorThreshold])

  // Format metrics for display
  const formatMetrics = useCallback(() => {
    if (!metrics) return null

    return {
      totalTime: `${metrics.totalTime.toFixed(2)}ms`,
      loadTime: `${metrics.loadTime.toFixed(2)}ms`,
      renderTime: `${metrics.renderTime.toFixed(2)}ms`,
      dataProcessingTime: `${metrics.dataProcessingTime.toFixed(2)}ms`,
      status: getPerformanceStatus()
    }
  }, [metrics, getPerformanceStatus])

  // Auto-start measurement on mount
  useEffect(() => {
    startMeasurement()
  }, [startMeasurement])

  return {
    metrics,
    isLoading,
    startMeasurement,
    endMeasurement,
    startDataProcessing,
    endDataProcessing,
    startRender,
    endRender,
    getPerformanceStatus,
    formatMetrics,
    logPerformance: () => metrics && logPerformance(metrics)
  }
}

// Hook for monitoring multiple charts
export function useMultiChartPerformance(chartNames: string[]) {
  const [allMetrics, setAllMetrics] = useState<Record<string, PerformanceMetrics>>({})
  const [overallStatus, setOverallStatus] = useState<'good' | 'warning' | 'critical'>('good')

  const updateMetrics = useCallback((chartName: string, metrics: PerformanceMetrics) => {
    setAllMetrics(prev => ({
      ...prev,
      [chartName]: metrics
    }))
  }, [])

  // Calculate overall performance status
  useEffect(() => {
    const metrics = Object.values(allMetrics)
    if (metrics.length === 0) return

    const maxTime = Math.max(...metrics.map(m => m.totalTime))
    
    if (maxTime > 5000) {
      setOverallStatus('critical')
    } else if (maxTime > 3000) {
      setOverallStatus('warning')
    } else {
      setOverallStatus('good')
    }
  }, [allMetrics])

  const getAverageLoadTime = useCallback(() => {
    const metrics = Object.values(allMetrics)
    if (metrics.length === 0) return 0

    const totalTime = metrics.reduce((sum, m) => sum + m.totalTime, 0)
    return totalTime / metrics.length
  }, [allMetrics])

  const getSlowestChart = useCallback(() => {
    const entries = Object.entries(allMetrics)
    if (entries.length === 0) return null

    return entries.reduce((slowest, [name, metrics]) => {
      if (!slowest || metrics.totalTime > slowest.metrics.totalTime) {
        return { name, metrics }
      }
      return slowest
    }, null as { name: string; metrics: PerformanceMetrics } | null)
  }, [allMetrics])

  return {
    allMetrics,
    overallStatus,
    updateMetrics,
    getAverageLoadTime,
    getSlowestChart,
    chartCount: Object.keys(allMetrics).length
  }
}

// Performance optimization utilities
export const chartPerformanceUtils = {
  // Debounce data updates to prevent excessive re-renders
  debounceDataUpdate: (callback: Function, delay: number = 300) => {
    let timeoutId: NodeJS.Timeout
    return (...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => callback(...args), delay)
    }
  },

  // Throttle chart updates for real-time data
  throttleChartUpdate: (callback: Function, limit: number = 1000) => {
    let inThrottle: boolean
    return (...args: any[]) => {
      if (!inThrottle) {
        callback(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // Optimize data for chart rendering
  optimizeChartData: (data: any[], maxPoints: number = 100) => {
    if (data.length <= maxPoints) return data

    const step = Math.ceil(data.length / maxPoints)
    return data.filter((_, index) => index % step === 0)
  },

  // Check if chart should use simplified rendering
  shouldUseSimplifiedRendering: (dataLength: number, threshold: number = 1000) => {
    return dataLength > threshold
  }
}
