'use client'

import React from 'react'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { useActivityData } from '@/hooks/useChartData'
import { formatters, CHART_COLORS } from '@/components/charts/chart-config'
import { ChartProps } from '@/components/charts/types'
import { CheckCircle, Clock, AlertTriangle, Activity } from 'lucide-react'

interface ActivityChartProps extends Omit<ChartProps, 'data'> {
  showStacked?: boolean
  groupBy?: 'priority' | 'status'
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{label} Priority</p>
        <div className="space-y-1">
          <p className="text-sm text-green-600">
            Completed: <span className="font-medium">{data.completed}</span>
          </p>
          <p className="text-sm text-blue-600">
            Pending: <span className="font-medium">{data.pending}</span>
          </p>
          <p className="text-sm text-red-600">
            Overdue: <span className="font-medium">{data.overdue}</span>
          </p>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Total: <span className="font-medium">{data.completed + data.pending + data.overdue}</span>
        </p>
      </div>
    )
  }
  return null
}

const getActivityIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'high':
    case 'urgent':
      return <AlertTriangle className="h-4 w-4 text-red-500" />
    case 'medium':
      return <Clock className="h-4 w-4 text-yellow-500" />
    case 'low':
      return <CheckCircle className="h-4 w-4 text-green-500" />
    default:
      return <Activity className="h-4 w-4 text-blue-500" />
  }
}

export function ActivityChart({ 
  config,
  loading: externalLoading,
  error,
  className = "",
  title = "Task Activity Overview",
  subtitle = "Task completion and priority analysis",
  showStacked = true,
  groupBy = 'priority'
}: ActivityChartProps) {
  const { data, loading: dataLoading } = useActivityData()
  const isLoading = externalLoading || dataLoading

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>Error loading activity data: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>No activity data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate summary metrics
  const totalTasks = data.reduce((sum, item) => sum + item.completed + item.pending + item.overdue, 0)
  const totalCompleted = data.reduce((sum, item) => sum + item.completed, 0)
  const totalPending = data.reduce((sum, item) => sum + item.pending, 0)
  const totalOverdue = data.reduce((sum, item) => sum + item.overdue, 0)
  const completionRate = totalTasks > 0 ? (totalCompleted / totalTasks) * 100 : 0
  const overdueRate = totalTasks > 0 ? (totalOverdue / totalTasks) * 100 : 0

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-primary" />
            {title}
          </div>
          <div className="flex gap-2">
            <Badge variant="success" className="flex items-center gap-1">
              <CheckCircle className="h-3 w-3" />
              {formatters.percentage(completionRate)}
            </Badge>
            {overdueRate > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {formatters.percentage(overdueRate)}
              </Badge>
            )}
          </div>
        </CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="flex-1">
            <ResponsiveContainer width="100%" height={config?.height || 300}>
              <BarChart
                data={data}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="type" 
                  tick={{ fontSize: 12 }}
                />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip content={<CustomTooltip />} />
                
                {showStacked ? (
                  <>
                    <Bar 
                      dataKey="completed" 
                      stackId="a" 
                      fill={CHART_COLORS.success}
                      name="Completed"
                      radius={[0, 0, 0, 0]}
                    />
                    <Bar 
                      dataKey="pending" 
                      stackId="a" 
                      fill={CHART_COLORS.info}
                      name="Pending"
                      radius={[0, 0, 0, 0]}
                    />
                    <Bar 
                      dataKey="overdue" 
                      stackId="a" 
                      fill={CHART_COLORS.danger}
                      name="Overdue"
                      radius={[4, 4, 0, 0]}
                    />
                  </>
                ) : (
                  <>
                    <Bar 
                      dataKey="completed" 
                      fill={CHART_COLORS.success}
                      name="Completed"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar 
                      dataKey="pending" 
                      fill={CHART_COLORS.info}
                      name="Pending"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar 
                      dataKey="overdue" 
                      fill={CHART_COLORS.danger}
                      name="Overdue"
                      radius={[4, 4, 0, 0]}
                    />
                  </>
                )}
              </BarChart>
            </ResponsiveContainer>
          </div>
          
          {/* Activity breakdown by priority */}
          <div className="lg:w-64 space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground">Priority Breakdown</h4>
            {data.map((item, index) => {
              const total = item.completed + item.pending + item.overdue
              const completionRate = total > 0 ? (item.completed / total) * 100 : 0
              
              return (
                <div key={item.type} className="p-3 rounded-lg bg-muted/50 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getActivityIcon(item.type)}
                      <span className="text-sm font-medium">{item.type}</span>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {formatters.percentage(completionRate)}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-green-600">Completed</span>
                      <span className="font-medium">{item.completed}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-blue-600">Pending</span>
                      <span className="font-medium">{item.pending}</span>
                    </div>
                    {item.overdue > 0 && (
                      <div className="flex justify-between">
                        <span className="text-red-600">Overdue</span>
                        <span className="font-medium">{item.overdue}</span>
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>
        
        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">Total Tasks</p>
            <p className="font-semibold text-lg">{formatters.number(totalTasks)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Completed</p>
            <p className="font-semibold text-lg text-green-600">{formatters.number(totalCompleted)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Pending</p>
            <p className="font-semibold text-lg text-blue-600">{formatters.number(totalPending)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Overdue</p>
            <p className="font-semibold text-lg text-red-600">{formatters.number(totalOverdue)}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
