"use client"

import { useState, useEffect, useRef, ReactNode } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface LazyLoaderProps {
  children: ReactNode
  fallback?: ReactNode
  rootMargin?: string
  threshold?: number
  delay?: number
  className?: string
}

export function LazyLoader({
  children,
  fallback,
  rootMargin = "50px",
  threshold = 0.1,
  delay = 0,
  className = ""
}: LazyLoaderProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          
          // Add delay if specified
          if (delay > 0) {
            setTimeout(() => setShouldRender(true), delay)
          } else {
            setShouldRender(true)
          }
          
          observer.unobserve(element)
        }
      },
      {
        rootMargin,
        threshold
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [rootMargin, threshold, delay])

  const defaultFallback = (
    <div className="space-y-3">
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-4 w-1/2" />
    </div>
  )

  return (
    <div ref={elementRef} className={className}>
      {shouldRender ? children : (fallback || defaultFallback)}
    </div>
  )
}

// Specialized lazy loaders for common use cases
export function LazyTable({ children, className = "" }: { children: ReactNode, className?: string }) {
  return (
    <LazyLoader
      className={className}
      fallback={
        <div className="space-y-2">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </div>
      }
    >
      {children}
    </LazyLoader>
  )
}

export function LazyCard({ children, className = "" }: { children: ReactNode, className?: string }) {
  return (
    <LazyLoader
      className={className}
      fallback={
        <div className="border rounded-lg p-4 space-y-3">
          <Skeleton className="h-6 w-1/3" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      }
    >
      {children}
    </LazyLoader>
  )
}

export function LazyChart({ children, className = "" }: { children: ReactNode, className?: string }) {
  return (
    <LazyLoader
      className={className}
      delay={200} // Charts might need more time to render
      fallback={
        <div className="border rounded-lg p-4">
          <Skeleton className="h-6 w-1/4 mb-4" />
          <Skeleton className="h-48 w-full" />
        </div>
      }
    >
      {children}
    </LazyLoader>
  )
}
