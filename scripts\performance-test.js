// ✅ COMPREHENSIVE PERFORMANCE TESTING SCRIPT
// Run with: node scripts/performance-test.js

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'https://sales.nawrasinchina.com',
  credentials: {
    email: '<EMAIL>',
    password: '111333Tt'
  },
  performanceTargets: {
    pageLoad: 3000, // 3 seconds
    queryTime: 1000, // 1 second
    authTime: 2000, // 2 seconds
  },
  modules: [
    { name: 'Dashboard', path: '/dashboard' },
    { name: 'Customers', path: '/dashboard/customers' },
    { name: 'Leads', path: '/dashboard/leads' },
    { name: 'Opportunities', path: '/dashboard/opportunities' },
    { name: 'Companies', path: '/dashboard/companies' },
    { name: 'Tasks', path: '/dashboard/tasks' },
  ]
};

class PerformanceTester {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
  }

  async initialize() {
    console.log('🚀 Initializing Performance Test Suite...');
    this.browser = await chromium.launch({ headless: false });
    this.page = await this.browser.newPage();
    
    // Enable performance monitoring
    await this.page.addInitScript(() => {
      window.performanceMetrics = {
        queries: [],
        startTime: Date.now(),
        authTime: 0,
        pageLoadTime: 0
      };
      
      // Monitor console for query performance
      const originalLog = console.log;
      console.log = (...args) => {
        const message = args.join(' ');
        if (message.includes('Slow query detected')) {
          const match = message.match(/(\w+): ([\d.]+)ms/);
          if (match) {
            window.performanceMetrics.queries.push({
              table: match[1],
              duration: parseFloat(match[2]),
              timestamp: Date.now()
            });
          }
        }
        originalLog.apply(console, args);
      };
    });
  }

  async login() {
    console.log('🔐 Testing Authentication Flow...');
    const startTime = Date.now();
    
    await this.page.goto(`${TEST_CONFIG.baseUrl}/login`);
    
    // Fill credentials
    await this.page.fill('input[type="email"]', TEST_CONFIG.credentials.email);
    await this.page.fill('input[type="password"]', TEST_CONFIG.credentials.password);
    
    // Click login and measure time
    await this.page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await this.page.waitForURL('**/dashboard', { timeout: 30000 });
    
    const authTime = Date.now() - startTime;
    console.log(`✅ Authentication completed in ${authTime}ms`);
    
    return {
      success: true,
      duration: authTime,
      withinTarget: authTime <= TEST_CONFIG.performanceTargets.authTime
    };
  }

  async testModule(module) {
    console.log(`🧪 Testing ${module.name} module...`);
    const startTime = Date.now();
    
    try {
      // Navigate to module
      await this.page.goto(`${TEST_CONFIG.baseUrl}${module.path}`);
      
      // Wait for content to load (look for data or loading indicators)
      await this.page.waitForSelector('[data-testid="module-content"], .loading, table, .grid', { 
        timeout: 30000 
      });
      
      // Wait additional time for data to fully load
      await this.page.waitForTimeout(2000);
      
      const pageLoadTime = Date.now() - startTime;
      
      // Get performance metrics from page
      const metrics = await this.page.evaluate(() => {
        return window.performanceMetrics || { queries: [] };
      });
      
      // Check for console errors
      const consoleErrors = await this.page.evaluate(() => {
        return window.consoleErrors || [];
      });
      
      // Calculate query statistics
      const recentQueries = metrics.queries.filter(q => 
        q.timestamp > startTime - 1000 // Queries from last second before navigation
      );
      
      const slowQueries = recentQueries.filter(q => q.duration > 1000);
      const avgQueryTime = recentQueries.length > 0 
        ? recentQueries.reduce((sum, q) => sum + q.duration, 0) / recentQueries.length 
        : 0;
      
      const result = {
        module: module.name,
        path: module.path,
        pageLoadTime,
        queryCount: recentQueries.length,
        slowQueryCount: slowQueries.length,
        averageQueryTime: Math.round(avgQueryTime),
        maxQueryTime: recentQueries.length > 0 ? Math.max(...recentQueries.map(q => q.duration)) : 0,
        consoleErrors: consoleErrors.length,
        success: pageLoadTime <= TEST_CONFIG.performanceTargets.pageLoad,
        timestamp: new Date().toISOString()
      };
      
      console.log(`📊 ${module.name} Results:`, {
        'Page Load': `${pageLoadTime}ms ${result.success ? '✅' : '❌'}`,
        'Queries': `${recentQueries.length} total, ${slowQueries.length} slow`,
        'Avg Query Time': `${Math.round(avgQueryTime)}ms`,
        'Console Errors': consoleErrors.length
      });
      
      return result;
      
    } catch (error) {
      console.error(`❌ ${module.name} test failed:`, error.message);
      return {
        module: module.name,
        path: module.path,
        error: error.message,
        success: false,
        timestamp: new Date().toISOString()
      };
    }
  }

  async runFullSuite() {
    console.log('🎯 Starting Comprehensive Performance Test Suite');
    console.log('=' .repeat(60));
    
    try {
      // Initialize browser
      await this.initialize();
      
      // Test authentication
      const authResult = await this.login();
      this.results.push({ type: 'auth', ...authResult });
      
      if (!authResult.success) {
        throw new Error('Authentication failed - aborting test suite');
      }
      
      // Test each module
      for (const module of TEST_CONFIG.modules) {
        const moduleResult = await this.testModule(module);
        this.results.push({ type: 'module', ...moduleResult });
        
        // Brief pause between tests
        await this.page.waitForTimeout(1000);
      }
      
      // Generate report
      this.generateReport();
      
    } catch (error) {
      console.error('💥 Test suite failed:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  generateReport() {
    console.log('\n📋 PERFORMANCE TEST REPORT');
    console.log('=' .repeat(60));
    
    const moduleResults = this.results.filter(r => r.type === 'module');
    const successfulModules = moduleResults.filter(r => r.success);
    const failedModules = moduleResults.filter(r => !r.success);
    
    console.log(`\n🎯 SUMMARY:`);
    console.log(`✅ Successful Modules: ${successfulModules.length}/${moduleResults.length}`);
    console.log(`❌ Failed Modules: ${failedModules.length}/${moduleResults.length}`);
    console.log(`📊 Success Rate: ${Math.round((successfulModules.length / moduleResults.length) * 100)}%`);
    
    if (successfulModules.length > 0) {
      const avgPageLoad = Math.round(
        successfulModules.reduce((sum, r) => sum + r.pageLoadTime, 0) / successfulModules.length
      );
      const avgQueryTime = Math.round(
        successfulModules.reduce((sum, r) => sum + r.averageQueryTime, 0) / successfulModules.length
      );
      
      console.log(`⚡ Average Page Load: ${avgPageLoad}ms`);
      console.log(`🔍 Average Query Time: ${avgQueryTime}ms`);
    }
    
    console.log(`\n📈 DETAILED RESULTS:`);
    moduleResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const loadTime = result.pageLoadTime ? `${result.pageLoadTime}ms` : 'N/A';
      const queryInfo = result.queryCount ? `${result.queryCount} queries (${result.slowQueryCount} slow)` : 'N/A';
      
      console.log(`${status} ${result.module.padEnd(15)} | Load: ${loadTime.padEnd(8)} | ${queryInfo}`);
    });
    
    if (failedModules.length > 0) {
      console.log(`\n❌ FAILED MODULES:`);
      failedModules.forEach(result => {
        console.log(`   ${result.module}: ${result.error || 'Performance target not met'}`);
      });
    }
    
    console.log('\n' + '=' .repeat(60));
  }
}

// Run the test suite
if (require.main === module) {
  const tester = new PerformanceTester();
  tester.runFullSuite().catch(console.error);
}

module.exports = PerformanceTester;
