"use client"

import { useState, useMemo } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  EnhancedDataTable, 
  TableFilters,
  MetricCard,
  TableSummary,
  DataExportDialog,
  DataImportDialog,
  type TableColumn,
  type FilterConfig,
  type ActiveFilter,
  type ExportColumn,
  type ImportColumn,
  type TableSummaryData
} from "@/components/tables"
import { CustomerFormData, CustomerDatabaseSchema } from "@/app/types/customer"
import { 
  Eye, Edit, Trash2, Mail, Phone, Building2, MapPin, 
  Star, DollarSign, Users, TrendingUp, Globe
} from "lucide-react"
import { cn } from "@/lib/utils"

interface EnhancedCustomersTableProps {
  customers: CustomerDatabaseSchema[]
  loading?: boolean
  onView?: (customer: CustomerDatabaseSchema) => void
  onEdit?: (customer: CustomerDatabaseSchema) => void
  onDelete?: (customerId: string) => void
  onExport?: (data: any[], options: any) => Promise<void>
  onImport?: (data: any[], mapping: Record<string, string>) => Promise<void>
  className?: string
}

export function EnhancedCustomersTable({
  customers,
  loading = false,
  onView,
  onEdit,
  onDelete,
  onExport,
  onImport,
  className
}: EnhancedCustomersTableProps) {
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 25 })
  const [sorting, setSorting] = useState<any[]>([])
  const [globalFilter, setGlobalFilter] = useState("")
  const [activeFilters, setActiveFilters] = useState<ActiveFilter[]>([])
  const [selection, setSelection] = useState<any>({
    selectedRows: new Set(),
    isAllSelected: false,
    isIndeterminate: false
  })

  // Table columns configuration
  const columns: TableColumn<CustomerDatabaseSchema>[] = [
    {
      id: "contact_person",
      header: "Contact Person",
      accessorKey: "contact_person",
      sortable: true,
      filterable: true,
      cell: (customer) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs">
              {customer.contact_person?.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-medium">{customer.contact_person}</p>
            {customer.title_position && (
              <p className="text-xs text-muted-foreground">{customer.title_position}</p>
            )}
          </div>
        </div>
      ),
      meta: {
        filterType: "text"
      }
    },
    {
      id: "company",
      header: "Company",
      accessorKey: "company",
      sortable: true,
      filterable: true,
      cell: (customer) => (
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <div>
            <p className="font-medium">{customer.company}</p>
            {customer.industry && (
              <p className="text-xs text-muted-foreground">{customer.industry}</p>
            )}
          </div>
        </div>
      ),
      meta: {
        filterType: "text"
      }
    },
    {
      id: "contact_info",
      header: "Contact Info",
      cell: (customer) => (
        <div className="space-y-1">
          {customer.email && (
            <div className="flex items-center gap-2 text-xs">
              <Mail className="h-3 w-3 text-muted-foreground" />
              <a href={`mailto:${customer.email}`} className="text-primary hover:underline">
                {customer.email}
              </a>
            </div>
          )}
          {customer.phone && (
            <div className="flex items-center gap-2 text-xs">
              <Phone className="h-3 w-3 text-muted-foreground" />
              <a href={`tel:${customer.phone}`} className="text-primary hover:underline">
                {customer.phone}
              </a>
            </div>
          )}
          {customer.website && (
            <div className="flex items-center gap-2 text-xs">
              <Globe className="h-3 w-3 text-muted-foreground" />
              <a 
                href={customer.website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:underline"
              >
                Website
              </a>
            </div>
          )}
        </div>
      )
    },
    {
      id: "location",
      header: "Location",
      cell: (customer) => (
        <div className="flex items-center gap-2">
          <MapPin className="h-4 w-4 text-muted-foreground" />
          <div className="text-sm">
            <p>{customer.city}</p>
            <p className="text-xs text-muted-foreground">{customer.country}</p>
          </div>
        </div>
      ),
      meta: {
        filterType: "select",
        filterOptions: [
          ...Array.from(new Set(customers.map(c => c.country).filter(Boolean)))
        ].map(country => ({ label: country!, value: country! }))
      }
    },
    {
      id: "business_type",
      header: "Business Type",
      accessorKey: "business_type",
      sortable: true,
      filterable: true,
      cell: (customer) => customer.business_type ? (
        <Badge variant="outline">{customer.business_type}</Badge>
      ) : (
        <span className="text-muted-foreground text-sm">-</span>
      ),
      meta: {
        filterType: "select",
        filterOptions: [
          ...Array.from(new Set(customers.map(c => c.business_type).filter(Boolean)))
        ].map(type => ({ label: type!, value: type! }))
      }
    },
    {
      id: "customer_tier",
      header: "Tier",
      accessorKey: "customer_tier",
      sortable: true,
      filterable: true,
      cell: (customer) => {
        const tierColors = {
          "VIP": "bg-purple-100 text-purple-800",
          "Platinum": "bg-gray-100 text-gray-800",
          "Gold": "bg-yellow-100 text-yellow-800",
          "Silver": "bg-gray-100 text-gray-600",
          "Bronze": "bg-orange-100 text-orange-800"
        }
        
        return customer.customer_tier ? (
          <Badge 
            variant="secondary" 
            className={cn(tierColors[customer.customer_tier as keyof typeof tierColors])}
          >
            <Star className="h-3 w-3 mr-1" />
            {customer.customer_tier}
          </Badge>
        ) : (
          <Badge variant="outline">Bronze</Badge>
        )
      },
      meta: {
        filterType: "select",
        filterOptions: [
          { label: "VIP", value: "VIP" },
          { label: "Platinum", value: "Platinum" },
          { label: "Gold", value: "Gold" },
          { label: "Silver", value: "Silver" },
          { label: "Bronze", value: "Bronze" }
        ]
      }
    },
    {
      id: "annual_volume",
      header: "Annual Volume",
      accessorKey: "annual_volume",
      sortable: true,
      align: "right",
      cell: (customer) => customer.annual_volume ? (
        <div className="text-right">
          <p className="font-medium">
            ${customer.annual_volume.toLocaleString()}
          </p>
          <p className="text-xs text-muted-foreground">
            {customer.currency_preference || 'USD'}
          </p>
        </div>
      ) : (
        <span className="text-muted-foreground text-sm">-</span>
      ),
      meta: {
        filterType: "number",
        format: "currency"
      }
    },
    {
      id: "status",
      header: "Status",
      accessorKey: "status",
      sortable: true,
      filterable: true,
      cell: (customer) => {
        const statusColors = {
          "Active": "bg-green-100 text-green-800",
          "Inactive": "bg-gray-100 text-gray-800",
          "Pending": "bg-yellow-100 text-yellow-800",
          "Suspended": "bg-red-100 text-red-800"
        }
        
        return (
          <Badge 
            variant="secondary"
            className={cn(statusColors[customer.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800")}
          >
            {customer.status || "Active"}
          </Badge>
        )
      },
      meta: {
        filterType: "select",
        filterOptions: [
          { label: "Active", value: "Active" },
          { label: "Inactive", value: "Inactive" },
          { label: "Pending", value: "Pending" },
          { label: "Suspended", value: "Suspended" }
        ]
      }
    }
  ]

  // Filter configuration
  const filterConfigs: FilterConfig[] = [
    {
      id: "contact_person",
      label: "Contact Person",
      type: "text",
      placeholder: "Search by name..."
    },
    {
      id: "company",
      label: "Company",
      type: "text",
      placeholder: "Search by company..."
    },
    {
      id: "country",
      label: "Country",
      type: "select",
      options: Array.from(new Set(customers.map(c => c.country).filter(Boolean)))
        .map(country => ({ label: country!, value: country! }))
    },
    {
      id: "business_type",
      label: "Business Type",
      type: "multiselect",
      options: Array.from(new Set(customers.map(c => c.business_type).filter(Boolean)))
        .map(type => ({ label: type!, value: type! }))
    },
    {
      id: "customer_tier",
      label: "Customer Tier",
      type: "multiselect",
      options: [
        { label: "VIP", value: "VIP" },
        { label: "Platinum", value: "Platinum" },
        { label: "Gold", value: "Gold" },
        { label: "Silver", value: "Silver" },
        { label: "Bronze", value: "Bronze" }
      ]
    },
    {
      id: "annual_volume",
      label: "Annual Volume",
      type: "number",
      min: 0,
      step: 1000
    }
  ]

  // Export columns configuration
  const exportColumns: ExportColumn[] = [
    { id: "contact_person", label: "Contact Person", accessor: "contact_person" },
    { id: "title_position", label: "Title/Position", accessor: "title_position" },
    { id: "company", label: "Company", accessor: "company" },
    { id: "email", label: "Email", accessor: "email" },
    { id: "phone", label: "Phone", accessor: "phone" },
    { id: "city", label: "City", accessor: "city" },
    { id: "country", label: "Country", accessor: "country" },
    { id: "business_type", label: "Business Type", accessor: "business_type" },
    { id: "industry", label: "Industry", accessor: "industry" },
    { id: "customer_tier", label: "Customer Tier", accessor: "customer_tier" },
    { id: "annual_volume", label: "Annual Volume", accessor: "annual_volume", format: "currency" },
    { id: "status", label: "Status", accessor: "status" }
  ]

  // Import columns configuration
  const importColumns: ImportColumn[] = [
    { sourceColumn: "Contact Person", targetField: "contact_person", required: true, type: "text" },
    { sourceColumn: "Company", targetField: "company", required: true, type: "text" },
    { sourceColumn: "Email", targetField: "email", required: false, type: "email" },
    { sourceColumn: "Phone", targetField: "phone", required: false, type: "text" },
    { sourceColumn: "City", targetField: "city", required: true, type: "text" },
    { sourceColumn: "Country", targetField: "country", required: true, type: "text" },
    { sourceColumn: "Business Type", targetField: "business_type", required: false, type: "text" },
    { sourceColumn: "Industry", targetField: "industry", required: false, type: "text" },
    { sourceColumn: "Annual Volume", targetField: "annual_volume", required: false, type: "number" }
  ]

  // Calculate summary data
  const summaryData: TableSummaryData = useMemo(() => {
    const totalCustomers = customers.length
    const activeCustomers = customers.filter(c => c.status === "Active" || !c.status).length
    const totalVolume = customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
    const avgVolume = totalCustomers > 0 ? totalVolume / totalCustomers : 0

    const tierDistribution = customers.reduce((acc, customer) => {
      const tier = customer.customer_tier || "Bronze"
      acc[tier] = (acc[tier] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      totalRecords: totalCustomers,
      filteredRecords: totalCustomers, // This would be calculated based on active filters
      selectedRecords: selection.selectedRows.size,
      metrics: [
        {
          label: "Total Customers",
          value: totalCustomers,
          icon: Users,
          color: "primary"
        },
        {
          label: "Active Customers",
          value: activeCustomers,
          icon: TrendingUp,
          color: "success"
        },
        {
          label: "Total Volume",
          value: totalVolume,
          format: "currency" as const,
          icon: DollarSign,
          color: "info"
        },
        {
          label: "Avg Volume",
          value: avgVolume,
          format: "currency" as const,
          icon: Star,
          color: "warning"
        }
      ],
      distribution: Object.entries(tierDistribution).map(([tier, count]) => ({
        label: tier,
        value: count,
        percentage: (count / totalCustomers) * 100
      })),
      trends: [] // Would be populated with historical data
    }
  }, [customers, selection.selectedRows.size])

  // Row actions
  const rowActions = [
    {
      label: "View Details",
      icon: Eye,
      onClick: (customer: CustomerDatabaseSchema) => onView?.(customer)
    },
    {
      label: "Edit Customer",
      icon: Edit,
      onClick: (customer: CustomerDatabaseSchema) => onEdit?.(customer)
    },
    {
      label: "Delete Customer",
      icon: Trash2,
      onClick: (customer: CustomerDatabaseSchema) => onDelete?.(customer.id),
      variant: "destructive" as const
    }
  ]

  // Bulk actions
  const bulkActions = [
    {
      label: "Export Selected",
      icon: DollarSign,
      onClick: (selectedCustomers: CustomerDatabaseSchema[]) => {
        console.log("Export selected:", selectedCustomers)
      }
    },
    {
      label: "Update Tier",
      icon: Star,
      onClick: (selectedCustomers: CustomerDatabaseSchema[]) => {
        console.log("Update tier:", selectedCustomers)
      }
    },
    {
      label: "Delete Selected",
      icon: Trash2,
      onClick: (selectedCustomers: CustomerDatabaseSchema[]) => {
        console.log("Delete selected:", selectedCustomers)
      },
      variant: "destructive" as const
    }
  ]

  return (
    <div className={cn("space-y-6", className)}>
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryData.metrics.map((metric, index) => (
          <MetricCard key={index} metric={metric} />
        ))}
      </div>

      {/* Filters */}
      <TableFilters
        filters={filterConfigs}
        activeFilters={activeFilters}
        onFiltersChange={setActiveFilters}
        onClearAll={() => setActiveFilters([])}
        variant="popover"
      />

      {/* Enhanced Data Table */}
      <EnhancedDataTable
        data={customers}
        columns={columns}
        loading={loading}
        pagination={pagination}
        onPaginationChange={setPagination}
        totalCount={customers.length}
        sorting={sorting}
        onSortingChange={setSorting}
        filters={activeFilters as any}
        onFiltersChange={setActiveFilters as any}
        globalFilter={globalFilter}
        onGlobalFilterChange={setGlobalFilter}
        selection={selection}
        onSelectionChange={setSelection}
        enableSelection={true}
        onRowClick={onView}
        rowActions={rowActions}
        bulkActions={bulkActions}
        title="Customer Database"
        description="Manage and view all customer information"
        searchPlaceholder="Search customers..."
        emptyMessage="No customers found"
        enableGlobalSearch={true}
        enableColumnFilters={true}
        enableColumnVisibility={true}
        enableExport={true}
        enableRefresh={true}
        onRefresh={() => window.location.reload()}
        onExport={(format) => console.log("Export as:", format)}
        variant="default"
        striped={true}
      />

      {/* Export/Import Dialogs */}
      {onExport && (
        <DataExportDialog
          data={customers}
          columns={exportColumns}
          onExport={onExport}
        />
      )}

      {onImport && (
        <DataImportDialog
          columns={importColumns}
          onImport={onImport}
        />
      )}
    </div>
  )
}
