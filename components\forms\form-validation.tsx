"use client"

import * as React from "react"

// Validation Rule Types
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  email?: boolean
  phone?: boolean
  url?: boolean
  custom?: (value: any) => string | undefined
  message?: string
}

export interface FieldValidation {
  [fieldName: string]: ValidationRule
}

export interface ValidationErrors {
  [fieldName: string]: string
}

export interface FormValidationConfig {
  fields: FieldValidation
  validateOnChange?: boolean
  validateOnBlur?: boolean
  showErrorsOnSubmit?: boolean
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  decimal: /^\d*\.?\d+$/,
  currency: /^\d+(\.\d{1,2})?$/,
  percentage: /^(100|[1-9]?\d)(\.\d+)?$/
}

// Common validation messages
export const VALIDATION_MESSAGES = {
  required: (field: string) => `${field} is required`,
  minLength: (field: string, min: number) => `${field} must be at least ${min} characters`,
  maxLength: (field: string, max: number) => `${field} must be no more than ${max} characters`,
  min: (field: string, min: number) => `${field} must be at least ${min}`,
  max: (field: string, max: number) => `${field} must be no more than ${max}`,
  email: (field: string) => `${field} must be a valid email address`,
  phone: (field: string) => `${field} must be a valid phone number`,
  url: (field: string) => `${field} must be a valid URL`,
  pattern: (field: string) => `${field} format is invalid`
}

// Validation function
export function validateField(
  value: any, 
  rules: ValidationRule, 
  fieldName: string = "Field"
): string | undefined {
  // Required validation
  if (rules.required && (value === undefined || value === null || value === "")) {
    return rules.message || VALIDATION_MESSAGES.required(fieldName)
  }

  // Skip other validations if value is empty and not required
  if (!value && !rules.required) {
    return undefined
  }

  const stringValue = String(value)

  // Length validations
  if (rules.minLength && stringValue.length < rules.minLength) {
    return rules.message || VALIDATION_MESSAGES.minLength(fieldName, rules.minLength)
  }

  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return rules.message || VALIDATION_MESSAGES.maxLength(fieldName, rules.maxLength)
  }

  // Numeric validations
  const numericValue = Number(value)
  if (rules.min !== undefined && !isNaN(numericValue) && numericValue < rules.min) {
    return rules.message || VALIDATION_MESSAGES.min(fieldName, rules.min)
  }

  if (rules.max !== undefined && !isNaN(numericValue) && numericValue > rules.max) {
    return rules.message || VALIDATION_MESSAGES.max(fieldName, rules.max)
  }

  // Pattern validations
  if (rules.email && !VALIDATION_PATTERNS.email.test(stringValue)) {
    return rules.message || VALIDATION_MESSAGES.email(fieldName)
  }

  if (rules.phone && !VALIDATION_PATTERNS.phone.test(stringValue)) {
    return rules.message || VALIDATION_MESSAGES.phone(fieldName)
  }

  if (rules.url && !VALIDATION_PATTERNS.url.test(stringValue)) {
    return rules.message || VALIDATION_MESSAGES.url(fieldName)
  }

  if (rules.pattern && !rules.pattern.test(stringValue)) {
    return rules.message || VALIDATION_MESSAGES.pattern(fieldName)
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value)
  }

  return undefined
}

// Validate entire form
export function validateForm(
  data: Record<string, any>, 
  config: FieldValidation
): ValidationErrors {
  const errors: ValidationErrors = {}

  Object.entries(config).forEach(([fieldName, rules]) => {
    const error = validateField(data[fieldName], rules, fieldName)
    if (error) {
      errors[fieldName] = error
    }
  })

  return errors
}

// Form validation hook
export function useFormValidation(config: FormValidationConfig) {
  const [errors, setErrors] = React.useState<ValidationErrors>({})
  const [touched, setTouched] = React.useState<Record<string, boolean>>({})
  const [isValid, setIsValid] = React.useState(false)

  // Validate single field
  const validateSingleField = React.useCallback((
    fieldName: string, 
    value: any, 
    showError: boolean = true
  ): string | undefined => {
    const rules = config.fields[fieldName]
    if (!rules) return undefined

    const error = validateField(value, rules, fieldName)
    
    if (showError) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: error || ""
      }))
    }

    return error
  }, [config.fields])

  // Validate all fields
  const validateAllFields = React.useCallback((
    data: Record<string, any>,
    showErrors: boolean = true
  ): ValidationErrors => {
    const newErrors = validateForm(data, config.fields)
    
    if (showErrors) {
      setErrors(newErrors)
    }

    const valid = Object.keys(newErrors).length === 0
    setIsValid(valid)

    return newErrors
  }, [config.fields])

  // Mark field as touched
  const touchField = React.useCallback((fieldName: string) => {
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }))
  }, [])

  // Clear field error
  const clearFieldError = React.useCallback((fieldName: string) => {
    setErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[fieldName]
      return newErrors
    })
  }, [])

  // Clear all errors
  const clearAllErrors = React.useCallback(() => {
    setErrors({})
    setTouched({})
    setIsValid(false)
  }, [])

  // Get field error (only show if touched or form submitted)
  const getFieldError = React.useCallback((fieldName: string): string | undefined => {
    return touched[fieldName] ? errors[fieldName] : undefined
  }, [errors, touched])

  // Check if field has error
  const hasFieldError = React.useCallback((fieldName: string): boolean => {
    return !!(touched[fieldName] && errors[fieldName])
  }, [errors, touched])

  return {
    errors,
    touched,
    isValid,
    validateSingleField,
    validateAllFields,
    touchField,
    clearFieldError,
    clearAllErrors,
    getFieldError,
    hasFieldError
  }
}

// Common validation configurations
export const COMMON_VALIDATIONS = {
  // Customer form validations
  customer: {
    contact_person: {
      required: true,
      minLength: 2,
      maxLength: 100
    },
    email: {
      required: true,
      email: true
    },
    phone: {
      phone: true
    },
    company: {
      required: true,
      minLength: 2,
      maxLength: 100
    },
    city: {
      required: true,
      minLength: 2,
      maxLength: 50
    },
    country: {
      required: true
    },
    annual_volume: {
      min: 0
    },
    credit_limit: {
      min: 0
    }
  },

  // Deal form validations
  deal: {
    title: {
      required: true,
      minLength: 3,
      maxLength: 100
    },
    company: {
      required: true,
      minLength: 2,
      maxLength: 100
    },
    value: {
      required: true,
      min: 0
    },
    probability: {
      required: true,
      min: 0,
      max: 100
    },
    stage: {
      required: true
    }
  },

  // User form validations
  user: {
    name: {
      required: true,
      minLength: 2,
      maxLength: 50
    },
    email: {
      required: true,
      email: true
    },
    password: {
      required: true,
      minLength: 8,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      message: "Password must contain at least one uppercase letter, one lowercase letter, and one number"
    },
    phone: {
      phone: true
    }
  },

  // Contact form validations
  contact: {
    name: {
      required: true,
      minLength: 2,
      maxLength: 100
    },
    email: {
      required: true,
      email: true
    },
    subject: {
      required: true,
      minLength: 5,
      maxLength: 200
    },
    message: {
      required: true,
      minLength: 10,
      maxLength: 1000
    }
  }
}

// Validation utilities
export const ValidationUtils = {
  // Check if email is valid
  isValidEmail: (email: string): boolean => {
    return VALIDATION_PATTERNS.email.test(email)
  },

  // Check if phone is valid
  isValidPhone: (phone: string): boolean => {
    return VALIDATION_PATTERNS.phone.test(phone)
  },

  // Check if URL is valid
  isValidUrl: (url: string): boolean => {
    return VALIDATION_PATTERNS.url.test(url)
  },

  // Sanitize input
  sanitizeInput: (input: string): string => {
    return input.trim().replace(/[<>]/g, "")
  },

  // Format phone number
  formatPhone: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, "")
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
    }
    return phone
  },

  // Format currency
  formatCurrency: (amount: number, currency: string = "USD"): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency
    }).format(amount)
  }
}
