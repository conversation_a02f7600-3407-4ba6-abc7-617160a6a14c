import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { calculateCustomerAnalytics, generateCustomerInsights } from '@/components/customers/customer-analytics'
import { CustomerFormData, mapDatabaseToForm } from '@/app/types/customer'

// Analytics calculation functions
function calculateAdvancedMetrics(customers: CustomerFormData[]) {
  const totalCustomers = customers.length
  const activeCustomers = customers.filter(c => c.status === "Active" || !c.status).length
  const totalRevenue = customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
  
  // Customer Lifetime Value (CLV) estimation
  const avgAnnualValue = totalCustomers > 0 ? totalRevenue / totalCustomers : 0
  const avgCustomerLifespan = 3.5 // years (industry average)
  const estimatedCLV = avgAnnualValue * avgCustomerLifespan
  
  // Churn rate calculation (mock - would be based on historical data)
  const churnRate = 0.15 // 15% annual churn rate
  const retentionRate = (1 - churnRate) * 100
  
  // Customer Acquisition Cost (CAC) - mock calculation
  const estimatedCAC = avgAnnualValue * 0.2 // 20% of annual value
  
  // LTV:CAC ratio
  const ltvCacRatio = estimatedCLV / estimatedCAC
  
  // Growth metrics
  const newCustomersThisMonth = customers.filter(c => {
    const customerDate = new Date(c.customer_since || Date.now())
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    return customerDate >= thirtyDaysAgo
  }).length
  
  const monthlyGrowthRate = totalCustomers > 0 ? (newCustomersThisMonth / totalCustomers) * 100 : 0
  
  return {
    totalCustomers,
    activeCustomers,
    totalRevenue,
    avgAnnualValue,
    estimatedCLV,
    churnRate: churnRate * 100,
    retentionRate,
    estimatedCAC,
    ltvCacRatio,
    newCustomersThisMonth,
    monthlyGrowthRate,
    revenuePerCustomer: avgAnnualValue,
    customerHealthScore: Math.min(100, (retentionRate + (ltvCacRatio * 10)) / 2)
  }
}

function calculateSegmentAnalytics(customers: CustomerFormData[]) {
  // Tier analysis
  const tierAnalysis = customers.reduce((acc, customer) => {
    const tier = customer.customer_tier || "Bronze"
    if (!acc[tier]) {
      acc[tier] = {
        count: 0,
        totalRevenue: 0,
        avgRevenue: 0,
        customers: []
      }
    }
    acc[tier].count++
    acc[tier].totalRevenue += customer.annual_volume || 0
    acc[tier].customers.push(customer)
    return acc
  }, {} as Record<string, any>)
  
  // Calculate averages
  Object.keys(tierAnalysis).forEach(tier => {
    tierAnalysis[tier].avgRevenue = tierAnalysis[tier].count > 0 
      ? tierAnalysis[tier].totalRevenue / tierAnalysis[tier].count 
      : 0
  })
  
  // Geographic analysis
  const geographicAnalysis = customers.reduce((acc, customer) => {
    const country = customer.country || "Unknown"
    if (!acc[country]) {
      acc[country] = {
        count: 0,
        totalRevenue: 0,
        avgRevenue: 0,
        cities: new Set()
      }
    }
    acc[country].count++
    acc[country].totalRevenue += customer.annual_volume || 0
    if (customer.city) acc[country].cities.add(customer.city)
    return acc
  }, {} as Record<string, any>)
  
  // Calculate geographic averages
  Object.keys(geographicAnalysis).forEach(country => {
    geographicAnalysis[country].avgRevenue = geographicAnalysis[country].count > 0
      ? geographicAnalysis[country].totalRevenue / geographicAnalysis[country].count
      : 0
    geographicAnalysis[country].cities = Array.from(geographicAnalysis[country].cities)
  })
  
  // Industry analysis
  const industryAnalysis = customers.reduce((acc, customer) => {
    const industry = customer.industry || "Other"
    if (!acc[industry]) {
      acc[industry] = {
        count: 0,
        totalRevenue: 0,
        avgRevenue: 0,
        topCompanies: []
      }
    }
    acc[industry].count++
    acc[industry].totalRevenue += customer.annual_volume || 0
    acc[industry].topCompanies.push({
      company: customer.company,
      revenue: customer.annual_volume || 0
    })
    return acc
  }, {} as Record<string, any>)
  
  // Calculate industry averages and sort top companies
  Object.keys(industryAnalysis).forEach(industry => {
    industryAnalysis[industry].avgRevenue = industryAnalysis[industry].count > 0
      ? industryAnalysis[industry].totalRevenue / industryAnalysis[industry].count
      : 0
    industryAnalysis[industry].topCompanies = industryAnalysis[industry].topCompanies
      .sort((a: any, b: any) => b.revenue - a.revenue)
      .slice(0, 5)
  })
  
  return {
    tierAnalysis,
    geographicAnalysis,
    industryAnalysis
  }
}

function generateTrendData(customers: CustomerFormData[], months: number = 12) {
  const trends = []
  const now = new Date()
  
  for (let i = months - 1; i >= 0; i--) {
    const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
    const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
    
    const monthCustomers = customers.filter(customer => {
      const customerDate = new Date(customer.customer_since || Date.now())
      return customerDate >= monthStart && customerDate <= monthEnd
    })
    
    const monthRevenue = monthCustomers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
    
    trends.push({
      month: monthStart.toISOString().slice(0, 7), // YYYY-MM format
      newCustomers: monthCustomers.length,
      revenue: monthRevenue,
      cumulativeCustomers: customers.filter(customer => {
        const customerDate = new Date(customer.customer_since || Date.now())
        return customerDate <= monthEnd
      }).length
    })
  }
  
  return trends
}

export async function GET(request: NextRequest) {
  try {
    // Use singleton Supabase client to prevent multiple GoTrueClient instances
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const analysisType = searchParams.get('type') || 'overview'
    const dateFrom = searchParams.get('from')
    const dateTo = searchParams.get('to')
    const segment = searchParams.get('segment')

    // Fetch customers
    let query = supabase
      .from('customers')
      .select('*')
      .eq('user_id', session.user.id)

    // Apply date filters
    if (dateFrom) {
      query = query.gte('created_at', dateFrom)
    }
    if (dateTo) {
      query = query.lte('created_at', dateTo)
    }

    // Apply segment filters
    if (segment && segment !== 'all') {
      // This could be tier, status, country, etc.
      const [field, value] = segment.split(':')
      if (field && value) {
        query = query.eq(field, value)
      }
    }

    const { data: customersData, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch customers' }, { status: 500 })
    }

    // Convert database format to form format
    const customers: CustomerFormData[] = (customersData || []).map(mapDatabaseToForm)

    // Generate analytics based on type
    switch (analysisType) {
      case 'overview':
        const overviewMetrics = calculateAdvancedMetrics(customers)
        const basicAnalytics = calculateCustomerAnalytics(customers)
        
        return NextResponse.json({
          type: 'overview',
          metrics: overviewMetrics,
          analytics: basicAnalytics,
          generatedAt: new Date().toISOString()
        })

      case 'segments':
        const segmentAnalytics = calculateSegmentAnalytics(customers)
        
        return NextResponse.json({
          type: 'segments',
          analytics: segmentAnalytics,
          generatedAt: new Date().toISOString()
        })

      case 'trends':
        const trendData = generateTrendData(customers)
        
        return NextResponse.json({
          type: 'trends',
          trends: trendData,
          generatedAt: new Date().toISOString()
        })

      case 'insights':
        const { insights, metrics } = generateCustomerInsights(customers)
        
        return NextResponse.json({
          type: 'insights',
          insights,
          metrics,
          generatedAt: new Date().toISOString()
        })

      case 'full':
        // Return comprehensive analytics
        const fullMetrics = calculateAdvancedMetrics(customers)
        const fullAnalytics = calculateCustomerAnalytics(customers)
        const fullSegments = calculateSegmentAnalytics(customers)
        const fullTrends = generateTrendData(customers)
        const fullInsights = generateCustomerInsights(customers)
        
        return NextResponse.json({
          type: 'full',
          metrics: fullMetrics,
          analytics: fullAnalytics,
          segments: fullSegments,
          trends: fullTrends,
          insights: fullInsights.insights,
          insightMetrics: fullInsights.metrics,
          generatedAt: new Date().toISOString(),
          dataQuality: {
            totalRecords: customers.length,
            completeProfiles: customers.filter(c => 
              c.contact_person && c.email && c.company && c.city && c.country
            ).length,
            withPhoneNumbers: customers.filter(c => c.phone || c.mobile).length,
            withAnnualVolume: customers.filter(c => c.annual_volume && c.annual_volume > 0).length,
            completenessScore: customers.length > 0 
              ? Math.round((customers.filter(c => 
                  c.contact_person && c.email && c.company && c.city && c.country
                ).length / customers.length) * 100)
              : 0
          }
        })

      default:
        return NextResponse.json({ error: 'Invalid analysis type' }, { status: 400 })
    }

  } catch (error) {
    console.error('Analytics API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: (error as Error).message },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Use singleton Supabase client to prevent multiple GoTrueClient instances
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, parameters } = body

    switch (action) {
      case 'export_report':
        // Generate and return downloadable report
        const { data: customersData } = await supabase
          .from('customers')
          .select('*')
          .eq('user_id', session.user.id)

        const customers: CustomerFormData[] = (customersData || []).map(mapDatabaseToForm)
        const analytics = calculateCustomerAnalytics(customers)
        const insights = generateCustomerInsights(customers)
        
        const report = {
          generatedAt: new Date().toISOString(),
          summary: calculateAdvancedMetrics(customers),
          analytics,
          insights: insights.insights,
          recommendations: insights.insights
            .filter(i => i.type === 'recommendation' && i.actionable)
            .map(i => ({
              title: i.title,
              description: i.description,
              priority: i.priority,
              impact: i.impact,
              confidence: i.confidence
            }))
        }

        return NextResponse.json({
          success: true,
          report,
          downloadUrl: null // In production, this would be a signed URL to download the report
        })

      case 'save_insight':
        // Save custom insight or bookmark
        const { insight } = parameters
        
        // In a real implementation, you would save this to a user_insights table
        return NextResponse.json({
          success: true,
          message: 'Insight saved successfully'
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('Analytics POST error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
