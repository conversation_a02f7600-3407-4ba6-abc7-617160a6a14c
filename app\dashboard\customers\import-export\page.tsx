"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { 
  CustomerExportDialog, 
  CustomerImportDialog,
  type CustomerExportOptions,
  type CustomerImportMapping
} from "@/components/customers/customer-import-export"
import { CustomerFormData, CustomerDatabaseSchema } from "@/app/types/customer"
import { 
  Download, Upload, FileText, Users, Database, 
  CheckCircle, AlertCircle, Clock, TrendingUp,
  FileSpreadsheet, FileCheck, History
} from "lucide-react"
import { cn } from "@/lib/utils"
import { toast } from "sonner"

interface ImportExportStats {
  totalCustomers: number
  lastExportDate?: string
  lastImportDate?: string
  exportCount: number
  importCount: number
  recentActivity: Array<{
    id: string
    type: "import" | "export"
    date: string
    recordCount: number
    status: "success" | "error" | "partial"
    filename?: string
  }>
}

export default function CustomerImportExportPage() {
  const [customers, setCustomers] = useState<CustomerDatabaseSchema[]>([])
  const [stats, setStats] = useState<ImportExportStats>({
    totalCustomers: 0,
    exportCount: 0,
    importCount: 0,
    recentActivity: []
  })
  const [loading, setLoading] = useState(true)

  // Fetch customers and stats
  useEffect(() => {
    fetchCustomers()
    fetchStats()
  }, [])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      if (response.ok) {
        const data = await response.json()
        setCustomers(data.customers || [])
      }
    } catch (error) {
      console.error('Failed to fetch customers:', error)
      toast.error('Failed to load customers')
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      // Mock stats - in real implementation, fetch from API
      setStats({
        totalCustomers: customers.length,
        lastExportDate: '2024-01-15',
        lastImportDate: '2024-01-10',
        exportCount: 12,
        importCount: 8,
        recentActivity: [
          {
            id: '1',
            type: 'export',
            date: '2024-01-15T10:30:00Z',
            recordCount: 150,
            status: 'success',
            filename: 'customers_export_2024-01-15.csv'
          },
          {
            id: '2',
            type: 'import',
            date: '2024-01-10T14:20:00Z',
            recordCount: 45,
            status: 'success',
            filename: 'new_customers.xlsx'
          },
          {
            id: '3',
            type: 'export',
            date: '2024-01-08T09:15:00Z',
            recordCount: 200,
            status: 'success',
            filename: 'customers_backup.json'
          },
          {
            id: '4',
            type: 'import',
            date: '2024-01-05T16:45:00Z',
            recordCount: 25,
            status: 'partial',
            filename: 'customer_updates.csv'
          }
        ]
      })
    } catch (error) {
      console.error('Failed to fetch stats:', error)
    }
  }

  const handleExport = async (data: any[], options: CustomerExportOptions) => {
    try {
      const response = await fetch('/api/customers/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerIds: customers.map(c => c.id),
          options
        })
      })

      if (response.ok) {
        // Create download link
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = response.headers.get('Content-Disposition')?.split('filename=')[1]?.replace(/"/g, '') || 'customers_export.csv'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        toast.success(`Successfully exported ${data.length} customers`)
        
        // Update stats
        setStats(prev => ({
          ...prev,
          exportCount: prev.exportCount + 1,
          lastExportDate: new Date().toISOString().split('T')[0],
          recentActivity: [
            {
              id: Date.now().toString(),
              type: 'export',
              date: new Date().toISOString(),
              recordCount: data.length,
              status: 'success',
              filename: a.download
            },
            ...prev.recentActivity.slice(0, 9)
          ]
        }))
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Export failed')
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error(`Export failed: ${(error as Error).message}`)
    }
  }

  const handleImport = async (data: CustomerFormData[], mapping: CustomerImportMapping) => {
    try {
      const response = await fetch('/api/customers/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customers: data,
          mapping,
          options: {
            skipDuplicates: true,
            updateExisting: false
          }
        })
      })

      const result = await response.json()

      if (response.ok) {
        toast.success(`Successfully imported ${result.successful} customers`)
        
        if (result.failed > 0) {
          toast.warning(`${result.failed} records failed to import`)
        }
        
        if (result.skipped > 0) {
          toast.info(`${result.skipped} duplicate records were skipped`)
        }

        // Refresh customers list
        await fetchCustomers()
        
        // Update stats
        setStats(prev => ({
          ...prev,
          importCount: prev.importCount + 1,
          lastImportDate: new Date().toISOString().split('T')[0],
          totalCustomers: prev.totalCustomers + result.successful,
          recentActivity: [
            {
              id: Date.now().toString(),
              type: 'import',
              date: new Date().toISOString(),
              recordCount: result.successful,
              status: result.failed > 0 ? 'partial' : 'success'
            },
            ...prev.recentActivity.slice(0, 9)
          ]
        }))
      } else {
        throw new Error(result.error || 'Import failed')
      }
    } catch (error) {
      console.error('Import error:', error)
      toast.error(`Import failed: ${(error as Error).message}`)
    }
  }

  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/customers/export?template=import')
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'customer_import_template.csv'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        toast.success('Template downloaded successfully')
      } else {
        throw new Error('Failed to download template')
      }
    } catch (error) {
      console.error('Template download error:', error)
      toast.error('Failed to download template')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'partial':
        return <AlertCircle className="h-4 w-4 text-yellow-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getActivityIcon = (type: string) => {
    return type === 'import' ? 
      <Upload className="h-4 w-4" /> : 
      <Download className="h-4 w-4" />
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="heading-1 text-foreground">Customer Import & Export</h1>
          <p className="body-large text-muted-foreground mt-2">
            Manage customer data with bulk import and export operations
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={downloadTemplate} className="gap-2">
            <FileText className="h-4 w-4" />
            Download Template
          </Button>
          
          <CustomerImportDialog
            onImport={handleImport}
            trigger={
              <Button variant="outline" className="gap-2">
                <Upload className="h-4 w-4" />
                Import Customers
              </Button>
            }
          />
          
          <CustomerExportDialog
            customers={customers}
            onExport={handleExport}
            trigger={
              <Button className="gap-2">
                <Download className="h-4 w-4" />
                Export Customers
              </Button>
            }
          />
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{stats.totalCustomers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Download className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Exports</p>
                <p className="text-2xl font-bold">{stats.exportCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Upload className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Imports</p>
                <p className="text-2xl font-bold">{stats.importCount}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Last Activity</p>
                <p className="text-sm font-medium">
                  {stats.recentActivity[0] ? 
                    new Date(stats.recentActivity[0].date).toLocaleDateString() : 
                    'No activity'
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Customers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Upload customer data from CSV, Excel, or JSON files with intelligent field mapping and validation.
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Automatic field mapping</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Data validation and error reporting</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Duplicate detection and handling</span>
              </div>
            </div>
            
            <div className="flex gap-2">
              <CustomerImportDialog
                onImport={handleImport}
                trigger={
                  <Button className="flex-1">
                    Start Import
                  </Button>
                }
              />
              <Button variant="outline" onClick={downloadTemplate}>
                Get Template
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Download className="h-5 w-5" />
              Export Customers
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Export customer data in multiple formats with customizable column selection and filtering options.
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Multiple export formats (CSV, Excel, JSON)</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Customizable column selection</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Advanced filtering and date formatting</span>
              </div>
            </div>
            
            <CustomerExportDialog
              customers={customers}
              onExport={handleExport}
              trigger={
                <Button className="w-full">
                  Start Export
                </Button>
              }
            />
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          {stats.recentActivity.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No recent import/export activity</p>
            </div>
          ) : (
            <div className="space-y-3">
              {stats.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "p-2 rounded-lg",
                      activity.type === 'import' ? "bg-purple-100" : "bg-green-100"
                    )}>
                      {getActivityIcon(activity.type)}
                    </div>
                    
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium capitalize">{activity.type}</span>
                        {getStatusIcon(activity.status)}
                        <Badge variant="outline" className="text-xs">
                          {activity.recordCount} records
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {activity.filename && `${activity.filename} • `}
                        {new Date(activity.date).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  
                  <Badge 
                    variant={activity.status === 'success' ? 'default' : 
                            activity.status === 'error' ? 'destructive' : 'secondary'}
                  >
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Help Section */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Need help?</strong> Download the import template to see the required format, 
          or contact support for assistance with large data migrations.
        </AlertDescription>
      </Alert>
    </div>
  )
}
