"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface DashboardLayoutProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  padding?: "sm" | "md" | "lg"
}

export function DashboardLayout({ 
  children, 
  className,
  maxWidth = "full",
  padding = "md"
}: DashboardLayoutProps) {
  const maxWidthClasses = {
    sm: "max-w-screen-sm",
    md: "max-w-screen-md", 
    lg: "max-w-screen-lg",
    xl: "max-w-screen-xl",
    "2xl": "max-w-screen-2xl",
    full: "max-w-none"
  }

  const paddingClasses = {
    sm: "p-4",
    md: "p-6",
    lg: "p-8"
  }

  return (
    <div className={cn(
      "container mx-auto space-y-6",
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  )
}

interface DashboardSectionProps {
  children: React.ReactNode
  className?: string
  spacing?: "sm" | "md" | "lg"
}

export function DashboardSection({ 
  children, 
  className,
  spacing = "md"
}: DashboardSectionProps) {
  const spacingClasses = {
    sm: "space-y-4",
    md: "space-y-6", 
    lg: "space-y-8"
  }

  return (
    <section className={cn(
      spacingClasses[spacing],
      className
    )}>
      {children}
    </section>
  )
}

interface DashboardGridProps {
  children: React.ReactNode
  className?: string
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: "sm" | "md" | "lg"
}

export function DashboardGrid({ 
  children, 
  className,
  cols = { default: 1, sm: 2, lg: 4 },
  gap = "md"
}: DashboardGridProps) {
  const gapClasses = {
    sm: "gap-4",
    md: "gap-6",
    lg: "gap-8"
  }

  const getGridCols = () => {
    const classes = []
    if (cols.default) classes.push(`grid-cols-${cols.default}`)
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`)
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`)
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`)
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`)
    return classes.join(" ")
  }

  return (
    <div className={cn(
      "grid",
      getGridCols(),
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}

interface DashboardContentProps {
  children: React.ReactNode
  className?: string
}

export function DashboardContent({ children, className }: DashboardContentProps) {
  return (
    <main className={cn("flex-1 space-y-6", className)}>
      {children}
    </main>
  )
}

// Responsive breakpoint utilities
export const breakpoints = {
  sm: "640px",
  md: "768px", 
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px"
} as const

// Common spacing values
export const spacing = {
  xs: "0.25rem", // 4px
  sm: "0.5rem",  // 8px
  md: "1rem",    // 16px
  lg: "1.5rem",  // 24px
  xl: "2rem",    // 32px
  "2xl": "3rem", // 48px
  "3xl": "4rem", // 64px
} as const

// Layout constants for consistency
export const layoutConstants = {
  headerHeight: "4rem",
  sidebarWidth: "16rem",
  sidebarCollapsedWidth: "4rem",
  maxContentWidth: "1200px",
  cardPadding: "1.5rem",
  sectionSpacing: "2rem",
} as const
