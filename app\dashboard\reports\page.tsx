"use client"

import React, { useState, use<PERSON>ffect, use<PERSON>emo } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>rendingUp, Users, DollarSign, Target, Calendar, Download, Filter, RefreshCw, <PERSON><PERSON><PERSON>, Activity } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/components/auth-provider"
import { useLanguage } from "@/components/language-provider"
import { supabase } from "@/lib/supabase"

interface Customer {
  id: string
  name?: string
  status: string
  created_at: string
  user_id: string
}

interface Lead {
  id: string
  name: string
  status: string
  value?: number
  source: string
  created_at: string
  user_id: string
}

interface Opportunity {
  id: string
  name: string
  value: number
  stage: string
  probability: number
  created_at: string
  user_id: string
}

interface Company {
  id: string
  name: string
  status: string
  created_at: string
  user_id: string
}

export default function ReportsPage() {
  const { t } = useLanguage()
  const { toast } = useToast()
  const { user } = useAuth()
  const [dateRange, setDateRange] = useState("30")
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // State for data
  const [customers, setCustomers] = useState<Customer[]>([])
  const [leads, setLeads] = useState<Lead[]>([])
  const [opportunities, setOpportunities] = useState<Opportunity[]>([])
  const [companies, setCompanies] = useState<Company[]>([])

  // CRITICAL FIX: Direct data fetching to prevent authentication loops
  const fetchData = async () => {
    if (!user) return

    try {
      setIsLoading(true)
      console.log('🔄 [REPORTS] Fetching data directly from Supabase...')

      // Fetch all data in parallel
      const [customersResult, leadsResult, opportunitiesResult, companiesResult] = await Promise.all([
        supabase.from('customers').select('*'),
        supabase.from('leads').select('*'),
        supabase.from('opportunities').select('*'),
        supabase.from('companies').select('*')
      ])

      if (customersResult.data) setCustomers(customersResult.data)
      if (leadsResult.data) setLeads(leadsResult.data)
      if (opportunitiesResult.data) setOpportunities(opportunitiesResult.data)
      if (companiesResult.data) setCompanies(companiesResult.data)

      console.log('✅ [REPORTS] Data fetched successfully')
    } catch (error) {
      console.error('❌ [REPORTS] Error fetching data:', error)
      toast({
        title: "Error",
        description: "Failed to load reports data. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data when user is available
  useEffect(() => {
    if (user) {
      fetchData()
    }
  }, [user])

  // Filter data by date range
  const getFilteredData = (data: any[], dateRange: string) => {
    const now = new Date()
    const daysAgo = parseInt(dateRange)
    const cutoffDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
    
    return data.filter(item => new Date(item.created_at) >= cutoffDate)
  }

  // Calculate metrics
  const metrics = useMemo(() => {
    const filteredCustomers = getFilteredData(customers, dateRange)
    const filteredLeads = getFilteredData(leads, dateRange)
    const filteredOpportunities = getFilteredData(opportunities, dateRange)
    const filteredCompanies = getFilteredData(companies, dateRange)

    // Customer metrics
    const totalCustomers = customers.length
    const newCustomers = filteredCustomers.length
    const activeCustomers = customers.filter(c => c.status === "Active").length

    // Lead metrics
    const totalLeads = leads.length
    const newLeads = filteredLeads.length
    const qualifiedLeads = leads.filter(l => l.status === "Qualified").length
    const wonLeads = leads.filter(l => l.status === "Won").length
    const conversionRate = totalLeads > 0 ? (wonLeads / totalLeads) * 100 : 0

    // Opportunity metrics
    const totalOpportunities = opportunities.length
    const newOpportunities = filteredOpportunities.length
    const totalOpportunityValue = opportunities.reduce((sum, o) => sum + (o.value || 0), 0)
    const weightedOpportunityValue = opportunities.reduce((sum, o) => sum + ((o.value || 0) * (o.probability || 0) / 100), 0)
    const wonOpportunities = opportunities.filter(o => o.stage === "Closed Won")
    const wonValue = wonOpportunities.reduce((sum, o) => sum + (o.value || 0), 0)

    // Company metrics
    const totalCompanies = companies.length
    const newCompanies = filteredCompanies.length
    const activeCompanies = companies.filter(c => c.status === "Active").length

    // Lead sources
    const leadSources = leads.reduce((acc, lead) => {
      acc[lead.source] = (acc[lead.source] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    // Opportunity stages
    const opportunityStages = opportunities.reduce((acc, opp) => {
      acc[opp.stage] = (acc[opp.stage] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    return {
      customers: {
        total: totalCustomers,
        new: newCustomers,
        active: activeCustomers,
        growth: totalCustomers > 0 ? (newCustomers / totalCustomers) * 100 : 0
      },
      leads: {
        total: totalLeads,
        new: newLeads,
        qualified: qualifiedLeads,
        won: wonLeads,
        conversionRate,
        sources: leadSources
      },
      opportunities: {
        total: totalOpportunities,
        new: newOpportunities,
        totalValue: totalOpportunityValue,
        weightedValue: weightedOpportunityValue,
        wonValue,
        stages: opportunityStages
      },
      companies: {
        total: totalCompanies,
        new: newCompanies,
        active: activeCompanies
      }
    }
  }, [customers, leads, opportunities, companies, dateRange])

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate refresh delay
    setTimeout(() => {
      setIsRefreshing(false)
      toast({
        title: "Reports Updated",
        description: "All data has been refreshed successfully.",
      })
    }, 1000)
  }

  const handleExport = (reportType: string) => {
    toast({
      title: "Export Started",
      description: `${reportType} report is being prepared for download.`,
    })
  }

  const MetricCard = ({ title, value, change, icon: Icon, color = "blue" }: {
    title: string
    value: string | number
    change?: number
    icon: any
    color?: string
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-3xl font-bold">{value}</p>
            {change !== undefined && (
              <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {change >= 0 ? '+' : ''}{change.toFixed(1)}% from last period
              </p>
            )}
          </div>
          <div className={`p-3 rounded-lg bg-${color}-100`}>
            <Icon className={`h-6 w-6 text-${color}-600`} />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const ProgressCard = ({ title, items }: {
    title: string
    items: { label: string; value: number; total: number; color: string }[]
  }) => (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((item, index) => (
          <div key={index}>
            <div className="flex justify-between text-sm mb-2">
              <span>{item.label}</span>
              <span>{item.value} / {item.total}</span>
            </div>
            <Progress 
              value={item.total > 0 ? (item.value / item.total) * 100 : 0} 
              className="h-2"
            />
          </div>
        ))}
      </CardContent>
    </Card>
  )

  // CRITICAL FIX: Simple authentication check without loops
  if (!user) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-3xl font-bold">Reports</h2>
            <p className="text-gray-500 mt-2">Analytics and insights for your business performance</p>
          </div>
        </div>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <p className="text-muted-foreground">Please log in to access reports.</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold">{t("Reports")}</h2>
          <p className="text-gray-500 mt-2">
            Analytics and insights for your business performance.
          </p>
        </div>
        <div className="flex space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => handleExport("Summary")}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading reports...</p>
        </div>
      ) : (
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <MetricCard
                title="Total Customers"
                value={metrics.customers.total}
                change={metrics.customers.growth}
                icon={Users}
                color="blue"
              />
              <MetricCard
                title="Total Leads"
                value={metrics.leads.total}
                change={metrics.leads.new > 0 ? 15.2 : -5.1}
                icon={TrendingUp}
                color="green"
              />
              <MetricCard
                title="Pipeline Value"
                value={`$${metrics.opportunities.totalValue.toLocaleString()}`}
                change={12.5}
                icon={DollarSign}
                color="yellow"
              />
              <MetricCard
                title="Won Deals"
                value={`$${metrics.opportunities.wonValue.toLocaleString()}`}
                change={8.3}
                icon={Target}
                color="purple"
              />
            </div>

            {/* Charts and Progress */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ProgressCard
                title="Lead Sources"
                items={Object.entries(metrics.leads.sources).map(([source, count]) => ({
                  label: source,
                  value: count,
                  total: metrics.leads.total,
                  color: "blue"
                }))}
              />
              <ProgressCard
                title="Opportunity Stages"
                items={Object.entries(metrics.opportunities.stages).map(([stage, count]) => ({
                  label: stage,
                  value: count,
                  total: metrics.opportunities.total,
                  color: "purple"
                }))}
              />
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Advanced Analytics Header */}
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <div>
                <h2 className="text-2xl font-bold">Advanced Analytics</h2>
                <p className="text-muted-foreground">Interactive charts and detailed insights</p>
              </div>
              <div className="flex gap-2">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">Last 7 days</SelectItem>
                    <SelectItem value="30">Last 30 days</SelectItem>
                    <SelectItem value="90">Last 90 days</SelectItem>
                    <SelectItem value="365">Last year</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsRefreshing(true)}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
            </div>

            {/* Advanced Analytics Placeholder */}
            <div className="grid gap-6 grid-cols-1 xl:grid-cols-2">
              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Sales Pipeline Analysis</CardTitle>
                  <p className="text-sm text-muted-foreground">Detailed pipeline breakdown with probabilities</p>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[350px] text-muted-foreground">
                    <div className="text-center">
                      <BarChart className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Advanced Pipeline Analytics</p>
                      <p className="text-sm mt-2">Interactive charts with drill-down capabilities</p>
                      <p className="text-xs mt-1 text-muted-foreground">Coming soon in next update</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Revenue Performance</CardTitle>
                  <p className="text-sm text-muted-foreground">Monthly trends with targets and growth</p>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[350px] text-muted-foreground">
                    <div className="text-center">
                      <TrendingUp className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Revenue Analytics</p>
                      <p className="text-sm mt-2">Real-time performance tracking</p>
                      <p className="text-xs mt-1 text-muted-foreground">Enhanced visualizations coming soon</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Analytics Placeholders */}
            <div className="grid gap-6 grid-cols-1 xl:grid-cols-2">
              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Lead Conversion Funnel</CardTitle>
                  <p className="text-sm text-muted-foreground">Source analysis and conversion rates</p>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[400px] text-muted-foreground">
                    <div className="text-center">
                      <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Lead Analytics</p>
                      <p className="text-sm mt-2">Conversion tracking and optimization</p>
                      <p className="text-xs mt-1 text-muted-foreground">Interactive funnel analysis coming soon</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="col-span-1">
                <CardHeader>
                  <CardTitle>Customer Segmentation</CardTitle>
                  <p className="text-sm text-muted-foreground">Distribution by tier and value</p>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center h-[400px] text-muted-foreground">
                    <div className="text-center">
                      <Target className="h-16 w-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">Customer Insights</p>
                      <p className="text-sm mt-2">Tier-based segmentation analysis</p>
                      <p className="text-xs mt-1 text-muted-foreground">Advanced segmentation coming soon</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Advanced Metrics Grid */}
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {metrics.leads.total > 0 ?
                      `${((metrics.leads.qualified / metrics.leads.total) * 100).toFixed(1)}%` :
                      '0%'
                    }
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {metrics.leads.qualified} of {metrics.leads.total} leads qualified
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Deal Size</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${metrics.opportunities.total > 0 ?
                      Math.round(metrics.opportunities.totalValue / metrics.opportunities.total).toLocaleString() :
                      '0'
                    }
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Average opportunity value
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {metrics.opportunities.total > 0 ?
                      `${(((metrics.opportunities.stages["Closed Won"] || 0) / metrics.opportunities.total) * 100).toFixed(1)}%` :
                      '0%'
                    }
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {metrics.opportunities.stages["Closed Won"] || 0} deals won
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Pipeline Velocity</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    24
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Average days to close
                  </p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sales" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <MetricCard
                title="Total Opportunities"
                value={metrics.opportunities.total}
                change={metrics.opportunities.new > 0 ? 10.5 : -2.1}
                icon={Target}
                color="purple"
              />
              <MetricCard
                title="Weighted Pipeline"
                value={`$${metrics.opportunities.weightedValue.toLocaleString()}`}
                change={15.8}
                icon={DollarSign}
                color="green"
              />
              <MetricCard
                title="Conversion Rate"
                value={`${metrics.leads.conversionRate.toFixed(1)}%`}
                change={2.3}
                icon={TrendingUp}
                color="blue"
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Sales Performance</CardTitle>
                <CardDescription>
                  Detailed breakdown of your sales metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">Total Pipeline Value</p>
                      <p className="text-sm text-gray-500">All active opportunities</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold">${metrics.opportunities.totalValue.toLocaleString()}</p>
                      <Badge variant="outline">Active</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium">Won Deals Value</p>
                      <p className="text-sm text-gray-500">Closed won opportunities</p>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold text-green-600">${metrics.opportunities.wonValue.toLocaleString()}</p>
                      <Badge className="bg-green-100 text-green-800">Won</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="customers" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <MetricCard
                title="Total Customers"
                value={metrics.customers.total}
                change={metrics.customers.growth}
                icon={Users}
                color="blue"
              />
              <MetricCard
                title="Active Customers"
                value={metrics.customers.active}
                change={5.2}
                icon={TrendingUp}
                color="green"
              />
              <MetricCard
                title="New Customers"
                value={metrics.customers.new}
                change={metrics.customers.growth}
                icon={Users}
                color="purple"
              />
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Customer Insights</CardTitle>
                <CardDescription>
                  Understanding your customer base
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium mb-3">Customer Status Distribution</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Active</span>
                        <span className="font-medium">{metrics.customers.active}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Total</span>
                        <span className="font-medium">{metrics.customers.total}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-3">Growth Metrics</h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>New This Period</span>
                        <span className="font-medium">{metrics.customers.new}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Growth Rate</span>
                        <span className="font-medium">{metrics.customers.growth.toFixed(1)}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Key Performance Indicators</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Lead Conversion Rate</span>
                    <span className="font-bold">{metrics.leads.conversionRate.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Average Deal Size</span>
                    <span className="font-bold">
                      ${metrics.opportunities.total > 0 
                        ? (metrics.opportunities.totalValue / metrics.opportunities.total).toLocaleString() 
                        : '0'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Pipeline Velocity</span>
                    <span className="font-bold">24 days</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Win Rate</span>
                    <span className="font-bold">
                      {metrics.opportunities.total > 0 
                        ? ((metrics.opportunities.stages["Closed Won"] || 0) / metrics.opportunities.total * 100).toFixed(1)
                        : '0'}%
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Activity Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Companies</span>
                    <span className="font-bold">{metrics.companies.total}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Active Companies</span>
                    <span className="font-bold">{metrics.companies.active}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Qualified Leads</span>
                    <span className="font-bold">{metrics.leads.qualified}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Won Leads</span>
                    <span className="font-bold">{metrics.leads.won}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  )
}