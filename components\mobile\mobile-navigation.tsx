"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Menu, X, Home, Users, Briefcase, Calendar, BarChart,
  Settings, User, ChevronRight, Bell, Search, Plus
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

// Navigation Types
export interface MobileNavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string | number
  description?: string
  children?: MobileNavItem[]
}

export interface MobileNavGroup {
  title: string
  items: MobileNavItem[]
}

// Navigation Configuration
const navigationGroups: MobileNavGroup[] = [
  {
    title: "Overview",
    items: [
      {
        title: "Dashboard",
        href: "/dashboard",
        icon: Home,
        description: "Overview and analytics"
      }
    ]
  },
  {
    title: "CRM",
    items: [
      {
        title: "Customers",
        href: "/dashboard/customers",
        icon: Users,
        badge: "12",
        description: "Manage customer relationships"
      },

      {
        title: "Tasks",
        href: "/dashboard/tasks",
        icon: Calendar,
        description: "Manage your tasks"
      }
    ]
  },
  {
    title: "Analytics",
    items: [
      {
        title: "Reports",
        href: "/dashboard/reports",
        icon: BarChart,
        description: "Business insights"
      }
    ]
  },
  {
    title: "Settings",
    items: [
      {
        title: "Profile",
        href: "/dashboard/profile",
        icon: User,
        description: "Your account settings"
      },
      {
        title: "System",
        href: "/dashboard/settings",
        icon: Settings,
        description: "System configuration"
      }
    ]
  }
]

// Mobile Navigation Item Component
function MobileNavItemComponent({
  item,
  onItemClick,
  level = 0
}: {
  item: MobileNavItem
  onItemClick?: () => void
  level?: number
}) {
  const pathname = usePathname()
  const isActive = pathname === item.href
  const hasChildren = item.children && item.children.length > 0
  const [isExpanded, setIsExpanded] = React.useState(false)

  const handleClick = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded)
    } else {
      onItemClick?.()
    }
  }

  return (
    <div>
      <div
        className={cn(
          "flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200",
          "hover:bg-accent/50 active:bg-accent/70",
          level > 0 && "ml-4 border-l-2 border-muted pl-6",
          isActive && "bg-primary text-primary-foreground shadow-sm"
        )}
        style={{ paddingLeft: `${16 + level * 16}px` }}
      >
        {hasChildren ? (
          <button
            onClick={handleClick}
            className="flex items-center gap-3 flex-1 text-left"
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="font-medium truncate">{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.description && (
                <p className="text-xs text-muted-foreground truncate">
                  {item.description}
                </p>
              )}
            </div>
            <ChevronRight 
              className={cn(
                "h-4 w-4 transition-transform duration-200",
                isExpanded && "rotate-90"
              )} 
            />
          </button>
        ) : (
          <Link
            href={item.href}
            onClick={handleClick}
            className="flex items-center gap-3 flex-1"
          >
            <item.icon className="h-5 w-5 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2">
                <span className="font-medium truncate">{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.description && (
                <p className="text-xs text-muted-foreground truncate">
                  {item.description}
                </p>
              )}
            </div>
          </Link>
        )}
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="mt-1 space-y-1">
          {item.children!.map((child) => (
            <MobileNavItemComponent
              key={child.href}
              item={child}
              onItemClick={onItemClick}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Mobile Navigation Menu
export function MobileNavigation({
  trigger,
  onNavigate
}: {
  trigger?: React.ReactNode
  onNavigate?: () => void
}) {
  const [isOpen, setIsOpen] = React.useState(false)
  const isMobile = useIsMobile()

  const handleItemClick = () => {
    setIsOpen(false)
    onNavigate?.()
  }

  if (!isMobile) {
    return null
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        {trigger || (
          <Button variant="ghost" size="sm" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Open navigation menu</span>
          </Button>
        )}
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-4 border-b">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-lg font-semibold">
                Nawras CRM
              </SheetTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </SheetHeader>

          {/* Quick Actions */}
          <div className="p-4 border-b">
            <div className="grid grid-cols-3 gap-2">
              <Button variant="outline" size="sm" className="flex-col h-auto py-3">
                <Plus className="h-4 w-4 mb-1" />
                <span className="text-xs">Add</span>
              </Button>
              <Button variant="outline" size="sm" className="flex-col h-auto py-3">
                <Search className="h-4 w-4 mb-1" />
                <span className="text-xs">Search</span>
              </Button>
              <Button variant="outline" size="sm" className="flex-col h-auto py-3">
                <Bell className="h-4 w-4 mb-1" />
                <span className="text-xs">Alerts</span>
              </Button>
            </div>
          </div>

          {/* Navigation */}
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-6">
              {navigationGroups.map((group) => (
                <div key={group.title}>
                  <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
                    {group.title}
                  </h3>
                  <div className="space-y-1">
                    {group.items.map((item) => (
                      <MobileNavItemComponent
                        key={item.href}
                        item={item}
                        onItemClick={handleItemClick}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Footer */}
          <div className="p-4 border-t">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <User className="h-4 w-4 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">John Doe</p>
                <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
              </div>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

// Mobile Bottom Navigation
export function MobileBottomNavigation({
  className
}: {
  className?: string
}) {
  const pathname = usePathname()
  const isMobile = useIsMobile()

  const bottomNavItems = [
    { title: "Home", href: "/dashboard", icon: Home },
    { title: "Customers", href: "/dashboard/customers", icon: Users },
    { title: "Tasks", href: "/dashboard/tasks", icon: Calendar },
    { title: "Reports", href: "/dashboard/reports", icon: BarChart }
  ]

  if (!isMobile) {
    return null
  }

  return (
    <div className={cn(
      "fixed bottom-0 left-0 right-0 z-50 bg-background border-t",
      "safe-area-inset-bottom",
      className
    )}>
      <div className="grid grid-cols-5 h-16">
        {bottomNavItems.map((item) => {
          const isActive = pathname === item.href
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center gap-1 transition-colors",
                "hover:bg-accent/50 active:bg-accent/70",
                isActive ? "text-primary" : "text-muted-foreground"
              )}
            >
              <item.icon className="h-5 w-5" />
              <span className="text-xs font-medium">{item.title}</span>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

// Mobile Header
export function MobileHeader({
  title,
  subtitle,
  actions,
  showBack = false,
  onBack,
  className
}: {
  title: string
  subtitle?: string
  actions?: React.ReactNode
  showBack?: boolean
  onBack?: () => void
  className?: string
}) {
  const isMobile = useIsMobile()

  if (!isMobile) {
    return null
  }

  return (
    <div className={cn(
      "sticky top-0 z-40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      "border-b px-4 py-3",
      className
    )}>
      <div className="flex items-center gap-3">
        {showBack && (
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ChevronRight className="h-4 w-4 rotate-180" />
          </Button>
        )}
        
        <div className="flex-1 min-w-0">
          <h1 className="text-lg font-semibold truncate">{title}</h1>
          {subtitle && (
            <p className="text-sm text-muted-foreground truncate">{subtitle}</p>
          )}
        </div>
        
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

// Mobile Safe Area Hook
export function useSafeArea() {
  const [safeArea, setSafeArea] = React.useState({
    top: 0,
    bottom: 0,
    left: 0,
    right: 0
  })

  React.useEffect(() => {
    const updateSafeArea = () => {
      const style = getComputedStyle(document.documentElement)
      setSafeArea({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0')
      })
    }

    updateSafeArea()
    window.addEventListener('resize', updateSafeArea)
    return () => window.removeEventListener('resize', updateSafeArea)
  }, [])

  return safeArea
}

// Mobile Layout Wrapper
export function MobileLayoutWrapper({
  children,
  hasBottomNav = false,
  className
}: {
  children: React.ReactNode
  hasBottomNav?: boolean
  className?: string
}) {
  const isMobile = useIsMobile()
  const safeArea = useSafeArea()

  if (!isMobile) {
    return <>{children}</>
  }

  return (
    <div 
      className={cn(
        "min-h-screen flex flex-col",
        hasBottomNav && "pb-16",
        className
      )}
      style={{
        paddingTop: safeArea.top,
        paddingBottom: hasBottomNav ? safeArea.bottom + 64 : safeArea.bottom,
        paddingLeft: safeArea.left,
        paddingRight: safeArea.right
      }}
    >
      {children}
    </div>
  )
}
