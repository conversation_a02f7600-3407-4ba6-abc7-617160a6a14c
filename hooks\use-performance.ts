"use client"

import { useEffect, useRef, useCallback, useMemo, useState } from "react"

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)
  const mountTime = useRef<number>(0)

  useEffect(() => {
    mountTime.current = performance.now()
    
    return () => {
      const unmountTime = performance.now()
      const totalLifetime = unmountTime - mountTime.current
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Performance] ${componentName}:`, {
          totalLifetime: `${totalLifetime.toFixed(2)}ms`,
          renderCount: renderCount.current,
          averageRenderTime: renderCount.current > 0 ? 
            `${(totalLifetime / renderCount.current).toFixed(2)}ms` : '0ms'
        })
      }
    }
  }, [componentName])

  useEffect(() => {
    renderStartTime.current = performance.now()
    renderCount.current += 1
    
    const endTime = performance.now()
    const renderTime = endTime - renderStartTime.current
    
    if (process.env.NODE_ENV === 'development' && renderTime > 16) {
      console.warn(`[Performance] Slow render in ${componentName}: ${renderTime.toFixed(2)}ms`)
    }
  })

  return {
    renderCount: renderCount.current,
    markRenderStart: () => {
      renderStartTime.current = performance.now()
    },
    markRenderEnd: () => {
      const endTime = performance.now()
      return endTime - renderStartTime.current
    }
  }
}

// Memory usage monitoring
export function useMemoryMonitor() {
  const getMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
        percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
      }
    }
    return null
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      const memory = getMemoryUsage()
      if (memory && memory.percentage > 80) {
        console.warn('[Performance] High memory usage:', memory)
      }
    }, 10000) // Check every 10 seconds

    return () => clearInterval(interval)
  }, [getMemoryUsage])

  return { getMemoryUsage }
}

// Debounced value hook for performance
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttled callback hook
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef<number>(0)
  const timeoutRef = useRef<NodeJS.Timeout>()

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now()
    
    if (now - lastRun.current >= delay) {
      callback(...args)
      lastRun.current = now
    } else {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = setTimeout(() => {
        callback(...args)
        lastRun.current = Date.now()
      }, delay - (now - lastRun.current))
    }
  }, [callback, delay]) as T
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [elementRef, options, hasIntersected])

  return { isIntersecting, hasIntersected }
}

// Virtual scrolling hook
export function useVirtualScrolling<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )
    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
      .map((item, index) => ({
        item,
        index: visibleRange.startIndex + index
      }))
  }, [items, visibleRange])

  const totalHeight = items.length * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll
  }
}

// Web Vitals monitoring
export function useWebVitals() {
  const vitals = useRef<Record<string, number>>({})

  useEffect(() => {
    // Only run in browser
    if (typeof window === 'undefined') return

    const reportVital = (metric: any) => {
      vitals.current[metric.name] = metric.value
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`[Web Vitals] ${metric.name}:`, metric.value)
      }
    }

    // Dynamically import web-vitals to avoid SSR issues
    import('web-vitals').then(({ onCLS, onINP, onFCP, onLCP, onTTFB }) => {
      onCLS(reportVital)
      onINP(reportVital)
      onFCP(reportVital)
      onLCP(reportVital)
      onTTFB(reportVital)
    }).catch(() => {
      // web-vitals not available
    })
  }, [])

  return vitals.current
}

// Bundle size monitoring
export function useBundleAnalysis() {
  const [bundleInfo, setBundleInfo] = useState<{
    chunks: string[]
    totalSize: number
    loadTime: number
  } | null>(null)

  useEffect(() => {
    if (typeof window === 'undefined') return

    const startTime = performance.now()
    
    // Monitor when all chunks are loaded
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const chunks = entries
        .filter(entry => entry.name.includes('/_next/static/chunks/'))
        .map(entry => entry.name)
      
      if (chunks.length > 0) {
        const totalSize = entries.reduce((sum, entry) => {
          return sum + ((entry as PerformanceResourceTiming).transferSize || 0)
        }, 0)
        
        setBundleInfo({
          chunks,
          totalSize,
          loadTime: performance.now() - startTime
        })
      }
    })

    observer.observe({ entryTypes: ['resource'] })

    return () => {
      observer.disconnect()
    }
  }, [])

  return bundleInfo
}

// Component lazy loading with error boundary
export function useLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const [Component, setComponent] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const loadComponent = useCallback(async () => {
    if (Component || loading) return

    setLoading(true)
    setError(null)

    try {
      const module = await importFn()
      setComponent(() => module.default)
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load component'))
    } finally {
      setLoading(false)
    }
  }, [Component, loading, importFn])

  return {
    Component,
    loading,
    error,
    loadComponent
  }
}

// Performance-optimized data fetching
export function useOptimizedFetch<T>(
  url: string,
  options: RequestInit = {},
  dependencies: any[] = []
) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const abortControllerRef = useRef<AbortController>()

  const fetchData = useCallback(async () => {
    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, {
        ...options,
        signal: abortControllerRef.current.signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      setData(result)
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err)
      }
    } finally {
      setLoading(false)
    }
  }, [url, ...dependencies])

  useEffect(() => {
    fetchData()

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchData])

  return { data, loading, error, refetch: fetchData }
}
