// Customer Import/Export Components
export {
  CustomerExportDialog,
  CustomerImportDialog,
  type CustomerExportOptions,
  type CustomerImportMapping,
  type ImportProgress,
  type ImportPreview
} from "./customer-import-export"

// Customer Analytics Components
export {
  CustomerAnalytics,
  calculateCustomerAnalytics,
  type CustomerAnalytics as CustomerAnalyticsType
} from "./customer-analytics"

export {
  CustomerInsights,
  generateCustomerInsights,
  type CustomerInsight,
  type InsightMetrics
} from "./customer-insights"

// Re-export existing customer components
export { CustomerForm } from "../customer-form"
// export { CustomerFormDialog } from "./customer-form-dialog"
