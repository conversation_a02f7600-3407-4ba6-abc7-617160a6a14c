# CRM Form System Documentation

## Overview

The CRM Form System provides a comprehensive, standardized approach to form creation with built-in validation, accessibility, and user experience enhancements. This system ensures consistency across all forms in the application while providing powerful features for complex form scenarios.

## Core Components

### 1. FormLayout
The main container for forms with consistent styling and behavior.

```tsx
import { FormLayout } from "@/components/forms"

<FormLayout
  title="Customer Information"
  description="Complete the form to create a customer"
  onSubmit={handleSubmit}
  onCancel={handleCancel}
  submitLabel="Save Customer"
  isSubmitting={isLoading}
  showProgress={true}
  progress={75}
  variant="modal"
  size="lg"
>
  {/* Form content */}
</FormLayout>
```

**Props:**
- `title`: Form title
- `description`: Optional description
- `onSubmit`: Submit handler
- `onCancel`: Cancel handler
- `submitLabel`: Submit button text
- `isSubmitting`: Loading state
- `showProgress`: Show progress bar
- `progress`: Progress percentage (0-100)
- `variant`: "default" | "modal" | "page" | "inline"
- `size`: "sm" | "md" | "lg" | "xl"

### 2. FormField
Standardized form field with validation and accessibility.

```tsx
import { FormField } from "@/components/forms"

<FormField
  id="email"
  label="Email Address"
  type="email"
  icon={Mail}
  placeholder="<EMAIL>"
  required={true}
  validation={{
    required: true,
    email: true
  }}
  value={formData.email}
  onChange={(value) => setFormData({...formData, email: value})}
  error={errors.email}
/>
```

**Field Types:**
- `text`, `email`, `password`, `number`, `tel`, `url`
- `textarea`, `select`, `checkbox`, `radio`, `switch`
- `date`, `datetime-local`, `time`
- `currency`, `percentage`

### 3. FormSection
Collapsible sections for organizing form content.

```tsx
import { FormSection } from "@/components/forms"

<FormSection
  section={{
    id: "contact",
    title: "Contact Information",
    description: "Primary contact details",
    icon: User,
    required: true,
    fields: ["name", "email", "phone"]
  }}
  isExpanded={true}
  isCompleted={isValid}
  hasErrors={hasErrors}
  onToggle={() => toggleSection("contact")}
>
  {/* Section content */}
</FormSection>
```

### 4. FormFieldGroup
Groups related fields with consistent spacing.

```tsx
import { FormFieldGroup } from "@/components/forms"

<FormFieldGroup
  title="Address Information"
  description="Billing and shipping addresses"
  columns={2}
>
  <FormField id="street" label="Street Address" />
  <FormField id="city" label="City" />
  <FormField id="state" label="State" />
  <FormField id="zip" label="ZIP Code" />
</FormFieldGroup>
```

## Advanced Components

### 1. MultiStepForm
Multi-step form with progress tracking and navigation.

```tsx
import { MultiStepForm } from "@/components/forms"

const steps = [
  {
    id: "basic",
    title: "Basic Information",
    description: "Essential details",
    icon: User,
    required: true,
    validation: (data) => !!(data.name && data.email)
  },
  // ... more steps
]

<MultiStepForm
  steps={steps}
  currentStep={currentStep}
  onStepChange={setCurrentStep}
  onNext={handleNext}
  onPrevious={handlePrevious}
  onSubmit={handleSubmit}
  showProgress={true}
  allowSkip={false}
>
  {renderStepContent()}
</MultiStepForm>
```

### 2. ConditionalField
Fields that show/hide based on conditions.

```tsx
import { ConditionalField } from "@/components/forms"

<ConditionalField
  id="company_size"
  label="Company Size"
  type="select"
  condition={(data) => data.business_type === "Company"}
  formData={formData}
  options={companySizeOptions}
  {...getFieldProps("company_size")}
/>
```

### 3. FileUpload
File upload with drag-and-drop and preview.

```tsx
import { FileUpload } from "@/components/forms"

<FileUpload
  id="documents"
  label="Supporting Documents"
  description="Upload relevant files"
  accept=".pdf,.doc,.docx"
  multiple={true}
  maxSize={10} // MB
  maxFiles={5}
  value={files}
  onChange={setFiles}
  variant="dropzone"
  showPreview={true}
/>
```

### 4. FieldArray
Dynamic field arrays for repeating data.

```tsx
import { FieldArray } from "@/components/forms"

<FieldArray
  label="Contact Addresses"
  value={addresses}
  onChange={setAddresses}
  addLabel="Add Address"
  maxItems={5}
>
  {(index, remove) => (
    <div className="space-y-4">
      <FormField
        id={`address-${index}-street`}
        label="Street"
        value={addresses[index]?.street}
        onChange={(value) => updateAddress(index, "street", value)}
      />
      <Button onClick={remove}>Remove</Button>
    </div>
  )}
</FieldArray>
```

## Form Hook (useForm)

The `useForm` hook provides comprehensive form state management.

```tsx
import { useForm, COMMON_VALIDATIONS } from "@/components/forms"

const form = useForm<CustomerFormData>({
  initialData: {
    name: "",
    email: "",
    company: ""
  },
  validation: COMMON_VALIDATIONS.customer,
  onSubmit: async (data) => {
    await saveCustomer(data)
  },
  onCancel: () => {
    router.back()
  },
  autoSave: true,
  persistKey: "customer-form"
})

// Usage
<FormField
  id="name"
  label="Name"
  {...form.getFieldProps("name")}
/>
```

**Hook Features:**
- Automatic validation
- Auto-save functionality
- Form persistence
- Dirty state tracking
- Error handling
- Field-level validation

## Validation System

### Built-in Validations

```tsx
import { VALIDATION_PATTERNS, COMMON_VALIDATIONS } from "@/components/forms"

// Common validation rules
const validation = {
  email: {
    required: true,
    email: true,
    message: "Please enter a valid email"
  },
  phone: {
    phone: true,
    message: "Please enter a valid phone number"
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    message: "Password must contain uppercase, lowercase, and number"
  },
  custom: {
    custom: (value) => {
      if (value === "admin") {
        return "Username 'admin' is not allowed"
      }
      return undefined
    }
  }
}
```

### Validation Patterns

```tsx
// Available patterns
VALIDATION_PATTERNS.email
VALIDATION_PATTERNS.phone
VALIDATION_PATTERNS.url
VALIDATION_PATTERNS.alphanumeric
VALIDATION_PATTERNS.numeric
VALIDATION_PATTERNS.decimal
VALIDATION_PATTERNS.currency
VALIDATION_PATTERNS.percentage
```

### Common Validation Sets

```tsx
// Pre-configured validation sets
COMMON_VALIDATIONS.customer
COMMON_VALIDATIONS.deal
COMMON_VALIDATIONS.user
COMMON_VALIDATIONS.contact
```

## Accessibility Features

### Screen Reader Support

```tsx
import { 
  ScreenReaderAnnouncement,
  announceToScreenReader 
} from "@/components/forms"

// Announce form errors
announceToScreenReader("Form has validation errors", "assertive")

// Live announcements
<ScreenReaderAnnouncement 
  message="Form saved successfully" 
  priority="polite" 
/>
```

### Focus Management

```tsx
import { useFocusManagement } from "@/components/forms"

const { focusFirstError, focusElement, trapFocus } = useFocusManagement()

// Focus first error after validation
const handleSubmit = () => {
  if (!isValid) {
    focusFirstError(formRef.current)
  }
}
```

### Keyboard Navigation

```tsx
import { useKeyboardNavigation } from "@/components/forms"

const { handleArrowNavigation } = useKeyboardNavigation()

// Handle arrow key navigation in lists
<div onKeyDown={(e) => 
  handleArrowNavigation(e, items, currentIndex, setCurrentIndex)
}>
  {/* Navigable items */}
</div>
```

### Accessibility Provider

```tsx
import { FormAccessibilityProvider } from "@/components/forms"

<FormAccessibilityProvider
  config={{
    announceErrors: true,
    announceSuccess: true,
    focusFirstError: true,
    skipLinks: true
  }}
>
  <App />
</FormAccessibilityProvider>
```

## Best Practices

### 1. Form Structure
- Use `FormLayout` as the main container
- Group related fields with `FormFieldGroup`
- Use `FormSection` for complex forms
- Implement progressive disclosure for long forms

### 2. Validation
- Validate on blur for immediate feedback
- Show errors only after user interaction
- Use clear, actionable error messages
- Implement client-side and server-side validation

### 3. User Experience
- Provide clear labels and descriptions
- Use appropriate input types
- Implement auto-save for long forms
- Show progress for multi-step forms
- Provide keyboard shortcuts

### 4. Accessibility
- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers
- Maintain focus management

### 5. Performance
- Use memoization for expensive calculations
- Implement debounced validation
- Lazy load large option lists
- Optimize re-renders

## Examples

### Simple Form
```tsx
import { FormLayout, FormField, useForm } from "@/components/forms"

function ContactForm() {
  const form = useForm({
    initialData: { name: "", email: "", message: "" },
    validation: COMMON_VALIDATIONS.contact,
    onSubmit: async (data) => await submitContact(data)
  })

  return (
    <FormLayout
      title="Contact Us"
      onSubmit={form.handleSubmit}
      onCancel={form.handleCancel}
    >
      <FormField id="name" label="Name" {...form.getFieldProps("name")} />
      <FormField id="email" label="Email" {...form.getFieldProps("email")} />
      <FormField id="message" label="Message" type="textarea" {...form.getFieldProps("message")} />
    </FormLayout>
  )
}
```

### Complex Multi-Step Form
```tsx
import { 
  MultiStepForm, 
  FormFieldGroup, 
  FormField, 
  ConditionalField,
  FileUpload 
} from "@/components/forms"

function CustomerOnboardingForm() {
  const [currentStep, setCurrentStep] = useState(0)
  const form = useForm({ /* configuration */ })

  const steps = [
    { id: "basic", title: "Basic Info", /* ... */ },
    { id: "business", title: "Business Details", /* ... */ },
    { id: "documents", title: "Documentation", /* ... */ }
  ]

  return (
    <MultiStepForm
      steps={steps}
      currentStep={currentStep}
      onStepChange={setCurrentStep}
      onSubmit={form.handleSubmit}
    >
      {currentStep === 0 && (
        <FormFieldGroup columns={2}>
          <FormField id="name" label="Company Name" {...form.getFieldProps("name")} />
          <FormField id="email" label="Email" {...form.getFieldProps("email")} />
        </FormFieldGroup>
      )}
      
      {currentStep === 1 && (
        <div>
          <FormField id="industry" label="Industry" {...form.getFieldProps("industry")} />
          <ConditionalField
            id="employees"
            label="Number of Employees"
            condition={(data) => data.industry === "Technology"}
            formData={form.data}
            {...form.getFieldProps("employees")}
          />
        </div>
      )}
      
      {currentStep === 2 && (
        <FileUpload
          id="documents"
          label="Business Documents"
          multiple={true}
          value={form.data.documents}
          onChange={(files) => form.setField("documents", files)}
        />
      )}
    </MultiStepForm>
  )
}
```

## Migration Guide

### From Old Forms
1. Replace form containers with `FormLayout`
2. Replace input components with `FormField`
3. Implement `useForm` hook for state management
4. Add validation rules
5. Test accessibility features

### Validation Migration
```tsx
// Old validation
const [errors, setErrors] = useState({})
const validateEmail = (email) => {
  if (!email) return "Email is required"
  if (!email.includes("@")) return "Invalid email"
  return ""
}

// New validation
const form = useForm({
  validation: {
    email: {
      required: true,
      email: true
    }
  }
})
```

## Troubleshooting

### Common Issues
1. **Focus not working**: Ensure proper `id` attributes
2. **Validation not triggering**: Check validation rules syntax
3. **Auto-save not working**: Verify `persistKey` is unique
4. **Accessibility warnings**: Use accessibility testing tools

### Performance Issues
1. **Slow rendering**: Use `React.memo` for field components
2. **Excessive re-renders**: Optimize form state updates
3. **Large forms**: Implement virtualization for long lists

## Support

For questions or issues with the form system:
1. Check this documentation
2. Review example implementations
3. Test with accessibility tools
4. Consult the development team
