import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase'
import { createHealthCheck, with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler'

export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest) => {
  const healthCheck = createHealthCheck()
  
  try {
    // Test database connection
    const supabase = createClient()
    const { data, error } = await supabase
      .from('customers')
      .select('count')
      .limit(1)
      .single()

    if (error) {
      return NextResponse.json({
        ...healthCheck,
        status: 'unhealthy',
        database: 'disconnected',
        error: error.message
      }, { status: 503 })
    }

    return NextResponse.json({
      ...healthCheck,
      status: 'healthy',
      database: 'connected',
      services: {
        api: 'operational',
        database: 'operational',
        authentication: 'operational'
      }
    })
  } catch (error) {
    return NextResponse.json({
      ...healthCheck,
      status: 'unhealthy',
      error: 'Service check failed'
    }, { status: 503 })
  }
})
