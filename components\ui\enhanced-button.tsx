import { cn } from "@/lib/utils"
import { <PERSON>ton, ButtonProps } from "@/components/ui/button"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { forwardRef } from "react"

interface EnhancedButtonProps extends ButtonProps {
  loading?: boolean
  loadingText?: string
  icon?: React.ReactNode
  iconPosition?: "left" | "right"
  fullWidth?: boolean
  gradient?: boolean
  pulse?: boolean
}

export const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({
    children,
    loading = false,
    loadingText,
    icon,
    iconPosition = "left",
    fullWidth = false,
    gradient = false,
    pulse = false,
    disabled,
    className,
    variant = "default",
    size = "default",
    ...props
  }, ref) => {
    const isDisabled = disabled || loading

    const getGradientClasses = () => {
      if (!gradient) return ""
      
      switch (variant) {
        case "default":
          return "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
        case "destructive":
          return "bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700"
        case "secondary":
          return "bg-gradient-to-r from-secondary to-secondary/80 hover:from-secondary/90 hover:to-secondary/70"
        default:
          return ""
      }
    }

    const buttonClasses = cn(
      fullWidth && "w-full",
      gradient && getGradientClasses(),
      pulse && !loading && "animate-pulse",
      loading && "cursor-not-allowed",
      className
    )

    const renderContent = () => {
      if (loading) {
        return (
          <div className="flex items-center justify-center gap-2">
            <LoadingSpinner size="xs" color="white" />
            {loadingText || "Loading..."}
          </div>
        )
      }

      const content = (
        <>
          {icon && iconPosition === "left" && (
            <span className="mr-2">{icon}</span>
          )}
          {children}
          {icon && iconPosition === "right" && (
            <span className="ml-2">{icon}</span>
          )}
        </>
      )

      return content
    }

    return (
      <Button
        ref={ref}
        variant={variant}
        size={size}
        disabled={isDisabled}
        className={buttonClasses}
        {...props}
      >
        {renderContent()}
      </Button>
    )
  }
)

EnhancedButton.displayName = "EnhancedButton"

// Specialized button variants
export function PrimaryButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="default" />
}

export function SecondaryButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="secondary" />
}

export function DestructiveButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="destructive" />
}

export function OutlineButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="outline" />
}

export function GhostButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="ghost" />
}

export function LinkButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="link" />
}

// Action buttons with common patterns
export function SaveButton({ 
  loading, 
  ...props 
}: Omit<EnhancedButtonProps, "variant" | "loadingText">) {
  return (
    <EnhancedButton
      {...props}
      variant="default"
      loading={loading}
      loadingText="Saving..."
    />
  )
}

export function DeleteButton({ 
  loading, 
  ...props 
}: Omit<EnhancedButtonProps, "variant" | "loadingText">) {
  return (
    <EnhancedButton
      {...props}
      variant="destructive"
      loading={loading}
      loadingText="Deleting..."
    />
  )
}

export function SubmitButton({ 
  loading, 
  ...props 
}: Omit<EnhancedButtonProps, "variant" | "loadingText" | "type">) {
  return (
    <EnhancedButton
      {...props}
      type="submit"
      variant="default"
      loading={loading}
      loadingText="Submitting..."
    />
  )
}

export function CancelButton(props: Omit<EnhancedButtonProps, "variant">) {
  return <EnhancedButton {...props} variant="outline" />
}

// Button groups
export function ButtonGroup({ 
  children, 
  className,
  orientation = "horizontal"
}: {
  children: React.ReactNode
  className?: string
  orientation?: "horizontal" | "vertical"
}) {
  return (
    <div
      className={cn(
        "flex",
        orientation === "horizontal" ? "flex-row space-x-2" : "flex-col space-y-2",
        className
      )}
    >
      {children}
    </div>
  )
}

// Floating Action Button
export function FloatingActionButton({
  children,
  position = "bottom-right",
  className,
  ...props
}: EnhancedButtonProps & {
  position?: "bottom-right" | "bottom-left" | "top-right" | "top-left"
}) {
  const getPositionClasses = () => {
    switch (position) {
      case "bottom-left":
        return "bottom-6 left-6"
      case "top-right":
        return "top-6 right-6"
      case "top-left":
        return "top-6 left-6"
      case "bottom-right":
      default:
        return "bottom-6 right-6"
    }
  }

  return (
    <EnhancedButton
      {...props}
      className={cn(
        "fixed z-50 h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow",
        getPositionClasses(),
        className
      )}
      size="lg"
    >
      {children}
    </EnhancedButton>
  )
}
