"use client"

import { useState, useEffect, useCallback } from "react"
import { useSession } from "next-auth/react"

interface SidebarStats {
  customers_count: number
  leads_count: number
  opportunities_count: number
}

interface UseSidebarStatsReturn {
  data: SidebarStats[]
  loading: boolean
  error: string | null
  refetch: () => void
}

export function useSidebarStats(): UseSidebarStatsReturn {
  const [data, setData] = useState<SidebarStats[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { data: session } = useSession()
  const user = session?.user

  const fetchStats = useCallback(async () => {
    if (!user) {
      setData([])
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      
      console.log('🔄 [SIDEBAR-STATS] Fetching sidebar stats via API...')
      
      const response = await fetch('/api/dashboard/sidebar-stats', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const stats = await response.json()
      console.log('✅ [SIDEBAR-STATS] Stats fetched successfully:', stats)
      
      setData(stats)
    } catch (error: any) {
      console.error('❌ [SIDEBAR-STATS] Error fetching stats:', error)
      setError(error.message)
      // Set default stats on error to prevent UI breaking
      setData([{
        customers_count: 0,
        leads_count: 0,
        opportunities_count: 0
      }])
    } finally {
      setLoading(false)
    }
  }, [user])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  const refetch = useCallback(() => {
    fetchStats()
  }, [fetchStats])

  return {
    data,
    loading,
    error,
    refetch
  }
}
