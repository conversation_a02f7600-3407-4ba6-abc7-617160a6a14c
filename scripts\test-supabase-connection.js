#!/usr/bin/env node

/**
 * Test Supabase Connection
 * 
 * This script tests the connection to Supabase and helps diagnose
 * authentication timeout issues.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Read .env.local manually
const envPath = path.join(__dirname, '..', '.env.local')
const envContent = fs.readFileSync(envPath, 'utf8')
const envLines = envContent.split('\n')

envLines.forEach(line => {
  if (line.trim() && !line.startsWith('#')) {
    const [key, ...valueParts] = line.split('=')
    if (key && valueParts.length > 0) {
      process.env[key.trim()] = valueParts.join('=').trim()
    }
  }
})

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables')
  console.error('Please check your .env.local file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: false,
    autoRefreshToken: false,
    detectSessionInUrl: false,
  }
})

async function testConnection() {
  console.log('🔍 Testing Supabase connection...')
  console.log(`📍 URL: ${supabaseUrl}`)
  
  try {
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing basic connection...')
    const start = Date.now()
    
    const { data, error } = await Promise.race([
      supabase.from('users').select('count').limit(1),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Connection timeout')), 10000)
      )
    ])
    
    const duration = Date.now() - start
    
    if (error && error.code !== 'PGRST116') {
      console.log(`❌ Connection failed: ${error.message}`)
      return false
    }
    
    console.log(`✅ Connection successful (${duration}ms)`)
    
    // Test 2: Authentication endpoint
    console.log('\n2️⃣ Testing authentication endpoint...')
    const authStart = Date.now()
    
    try {
      const { data: { session }, error: authError } = await Promise.race([
        supabase.auth.getSession(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Auth timeout')), 15000)
        )
      ])
      
      const authDuration = Date.now() - authStart
      
      if (authError) {
        console.log(`⚠️ Auth endpoint error: ${authError.message}`)
      } else {
        console.log(`✅ Auth endpoint accessible (${authDuration}ms)`)
      }
    } catch (authTimeoutError) {
      console.log(`❌ Auth endpoint timeout: ${authTimeoutError.message}`)
    }
    
    // Test 3: Network latency
    console.log('\n3️⃣ Testing network latency...')
    const latencyTests = []
    
    for (let i = 0; i < 3; i++) {
      const pingStart = Date.now()
      try {
        await supabase.from('users').select('count').limit(1)
        latencyTests.push(Date.now() - pingStart)
      } catch (e) {
        latencyTests.push(-1)
      }
    }
    
    const avgLatency = latencyTests.filter(t => t > 0).reduce((a, b) => a + b, 0) / latencyTests.filter(t => t > 0).length
    console.log(`📊 Average latency: ${avgLatency.toFixed(0)}ms`)
    
    if (avgLatency > 5000) {
      console.log('⚠️ High latency detected - this may cause timeout issues')
    }
    
    return true
    
  } catch (error) {
    console.log(`❌ Connection test failed: ${error.message}`)
    return false
  }
}

async function main() {
  console.log('🚀 Supabase Connection Test')
  console.log('=' .repeat(50))
  
  const isConnected = await testConnection()
  
  console.log('\n' + '='.repeat(50))
  if (isConnected) {
    console.log('✅ Overall: Connection is working')
    console.log('💡 If you still see timeout errors, try:')
    console.log('   - Increasing timeout values in auth-provider.tsx')
    console.log('   - Checking for network/firewall issues')
    console.log('   - Verifying Supabase project status')
  } else {
    console.log('❌ Overall: Connection issues detected')
    console.log('💡 Troubleshooting steps:')
    console.log('   - Check your internet connection')
    console.log('   - Verify Supabase project is active')
    console.log('   - Check environment variables')
    console.log('   - Try accessing Supabase dashboard directly')
  }
}

main().catch(console.error)
