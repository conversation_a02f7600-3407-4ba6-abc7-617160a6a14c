"use client"

import type * as React from "react"
// Force cache invalidation - Build timestamp: ${Date.now()}
import { usePathname } from "next/navigation"
import Link from "next/link"
import { useSession, signOut } from "next-auth/react"
import { useLanguage } from "@/components/language-provider"
import { useRole } from "@/hooks/use-role"
import { useOptimizedData } from "@/hooks/use-optimized-data"
import { useSidebarStats } from "@/hooks/use-sidebar-stats"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Separator } from "@/components/ui/separator"
import {
  Bar<PERSON>hart,
  Building2,
  Calendar,
  ChevronUp,
  CreditCard,
  DollarSign,
  FileText,
  HelpCircle,
  Home,
  LogOut,
  Settings,
  Shield,
  TrendingUp,
  Users,
  User,
  Briefcase,
  Target,
  Receipt,
  MessageSquare,
  Globe,
  Sparkles,
  Star,
  Eye,
  Truck,
  Search,
} from "lucide-react"

// CRM entity interfaces for Quick Stats
interface Customer {
  id: string
  status: string
}

interface Lead {
  id: string
  status: string
}

interface Opportunity {
  id: string
  stage: string
}

// Navigation items organized by groups
const navigationGroups = [
  {
    title: "Overview",
    items: [
      {
        title: "Dashboard",
        titleKey: "nav.dashboard",
        url: "/dashboard",
        icon: Home,
        description: "Overview and analytics"
      }
    ]
  },
  {
    title: "Sales & CRM",
    items: [
      {
        title: "Customers",
        titleKey: "nav.customers",
        url: "/dashboard/customers",
        icon: Users,
        description: "Manage customer relationships"
      },
      {
        title: "Companies",
        titleKey: "nav.companies",
        url: "/dashboard/companies",
        icon: Building2,
        description: "Company management"
      },
      {
        title: "Leads",
        titleKey: "nav.leads",
        url: "/dashboard/leads",
        icon: Target,
        description: "Track potential customers"
      },
      {
        title: "Opportunities",
        titleKey: "nav.opportunities",
        url: "/dashboard/opportunities",
        icon: TrendingUp,
        description: "Sales opportunities"
      },

      {
        title: "Proposals",
        titleKey: "nav.proposals",
        url: "/dashboard/proposals",
        icon: MessageSquare,
        description: "Client proposals and quotes"
      },
      {
        title: "Vista Status",
        titleKey: "nav.vistaStatus",
        url: "/dashboard/vista-status",
        icon: Eye,
        description: "Monitor vista processing status",
        comingSoon: true,
        adminOnly: false
      },
      {
        title: "Shipping Status",
        titleKey: "nav.shippingStatus",
        url: "/dashboard/shipping-status",
        icon: Truck,
        description: "Track shipments and delivery",
        comingSoon: true,
        adminOnly: false
      },
      {
        title: "Inspection",
        titleKey: "nav.inspection",
        url: "/dashboard/inspection",
        icon: Search,
        description: "Quality inspections and compliance"
      }
    ]
  },
  {
    title: "Management",
    items: [
      {
        title: "Tasks",
        titleKey: "nav.tasks",
        url: "/dashboard/tasks",
        icon: Calendar,
        description: "Task management"
      },
      {
        title: "Reports",
        titleKey: "nav.reports",
        url: "/dashboard/reports",
        icon: BarChart,
        description: "Analytics and insights"
      }
    ]
  }
]

// Admin navigation items
const adminItems = [
  {
    title: "Admin Dashboard",
    titleKey: "nav.adminDashboard",
    url: "/dashboard/admin",
    icon: Shield,
    description: "Administrative overview and system metrics"
  },
  {
    title: "User Management",
    titleKey: "nav.userManagement",
    url: "/dashboard/admin/users",
    icon: User,
    description: "Manage users, roles, and permissions"
  },
  {
    title: "System Reports",
    titleKey: "nav.systemReports",
    url: "/dashboard/admin/reports",
    icon: BarChart,
    description: "Advanced analytics and system reports"
  },
  {
    title: "Settings",
    titleKey: "nav.settings",
    url: "/dashboard/settings",
    icon: Settings,
    description: "System configuration"
  }
]

// ✅ PERFORMANCE OPTIMIZED: Quick Stats with Batch Query
function QuickStatsSection() {
  const { t } = useLanguage()
  const { canViewAllData } = useRole()

  // ✅ FIXED: Use API endpoint that bridges NextAuth with Supabase authentication
  const { data: batchData = [], loading: statsLoading } = useSidebarStats()

  // ✅ EXTRACT: Get counts from API response
  const statsData = batchData[0] || {}
  const totalCustomers = statsData?.customers_count || 0
  const totalLeads = statsData?.leads_count || 0
  const totalOpportunities = statsData?.opportunities_count || 0

  // For now, assume 70% of opportunities are active (since we don't have detailed data)
  const activeOpportunities = Math.floor(totalOpportunities * 0.7)

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="text-sidebar-foreground/60 font-semibold text-xs uppercase tracking-wider px-4 py-2">
        {t('common.quickStats')}
      </SidebarGroupLabel>
      <SidebarGroupContent>
        <div className="px-4 py-2 space-y-3">
          <div className="flex items-center justify-between text-sm">
            <span className="text-sidebar-foreground/80">{t('nav.customers')}</span>
            <Badge variant="info" className="text-xs">
              {statsLoading ? "..." : totalCustomers}
            </Badge>
          </div>

          <div className="flex items-center justify-between text-sm">
            <span className="text-sidebar-foreground/80">{t('nav.leads')}</span>
            <Badge variant="warning" className="text-xs">
              {statsLoading ? "..." : totalLeads}
            </Badge>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-sidebar-foreground/80">{t('nav.opportunities')}</span>
            <Badge variant="default" className="text-xs">
              {statsLoading ? "..." : activeOpportunities}
            </Badge>
          </div>
        </div>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const { data: session } = useSession()
  const user = session?.user
  const { t, language, toggleLanguage } = useLanguage()
  const { isAdmin, canViewAllData } = useRole()

  const handleSignOut = async () => {
    try {
      await signOut({ callbackUrl: '/login' })
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }

  const handleLanguageToggle = () => {
    toggleLanguage()
  }

  return (
    <Sidebar
      {...props}
      className="border-r border-sidebar-border bg-sidebar shadow-lg"
    >
      <SidebarRail />

      <SidebarHeader className="border-b border-sidebar-border bg-sidebar-accent/30">
        <div className="flex items-center gap-3 px-4 py-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-sidebar-primary shadow-lg">
            <Building2 className="h-6 w-6 text-sidebar-primary-foreground" />
          </div>
          <div className="flex flex-col">
            <h1 className="text-lg font-bold text-sidebar-foreground">Nawras CRM</h1>
            <p className="text-xs text-sidebar-foreground/70">{t('common.customerManagement')}</p>
          </div>
        </div>

        {/* Language Toggle */}
        <div className="px-4 pb-3">
          <button
            onClick={handleLanguageToggle}
            className="flex items-center gap-2 text-xs text-sidebar-foreground/70 hover:text-sidebar-foreground transition-colors"
          >
            <Globe className="h-3 w-3" />
            {language === 'en' ? 'العربية' : 'English'}
          </button>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        {navigationGroups.map((group) => (
          <SidebarGroup key={group.title}>
            <SidebarGroupLabel className="text-sidebar-foreground/60 font-semibold text-xs uppercase tracking-wider px-4 py-2">
              {group.title}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items
                  .filter((item) => {
                    // Hide coming soon routes for non-admin users
                    if ((item as any).comingSoon && !isAdmin()) {
                      return false
                    }
                    return true
                  })
                  .map((item) => {
                    const isActive = pathname === item.url
                    return (
                      <SidebarMenuItem key={item.title}>
                        <SidebarMenuButton
                          asChild
                          isActive={isActive}
                          className={`text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent transition-all duration-normal rounded-lg mx-2 my-1 ${
                            isActive ? 'bg-sidebar-primary text-sidebar-primary-foreground shadow-sm' : ''
                          }`}
                        >
                          <Link href={item.url} className="flex items-center gap-3 px-3 py-2">
                            <item.icon className="h-5 w-5" />
                            <span className="font-medium">{item.titleKey ? t(item.titleKey) : item.title}</span>
                            {(item as any).comingSoon && (
                              <Badge variant="secondary" className="ml-auto text-xs">
                                Soon
                              </Badge>
                            )}
                            {isActive && !((item as any).comingSoon) && (
                              <div className="ml-auto h-2 w-2 rounded-full bg-sidebar-primary-foreground" />
                            )}
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    )
                  })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        {/* Admin Section */}
        {isAdmin() && (
          <SidebarGroup>
            <SidebarGroupLabel className="text-sidebar-foreground/60 font-semibold text-xs uppercase tracking-wider px-4 py-2">
              {t("nav.administration")}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {adminItems.map((item) => {
                  const isActive = pathname === item.url
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        className={`text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent transition-all duration-normal rounded-lg mx-2 my-1 ${
                          isActive ? 'bg-sidebar-primary text-sidebar-primary-foreground shadow-sm' : ''
                        }`}
                      >
                        <Link href={item.url} className="flex items-center gap-3 px-3 py-2">
                          <item.icon className="h-5 w-5" />
                          <span className="font-medium">{item.titleKey ? t(item.titleKey) : item.title}</span>
                          {isActive && (
                            <div className="ml-auto h-2 w-2 rounded-full bg-sidebar-primary-foreground" />
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  )
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Quick Stats with Real Data */}
        <QuickStatsSection />
      </SidebarContent>
      
      <SidebarFooter className="border-t border-sidebar-border bg-sidebar-accent/30">
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent text-sidebar-foreground/90 hover:text-sidebar-foreground hover:bg-sidebar-accent transition-all duration-normal"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src="" alt={user?.email || 'User'} />
                    <AvatarFallback className="rounded-lg bg-sidebar-primary text-sidebar-primary-foreground text-sm font-semibold">
                      {user?.email?.charAt(0).toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold text-sidebar-foreground">
                      {user?.email || 'User'}
                    </span>
                    <span className="truncate text-xs text-sidebar-foreground/70">
                      {isAdmin() ? t('roles.administrator') : t('roles.salesPersonnel')}
                    </span>
                  </div>
                  <ChevronUp className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src="" alt={user?.email || 'User'} />
                      <AvatarFallback className="rounded-lg bg-primary text-primary-foreground">
                        {user?.email?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">
                        {user?.email || 'User'}
                      </span>
                      <span className="truncate text-xs">
                        {isAdmin() ? 'Administrator' : 'Sales Personnel'}
                      </span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="gap-2">
                  <User className="h-4 w-4" />
                  {t('nav.profile')}
                </DropdownMenuItem>
                <DropdownMenuItem className="gap-2">
                  <Settings className="h-4 w-4" />
                  {t('nav.settings')}
                </DropdownMenuItem>
                <DropdownMenuItem className="gap-2">
                  <HelpCircle className="h-4 w-4" />
                  {t('nav.help')}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="gap-2 text-destructive focus:text-destructive"
                  onClick={handleSignOut}
                >
                  <LogOut className="h-4 w-4" />
                  {t('auth.signOut')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}