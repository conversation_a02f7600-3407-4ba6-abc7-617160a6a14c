"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import { useSession, signIn, signOut, SessionProvider } from "next-auth/react"
import { supabase } from "@/lib/supabase"

// Types
interface UserWithRole {
  id: string
  email: string
  name?: string
  role?: string
  department?: string
  full_name?: string
}

interface AuthContextType {
  user: UserWithRole | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<boolean>
  signOut: () => Promise<void>
  retryConnection: () => void
  resetAuthentication: () => Promise<void>
  activateEmergencyBypass: () => void
  bypassActive: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  error: null,
  signIn: async () => false,
  signOut: async () => {},
  retryConnection: () => {},
  resetAuthentication: async () => {},
  activateEmergencyBypass: () => {},
  bypassActive: false,
})

export const useAuth = () => useContext(AuthContext)

// NextAuth Session Provider Wrapper
export function NextAuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <AuthProviderInner>{children}</AuthProviderInner>
    </SessionProvider>
  )
}

// Inner Auth Provider that uses NextAuth session
function AuthProviderInner({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [error, setError] = useState<string | null>(null)
  const [bypassActive, setBypassActive] = useState(false)

  const loading = status === "loading"
  const user: UserWithRole | null = session?.user ? {
    id: (session.user as any).id || '',
    email: session.user.email || '',
    name: session.user.name || '',
    role: (session.user as any).role || 'user',
    department: (session.user as any).department,
    full_name: session.user.name || '',
  } : null

  useEffect(() => {
    if (status === "authenticated" && session?.user) {
      console.log("✅ NextAuth: User authenticated successfully")
      console.log("👤 User:", {
        id: (session.user as any).id,
        email: session.user.email,
        role: (session.user as any).role
      })
      setError(null)
    } else if (status === "unauthenticated") {
      console.log("🚪 NextAuth: User not authenticated")
      setError(null)
    }
  }, [session, status])

  const handleSignIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setError(null)
      console.log("🔐 NextAuth: Attempting sign in with credentials")
      
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        console.error("❌ NextAuth: Sign in failed:", result.error)
        setError("Invalid email or password")
        return false
      }

      console.log("✅ NextAuth: Sign in successful")
      return true
    } catch (error) {
      console.error("❌ NextAuth: Sign in error:", error)
      setError("Authentication failed. Please try again.")
      return false
    }
  }

  const handleSignOut = async (): Promise<void> => {
    try {
      console.log("🚪 NextAuth: Signing out")
      await signOut({ redirect: false })
      setError(null)
    } catch (error) {
      console.error("❌ NextAuth: Sign out error:", error)
    }
  }

  const retryConnection = () => {
    console.log("🔄 NextAuth: Retrying connection")
    setError(null)
    window.location.reload()
  }

  const resetAuthentication = async () => {
    console.log("🔄 NextAuth: Resetting authentication")
    await handleSignOut()
    setError(null)
    setBypassActive(false)
  }

  const activateEmergencyBypass = () => {
    console.log("🚧 NextAuth: Emergency bypass activated")
    setBypassActive(true)
  }

  const contextValue: AuthContextType = {
    user,
    loading,
    error,
    signIn: handleSignIn,
    signOut: handleSignOut,
    retryConnection,
    resetAuthentication,
    activateEmergencyBypass,
    bypassActive,
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Export the context for direct use if needed
export { AuthContext }
