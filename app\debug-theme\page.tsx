"use client"

import { useState } from "react"

export default function DebugThemePage() {
  const [theme, setTheme] = useState("light")

  return (
    <div>
      <h1>Debug Theme Page - Client Component</h1>
      <p>Testing client-side rendering with <PERSON>act Compiler disabled</p>
      <button onClick={() => setTheme(theme === "light" ? "dark" : "light")}>
        Current theme: {theme}
      </button>
      <div>
        <p>This content has no CSS classes</p>
        <p>Theme state: {theme}</p>
      </div>
    </div>
  )
}
