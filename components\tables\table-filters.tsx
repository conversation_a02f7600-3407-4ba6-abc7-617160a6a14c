"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { 
  Filter, X, Calendar as CalendarIcon, Search, Plus, 
  ChevronDown, RotateCcw, Check, SlidersHorizontal
} from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

// Filter Types
export interface FilterOption {
  label: string
  value: any
  count?: number
}

export interface FilterConfig {
  id: string
  label: string
  type: "text" | "select" | "multiselect" | "date" | "daterange" | "number" | "boolean"
  options?: FilterOption[]
  placeholder?: string
  min?: number
  max?: number
  step?: number
}

export interface ActiveFilter {
  id: string
  label: string
  value: any
  operator?: "equals" | "contains" | "startsWith" | "endsWith" | "gt" | "lt" | "gte" | "lte" | "between"
  displayValue?: string
}

export interface TableFiltersProps {
  filters: FilterConfig[]
  activeFilters: ActiveFilter[]
  onFiltersChange: (filters: ActiveFilter[]) => void
  onClearAll: () => void
  className?: string
  variant?: "inline" | "sidebar" | "popover"
}

// Individual Filter Components
function TextFilter({ 
  config, 
  value, 
  onChange 
}: { 
  config: FilterConfig
  value: string
  onChange: (value: string, operator?: string) => void 
}) {
  const [operator, setOperator] = React.useState("contains")
  const [inputValue, setInputValue] = React.useState(value || "")

  const operators = [
    { value: "contains", label: "Contains" },
    { value: "equals", label: "Equals" },
    { value: "startsWith", label: "Starts with" },
    { value: "endsWith", label: "Ends with" }
  ]

  const handleSubmit = () => {
    onChange(inputValue, operator)
  }

  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <div className="space-y-2">
        <Select value={operator} onValueChange={setOperator}>
          <SelectTrigger className="w-full">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {operators.map((op) => (
              <SelectItem key={op.value} value={op.value}>
                {op.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div className="flex gap-2">
          <Input
            placeholder={config.placeholder}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSubmit()}
          />
          <Button size="sm" onClick={handleSubmit}>
            <Check className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

function SelectFilter({ 
  config, 
  value, 
  onChange 
}: { 
  config: FilterConfig
  value: any
  onChange: (value: any) => void 
}) {
  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <Select value={value || ""} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder={config.placeholder || "Select option"} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">All</SelectItem>
          {config.options?.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center justify-between w-full">
                <span>{option.label}</span>
                {option.count !== undefined && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {option.count}
                  </Badge>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

function MultiSelectFilter({ 
  config, 
  value = [], 
  onChange 
}: { 
  config: FilterConfig
  value: any[]
  onChange: (value: any[]) => void 
}) {
  const handleToggle = (optionValue: any) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue]
    onChange(newValue)
  }

  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <div className="space-y-2 max-h-48 overflow-y-auto">
        {config.options?.map((option) => (
          <div key={option.value} className="flex items-center space-x-2">
            <Checkbox
              id={`${config.id}-${option.value}`}
              checked={value.includes(option.value)}
              onCheckedChange={() => handleToggle(option.value)}
            />
            <Label 
              htmlFor={`${config.id}-${option.value}`}
              className="text-sm font-normal flex-1 cursor-pointer"
            >
              <div className="flex items-center justify-between">
                <span>{option.label}</span>
                {option.count !== undefined && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {option.count}
                  </Badge>
                )}
              </div>
            </Label>
          </div>
        ))}
      </div>
    </div>
  )
}

function DateFilter({ 
  config, 
  value, 
  onChange 
}: { 
  config: FilterConfig
  value: Date | undefined
  onChange: (value: Date | undefined) => void 
}) {
  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !value && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {value ? format(value, "PPP") : "Pick a date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={value}
            onSelect={onChange}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  )
}

function DateRangeFilter({ 
  config, 
  value = {}, 
  onChange 
}: { 
  config: FilterConfig
  value: { from?: Date; to?: Date }
  onChange: (value: { from?: Date; to?: Date }) => void 
}) {
  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <div className="grid grid-cols-2 gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !value.from && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {value.from ? format(value.from, "MMM dd") : "From"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={value.from}
              onSelect={(date) => onChange({ ...value, from: date })}
              initialFocus
            />
          </PopoverContent>
        </Popover>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "justify-start text-left font-normal",
                !value.to && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {value.to ? format(value.to, "MMM dd") : "To"}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={value.to}
              onSelect={(date) => onChange({ ...value, to: date })}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  )
}

function NumberFilter({ 
  config, 
  value = {}, 
  onChange 
}: { 
  config: FilterConfig
  value: { min?: number; max?: number; operator?: string }
  onChange: (value: { min?: number; max?: number; operator?: string }) => void 
}) {
  const [operator, setOperator] = React.useState(value.operator || "between")

  const operators = [
    { value: "equals", label: "Equals" },
    { value: "gt", label: "Greater than" },
    { value: "gte", label: "Greater than or equal" },
    { value: "lt", label: "Less than" },
    { value: "lte", label: "Less than or equal" },
    { value: "between", label: "Between" }
  ]

  const handleOperatorChange = (newOperator: string) => {
    setOperator(newOperator)
    onChange({ ...value, operator: newOperator })
  }

  return (
    <div className="space-y-2">
      <Label>{config.label}</Label>
      <div className="space-y-2">
        <Select value={operator} onValueChange={handleOperatorChange}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {operators.map((op) => (
              <SelectItem key={op.value} value={op.value}>
                {op.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        {operator === "between" ? (
          <div className="grid grid-cols-2 gap-2">
            <Input
              type="number"
              placeholder="Min"
              value={value.min || ""}
              onChange={(e) => onChange({ 
                ...value, 
                min: e.target.value ? Number(e.target.value) : undefined 
              })}
              min={config.min}
              max={config.max}
              step={config.step}
            />
            <Input
              type="number"
              placeholder="Max"
              value={value.max || ""}
              onChange={(e) => onChange({ 
                ...value, 
                max: e.target.value ? Number(e.target.value) : undefined 
              })}
              min={config.min}
              max={config.max}
              step={config.step}
            />
          </div>
        ) : (
          <Input
            type="number"
            placeholder="Value"
            value={value.min || ""}
            onChange={(e) => onChange({ 
              ...value, 
              min: e.target.value ? Number(e.target.value) : undefined 
            })}
            min={config.min}
            max={config.max}
            step={config.step}
          />
        )}
      </div>
    </div>
  )
}

// Main Filter Component
export function TableFilters({
  filters,
  activeFilters,
  onFiltersChange,
  onClearAll,
  className,
  variant = "inline"
}: TableFiltersProps) {
  const [filterValues, setFilterValues] = React.useState<Record<string, any>>({})
  const [isOpen, setIsOpen] = React.useState(false)

  // Initialize filter values from active filters
  React.useEffect(() => {
    const values: Record<string, any> = {}
    activeFilters.forEach(filter => {
      values[filter.id] = filter.value
    })
    setFilterValues(values)
  }, [activeFilters])

  const handleFilterChange = (filterId: string, value: any, operator?: string) => {
    const newFilterValues = { ...filterValues, [filterId]: value }
    setFilterValues(newFilterValues)

    // Update active filters
    const filterConfig = filters.find(f => f.id === filterId)
    if (!filterConfig) return

    const newActiveFilters = activeFilters.filter(f => f.id !== filterId)
    
    if (value !== undefined && value !== null && value !== "" && 
        (Array.isArray(value) ? value.length > 0 : true)) {
      const displayValue = Array.isArray(value) 
        ? value.map(v => filterConfig.options?.find(o => o.value === v)?.label || v).join(", ")
        : filterConfig.options?.find(o => o.value === value)?.label || value

      newActiveFilters.push({
        id: filterId,
        label: filterConfig.label,
        value,
        operator: operator as "endsWith" | "startsWith" | "between" | "equals" | "contains" | "gt" | "lt" | "gte" | "lte",
        displayValue: displayValue?.toString()
      })
    }

    onFiltersChange(newActiveFilters)
  }

  const removeFilter = (filterId: string) => {
    const newActiveFilters = activeFilters.filter(f => f.id !== filterId)
    onFiltersChange(newActiveFilters)
    
    const newFilterValues = { ...filterValues }
    delete newFilterValues[filterId]
    setFilterValues(newFilterValues)
  }

  const renderFilter = (config: FilterConfig) => {
    const value = filterValues[config.id]

    switch (config.type) {
      case "text":
        return (
          <TextFilter
            key={config.id}
            config={config}
            value={value}
            onChange={(val, op) => handleFilterChange(config.id, val, op)}
          />
        )
      case "select":
        return (
          <SelectFilter
            key={config.id}
            config={config}
            value={value}
            onChange={(val) => handleFilterChange(config.id, val)}
          />
        )
      case "multiselect":
        return (
          <MultiSelectFilter
            key={config.id}
            config={config}
            value={value || []}
            onChange={(val) => handleFilterChange(config.id, val)}
          />
        )
      case "date":
        return (
          <DateFilter
            key={config.id}
            config={config}
            value={value}
            onChange={(val) => handleFilterChange(config.id, val)}
          />
        )
      case "daterange":
        return (
          <DateRangeFilter
            key={config.id}
            config={config}
            value={value || {}}
            onChange={(val) => handleFilterChange(config.id, val)}
          />
        )
      case "number":
        return (
          <NumberFilter
            key={config.id}
            config={config}
            value={value || {}}
            onChange={(val) => handleFilterChange(config.id, val)}
          />
        )
      default:
        return null
    }
  }

  if (variant === "popover") {
    return (
      <div className={className}>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="gap-2">
              <SlidersHorizontal className="h-4 w-4" />
              Filters
              {activeFilters.length > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {activeFilters.length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80 p-0" align="start">
            <Card className="border-0 shadow-none">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm">Filters</CardTitle>
                  {activeFilters.length > 0 && (
                    <Button variant="ghost" size="sm" onClick={onClearAll}>
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Clear
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {filters.map(renderFilter)}
              </CardContent>
            </Card>
          </PopoverContent>
        </Popover>

        {/* Active Filters Display */}
        {activeFilters.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap mt-2">
            {activeFilters.map((filter) => (
              <Badge key={filter.id} variant="secondary" className="gap-1">
                {filter.label}: {filter.displayValue || filter.value}
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => removeFilter(filter.id)}
                />
              </Badge>
            ))}
          </div>
        )}
      </div>
    )
  }

  if (variant === "sidebar") {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filters
            </CardTitle>
            {activeFilters.length > 0 && (
              <Button variant="ghost" size="sm" onClick={onClearAll}>
                <RotateCcw className="h-3 w-3 mr-1" />
                Clear All
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {filters.map((config, index) => (
            <div key={config.id}>
              {renderFilter(config)}
              {index < filters.length - 1 && <Separator className="mt-4" />}
            </div>
          ))}
        </CardContent>
      </Card>
    )
  }

  // Inline variant
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium">Filters</h3>
        {activeFilters.length > 0 && (
          <Button variant="ghost" size="sm" onClick={onClearAll}>
            <RotateCcw className="h-3 w-3 mr-1" />
            Clear All
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filters.map(renderFilter)}
      </div>
      
      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active:</span>
          {activeFilters.map((filter) => (
            <Badge key={filter.id} variant="secondary" className="gap-1">
              {filter.label}: {filter.displayValue || filter.value}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => removeFilter(filter.id)}
              />
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
