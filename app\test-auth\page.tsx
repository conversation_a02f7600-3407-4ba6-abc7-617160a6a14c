import { supabase } from '@/lib/supabase'
import { redirect } from 'next/navigation'

export default async function TestAuthPage() {
  // Using singleton client for consistency
  
  // Test server-side authentication
  const { data: { session }, error } = await supabase.auth.getSession()
  
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Server-Side Authentication Test</h1>
      
      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold">Session Status:</h2>
          {session ? (
            <div className="text-green-600">
              ✅ Authenticated
              <div className="mt-2">
                <p><strong>User ID:</strong> {session.user.id}</p>
                <p><strong>Email:</strong> {session.user.email}</p>
                <p><strong>Session Expires:</strong> {new Date(session.expires_at! * 1000).toLocaleString()}</p>
              </div>
            </div>
          ) : (
            <div className="text-red-600">
              ❌ Not authenticated
              {error && <p className="text-sm">Error: {error.message}</p>}
            </div>
          )}
        </div>
        
        <div>
          <h2 className="text-lg font-semibold">Supabase Connection Test:</h2>
          <div className="text-blue-600">
            ✅ Server-side Supabase client working
          </div>
        </div>
        
        <div>
          <h2 className="text-lg font-semibold">Environment Variables:</h2>
          <div className="text-sm">
            <p><strong>URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL}</p>
            <p><strong>Anon Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.substring(0, 20)}...</p>
          </div>
        </div>
        
        <div className="mt-6">
          <a 
            href="/login" 
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Go to Login Page
          </a>
        </div>
      </div>
    </div>
  )
}
