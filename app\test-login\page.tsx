import { supabase } from '@/lib/supabase'
import { redirect } from 'next/navigation'

async function testLogin(formData: FormData) {
  'use server'

  const email = formData.get('email') as string
  const password = formData.get('password') as string

  // Using singleton client for consistency
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })
  
  if (error) {
    console.error('Login error:', error)
    redirect('/test-login?error=' + encodeURIComponent(error.message))
  }
  
  if (data.user) {
    console.log('Login successful:', data.user.email)
    redirect('/test-auth?success=true')
  }
}

export default async function TestLoginPage({
  searchParams,
}: {
  searchParams: Promise<{ error?: string; success?: string }>
}) {
  const params = await searchParams
  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Server-Side Login Test</h1>
      
      {params.error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {params.error}
        </div>
      )}

      {params.success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <strong>Success:</strong> Login successful!
        </div>
      )}
      
      <form action={testLogin} className="space-y-4">
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">
            Email
          </label>
          <input
            type="email"
            id="email"
            name="email"
            defaultValue="<EMAIL>"
            required
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Password
          </label>
          <input
            type="password"
            id="password"
            name="password"
            defaultValue="111333Tt"
            required
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        
        <button
          type="submit"
          className="w-full bg-blue-500 text-white py-2 px-4 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Test Login (Server Action)
        </button>
      </form>
      
      <div className="mt-6 text-sm text-gray-600">
        <p>This form uses Next.js Server Actions to test authentication without client-side JavaScript.</p>
        <p className="mt-2">
          <a href="/test-auth" className="text-blue-500 hover:underline">
            Check Authentication Status
          </a>
        </p>
      </div>
    </div>
  )
}
