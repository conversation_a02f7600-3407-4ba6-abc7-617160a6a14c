"use client"

import * as React from "react"
import { memo, useMemo, useCallback, lazy, Suspense } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { usePerformanceMonitor, useVirtualScrolling, useIntersectionObserver } from "@/hooks/use-performance"
import { PerformanceMonitor } from "@/lib/performance-optimizations"
import { cn } from "@/lib/utils"

// Optimized List Component with Virtual Scrolling
interface OptimizedListProps<T> {
  items: T[]
  renderItem: (item: T, index: number) => React.ReactNode
  itemHeight: number
  containerHeight: number
  keyExtractor: (item: T, index: number) => string
  className?: string
  onItemClick?: (item: T, index: number) => void
}

export const OptimizedList = memo(<T,>({
  items,
  renderItem,
  itemHeight,
  containerHeight,
  keyExtractor,
  className,
  onItemClick
}: OptimizedListProps<T>) => {
  const { visibleItems, totalHeight, offsetY, handleScroll } = useVirtualScrolling({
    items,
    itemHeight,
    containerHeight
  })

  const handleItemClick = useCallback((item: T, index: number) => {
    onItemClick?.(item, index)
  }, [onItemClick])

  return (
    <div
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map(({ item, index }) => (
            <div
              key={keyExtractor(item, index)}
              style={{ height: itemHeight }}
              onClick={() => handleItemClick(item, index)}
            >
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}) as <T>(props: OptimizedListProps<T>) => JSX.Element

// OptimizedList.displayName = 'OptimizedList'

// Lazy Loading Component with Intersection Observer
interface LazyComponentProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  rootMargin?: string
  threshold?: number
  className?: string
}

export const LazyComponent = memo(({
  children,
  fallback = <Skeleton className="h-20 w-full" />,
  rootMargin = "50px",
  threshold = 0.1,
  className
}: LazyComponentProps) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const { hasIntersected } = useIntersectionObserver(ref, {
    rootMargin,
    threshold
  })

  return (
    <div ref={ref} className={className}>
      {hasIntersected ? children : fallback}
    </div>
  )
})

LazyComponent.displayName = 'LazyComponent'

// Memoized Data Table Row
interface OptimizedTableRowProps {
  data: Record<string, any>
  columns: Array<{
    key: string
    render?: (value: any, row: Record<string, any>) => React.ReactNode
  }>
  onRowClick?: (data: Record<string, any>) => void
  isSelected?: boolean
  className?: string
}

export const OptimizedTableRow = memo(({
  data,
  columns,
  onRowClick,
  isSelected,
  className
}: OptimizedTableRowProps) => {
  const handleClick = useCallback(() => {
    onRowClick?.(data)
  }, [onRowClick, data])

  const renderedCells = useMemo(() => {
    return columns.map((column) => {
      const value = data[column.key]
      const content = column.render ? column.render(value, data) : value
      
      return (
        <td key={column.key} className="px-4 py-2">
          {content}
        </td>
      )
    })
  }, [data, columns])

  return (
    <tr
      className={cn(
        "hover:bg-accent/50 cursor-pointer transition-colors",
        isSelected && "bg-accent",
        className
      )}
      onClick={handleClick}
    >
      {renderedCells}
    </tr>
  )
})

OptimizedTableRow.displayName = 'OptimizedTableRow'

// Performance-Optimized Chart Component
interface OptimizedChartProps {
  data: Array<{ label: string; value: number }>
  width?: number
  height?: number
  type?: 'bar' | 'line' | 'pie'
  className?: string
}

export const OptimizedChart = memo(({
  data,
  width = 400,
  height = 300,
  type = 'bar',
  className
}: OptimizedChartProps) => {
  const { markRenderStart, markRenderEnd } = usePerformanceMonitor('OptimizedChart')

  const chartData = useMemo(() => {
    markRenderStart()
    
    // Expensive chart calculations
    const maxValue = Math.max(...data.map(d => d.value))
    const processedData = data.map(item => ({
      ...item,
      percentage: (item.value / maxValue) * 100
    }))
    
    const renderTime = markRenderEnd()
    if (renderTime > 16) {
      console.warn(`Chart render took ${renderTime.toFixed(2)}ms`)
    }
    
    return processedData
  }, [data, markRenderStart, markRenderEnd])

  const renderChart = useCallback(() => {
    switch (type) {
      case 'bar':
        return (
          <div className="space-y-2">
            {chartData.map((item, index) => (
              <div key={item.label} className="flex items-center gap-2">
                <span className="text-sm w-20 truncate">{item.label}</span>
                <div className="flex-1 bg-muted rounded-full h-4 overflow-hidden">
                  <div
                    className="h-full bg-primary transition-all duration-300"
                    style={{ width: `${item.percentage}%` }}
                  />
                </div>
                <span className="text-sm w-12 text-right">{item.value}</span>
              </div>
            ))}
          </div>
        )
      
      case 'pie':
        // Simplified pie chart implementation
        return (
          <div className="grid grid-cols-2 gap-2">
            {chartData.map((item, index) => (
              <div key={item.label} className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: `hsl(${index * 60}, 70%, 50%)` }}
                />
                <span className="text-sm">{item.label}: {item.value}</span>
              </div>
            ))}
          </div>
        )
      
      default:
        return <div>Chart type not supported</div>
    }
  }, [chartData, type])

  return (
    <div className={cn("p-4", className)} style={{ width, height }}>
      {renderChart()}
    </div>
  )
})

OptimizedChart.displayName = 'OptimizedChart'

// Debounced Search Component
interface OptimizedSearchProps {
  onSearch: (query: string) => void
  placeholder?: string
  debounceMs?: number
  className?: string
}

export const OptimizedSearch = memo(({
  onSearch,
  placeholder = "Search...",
  debounceMs = 300,
  className
}: OptimizedSearchProps) => {
  const [query, setQuery] = React.useState("")
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  const debouncedSearch = useCallback((searchQuery: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    timeoutRef.current = setTimeout(() => {
      onSearch(searchQuery)
    }, debounceMs)
  }, [onSearch, debounceMs])

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    debouncedSearch(value)
  }, [debouncedSearch])

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return (
    <input
      type="text"
      value={query}
      onChange={handleInputChange}
      placeholder={placeholder}
      className={cn(
        "w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary",
        className
      )}
    />
  )
})

OptimizedSearch.displayName = 'OptimizedSearch'

// Lazy Loaded Modal
const LazyModal = lazy(() => import('@/components/ui/dialog').then(module => ({
  default: module.Dialog
})))

interface OptimizedModalProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
}

export const OptimizedModal = memo(({
  isOpen,
  onClose,
  children,
  title
}: OptimizedModalProps) => {
  if (!isOpen) return null

  return (
    <Suspense fallback={<div className="fixed inset-0 bg-black/50 flex items-center justify-center">
      <Skeleton className="w-96 h-64" />
    </div>}>
      <LazyModal>
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 max-w-md w-full mx-4">
            {title && (
              <h2 className="text-lg font-semibold mb-4">{title}</h2>
            )}
            {children}
            <div className="flex justify-end mt-4">
              <Button onClick={onClose} variant="outline">
                Close
              </Button>
            </div>
          </div>
        </div>
      </LazyModal>
    </Suspense>
  )
})

OptimizedModal.displayName = 'OptimizedModal'

// Performance Metrics Display
interface PerformanceMetricsProps {
  className?: string
}

export const PerformanceMetrics = memo(({ className }: PerformanceMetricsProps) => {
  const [metrics, setMetrics] = React.useState<Record<string, any>>({})

  React.useEffect(() => {
    const interval = setInterval(() => {
      setMetrics(PerformanceMonitor.getAllMetrics())
    }, 5000)

    return () => clearInterval(interval)
  }, [])

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <Card className={cn("fixed bottom-4 right-4 w-80 z-50", className)}>
      <CardHeader>
        <CardTitle className="text-sm">Performance Metrics</CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {Object.entries(metrics).map(([label, data]) => (
          <div key={label} className="text-xs">
            <div className="font-medium">{label}</div>
            {data && (
              <div className="text-muted-foreground">
                Avg: {data.avg?.toFixed(2)}ms | P95: {data.p95?.toFixed(2)}ms
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  )
})

PerformanceMetrics.displayName = 'PerformanceMetrics'

// Optimized Image Component
interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  className?: string
  priority?: boolean
}

export const OptimizedImage = memo(({
  src,
  alt,
  width,
  height,
  className,
  priority = false
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [error, setError] = React.useState(false)
  const imgRef = React.useRef<HTMLImageElement>(null)

  const { hasIntersected } = useIntersectionObserver(imgRef, {
    threshold: 0.1,
    rootMargin: '50px'
  })

  const shouldLoad = priority || hasIntersected

  const handleLoad = useCallback(() => {
    setIsLoaded(true)
  }, [])

  const handleError = useCallback(() => {
    setError(true)
  }, [])

  return (
    <div
      ref={imgRef}
      className={cn("relative overflow-hidden", className)}
      style={{ width, height }}
    >
      {!isLoaded && !error && (
        <Skeleton className="absolute inset-0" />
      )}
      
      {shouldLoad && !error && (
        <img
          src={src}
          alt={alt}
          width={width}
          height={height}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            "transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          loading={priority ? "eager" : "lazy"}
        />
      )}
      
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground text-sm">
          Failed to load image
        </div>
      )}
    </div>
  )
})

OptimizedImage.displayName = 'OptimizedImage'
