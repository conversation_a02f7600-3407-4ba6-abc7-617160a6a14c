-- ✅ PERFORMANCE OPTIMIZATION: Dashboard Stats View
-- Based on Context 7 research and Neon performance best practices
-- This view aggregates counts for sidebar stats in a single query

CREATE OR REPLACE VIEW dashboard_stats AS
SELECT
  (SELECT COUNT(*) FROM customers WHERE user_id = auth.uid()) as customers_count,
  (SELECT COUNT(*) FROM leads WHERE user_id = auth.uid()) as leads_count,
  (SELECT COUNT(*) FROM opportunities WHERE user_id = auth.uid()) as opportunities_count,
  (SELECT COUNT(*) FROM companies WHERE user_id = auth.uid()) as companies_count,
  (SELECT COUNT(*) FROM tasks WHERE user_id = auth.uid()) as tasks_count,
  -- Additional aggregated stats for comprehensive dashboard
  (SELECT COUNT(*) FROM customers WHERE user_id = auth.uid() AND status = 'Active') as active_customers_count,
  (SELECT COUNT(*) FROM leads WHERE user_id = auth.uid() AND status = 'Qualified') as qualified_leads_count,
  (SELECT COALESCE(SUM(value), 0) FROM opportunities WHERE user_id = auth.uid()) as total_opportunities_value,
  -- Performance metadata
  NOW() as generated_at,
  auth.uid() as user_id;

-- ✅ CRITICAL: Create batch stats table for even better performance
CREATE OR REPLACE VIEW dashboard_stats_batch AS
SELECT
  (SELECT COUNT(*) FROM customers WHERE user_id = auth.uid()) as customers_count,
  (SELECT COUNT(*) FROM leads WHERE user_id = auth.uid()) as leads_count,
  (SELECT COUNT(*) FROM opportunities WHERE user_id = auth.uid()) as opportunities_count;

-- Enable RLS on both views
ALTER VIEW dashboard_stats ENABLE ROW LEVEL SECURITY;
ALTER VIEW dashboard_stats_batch ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the views
CREATE POLICY "Users can view their own dashboard stats" ON dashboard_stats
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can view their own batch stats" ON dashboard_stats_batch
  FOR SELECT USING (true); -- No user_id column in batch view, relies on RLS of underlying tables

-- Grant access to authenticated users
GRANT SELECT ON dashboard_stats TO authenticated;
GRANT SELECT ON dashboard_stats_batch TO authenticated;

-- Add comments for documentation
COMMENT ON VIEW dashboard_stats IS 'Comprehensive dashboard statistics - optimized for performance based on Neon best practices';
COMMENT ON VIEW dashboard_stats_batch IS 'Lightweight batch stats for sidebar - eliminates query cascade overhead';
