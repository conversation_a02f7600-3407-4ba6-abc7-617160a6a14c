"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

// Touch Interaction Types
export interface TouchGesture {
  type: "tap" | "double-tap" | "long-press" | "swipe" | "pinch" | "pan"
  direction?: "left" | "right" | "up" | "down"
  distance?: number
  duration?: number
}

export interface TouchInteractionProps {
  onGesture?: (gesture: TouchGesture) => void
  onTap?: () => void
  onDoubleTap?: () => void
  onLongPress?: () => void
  onSwipe?: (direction: "left" | "right" | "up" | "down", distance: number) => void
  onPinch?: (scale: number) => void
  onPan?: (deltaX: number, deltaY: number) => void
  disabled?: boolean
  className?: string
  children: React.ReactNode
}

// Touch Interaction Hook
export function useTouchInteractions({
  onTap,
  onDoubleTap,
  onLongPress,
  onSwipe,
  onPinch,
  onPan,
  disabled = false
}: Omit<TouchInteractionProps, "children" | "className">) {
  const touchStartRef = React.useRef<{ x: number; y: number; time: number } | null>(null)
  const touchEndRef = React.useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimerRef = React.useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = React.useRef<number>(0)
  const pinchStartRef = React.useRef<number | null>(null)

  const handleTouchStart = React.useCallback((e: React.TouchEvent) => {
    if (disabled) return

    const touch = e.touches[0]
    touchStartRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && onPinch) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      pinchStartRef.current = distance
    }

    // Start long press timer
    if (onLongPress) {
      longPressTimerRef.current = setTimeout(() => {
        onLongPress()
      }, 500) // 500ms for long press
    }
  }, [disabled, onLongPress, onPinch])

  const handleTouchMove = React.useCallback((e: React.TouchEvent) => {
    if (disabled) return

    // Clear long press timer on move
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && onPinch && pinchStartRef.current) {
      const touch1 = e.touches[0]
      const touch2 = e.touches[1]
      const distance = Math.sqrt(
        Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
      )
      const scale = distance / pinchStartRef.current
      onPinch(scale)
    }

    // Handle pan gesture
    if (onPan && touchStartRef.current && e.touches.length === 1) {
      const touch = e.touches[0]
      const deltaX = touch.clientX - touchStartRef.current.x
      const deltaY = touch.clientY - touchStartRef.current.y
      onPan(deltaX, deltaY)
    }
  }, [disabled, onPinch, onPan])

  const handleTouchEnd = React.useCallback((e: React.TouchEvent) => {
    if (disabled) return

    // Clear long press timer
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }

    if (!touchStartRef.current) return

    const touch = e.changedTouches[0]
    touchEndRef.current = {
      x: touch.clientX,
      y: touch.clientY,
      time: Date.now()
    }

    const deltaX = touchEndRef.current.x - touchStartRef.current.x
    const deltaY = touchEndRef.current.y - touchStartRef.current.y
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
    const duration = touchEndRef.current.time - touchStartRef.current.time

    // Handle tap gestures
    if (distance < 10 && duration < 300) {
      const now = Date.now()
      const timeSinceLastTap = now - lastTapRef.current

      if (timeSinceLastTap < 300 && onDoubleTap) {
        // Double tap
        onDoubleTap()
        lastTapRef.current = 0 // Reset to prevent triple tap
      } else {
        // Single tap
        if (onTap) {
          setTimeout(() => {
            if (Date.now() - lastTapRef.current > 300) {
              onTap()
            }
          }, 300)
        }
        lastTapRef.current = now
      }
    }

    // Handle swipe gestures
    if (distance > 50 && duration < 500 && onSwipe) {
      const absX = Math.abs(deltaX)
      const absY = Math.abs(deltaY)

      if (absX > absY) {
        // Horizontal swipe
        onSwipe(deltaX > 0 ? "right" : "left", distance)
      } else {
        // Vertical swipe
        onSwipe(deltaY > 0 ? "down" : "up", distance)
      }
    }

    // Reset refs
    touchStartRef.current = null
    touchEndRef.current = null
    pinchStartRef.current = null
  }, [disabled, onTap, onDoubleTap, onSwipe])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

// Touch Interaction Component
export function TouchInteraction({
  onGesture,
  onTap,
  onDoubleTap,
  onLongPress,
  onSwipe,
  onPinch,
  onPan,
  disabled = false,
  className,
  children
}: TouchInteractionProps) {
  const touchHandlers = useTouchInteractions({
    onTap,
    onDoubleTap,
    onLongPress,
    onSwipe,
    onPinch,
    onPan,
    disabled
  })

  return (
    <div
      className={cn("touch-manipulation", className)}
      {...touchHandlers}
    >
      {children}
    </div>
  )
}

// Touch-Friendly Button
export function TouchButton({
  children,
  size = "default",
  variant = "default",
  className,
  onPress,
  onLongPress,
  disabled = false,
  hapticFeedback = true,
  ...props
}: {
  children: React.ReactNode
  size?: "sm" | "default" | "lg" | "xl"
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  className?: string
  onPress?: () => void
  onLongPress?: () => void
  disabled?: boolean
  hapticFeedback?: boolean
} & Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, "onClick" | "size">) {
  const isMobile = useIsMobile()

  const sizeClasses = {
    sm: "h-9 px-3 text-sm",
    default: "h-12 px-4 py-2", // Increased height for touch
    lg: "h-14 px-6 text-lg", // Even larger for primary actions
    xl: "h-16 px-8 text-xl" // Extra large for important actions
  }

  const handlePress = () => {
    if (hapticFeedback && "vibrate" in navigator) {
      navigator.vibrate(10) // Short haptic feedback
    }
    onPress?.()
  }

  const handleLongPress = () => {
    if (hapticFeedback && "vibrate" in navigator) {
      navigator.vibrate([10, 50, 10]) // Pattern for long press
    }
    onLongPress?.()
  }

  return (
    <TouchInteraction
      onTap={handlePress}
      onLongPress={onLongPress ? handleLongPress : undefined}
      disabled={disabled}
    >
      <Button
        variant={variant}
        disabled={disabled}
        className={cn(
          isMobile && sizeClasses[size],
          "touch-manipulation select-none",
          "active:scale-95 transition-transform duration-100",
          className
        )}
        {...props}
      >
        {children}
      </Button>
    </TouchInteraction>
  )
}

// Swipeable Card
export function SwipeableCard({
  children,
  onSwipeLeft,
  onSwipeRight,
  leftAction,
  rightAction,
  className
}: {
  children: React.ReactNode
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  leftAction?: { icon: React.ReactNode; label: string; color?: string }
  rightAction?: { icon: React.ReactNode; label: string; color?: string }
  className?: string
}) {
  const [swipeOffset, setSwipeOffset] = React.useState(0)
  const [isSwipeActive, setIsSwipeActive] = React.useState(false)
  const isMobile = useIsMobile()

  const handlePan = (deltaX: number, deltaY: number) => {
    if (!isMobile) return
    
    setIsSwipeActive(true)
    setSwipeOffset(Math.max(-100, Math.min(100, deltaX)))
  }

  const handleSwipe = (direction: "left" | "right" | "up" | "down", distance: number) => {
    if (!isMobile) return

    if (distance > 50) {
      if (direction === "left" && onSwipeLeft) {
        onSwipeLeft()
      } else if (direction === "right" && onSwipeRight) {
        onSwipeRight()
      }
    }
    
    setSwipeOffset(0)
    setIsSwipeActive(false)
  }

  const handleTouchEnd = () => {
    setSwipeOffset(0)
    setIsSwipeActive(false)
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {/* Left Action */}
      {leftAction && (
        <div
          className={cn(
            "absolute left-0 top-0 bottom-0 flex items-center justify-center",
            "bg-red-500 text-white transition-transform duration-200",
            leftAction.color && `bg-${leftAction.color}-500`
          )}
          style={{
            width: "100px",
            transform: `translateX(${Math.min(0, swipeOffset + 100)}px)`
          }}
        >
          <div className="text-center">
            {leftAction.icon}
            <p className="text-xs mt-1">{leftAction.label}</p>
          </div>
        </div>
      )}

      {/* Right Action */}
      {rightAction && (
        <div
          className={cn(
            "absolute right-0 top-0 bottom-0 flex items-center justify-center",
            "bg-green-500 text-white transition-transform duration-200",
            rightAction.color && `bg-${rightAction.color}-500`
          )}
          style={{
            width: "100px",
            transform: `translateX(${Math.max(0, swipeOffset - 100)}px)`
          }}
        >
          <div className="text-center">
            {rightAction.icon}
            <p className="text-xs mt-1">{rightAction.label}</p>
          </div>
        </div>
      )}

      {/* Main Content */}
      <TouchInteraction
        onPan={handlePan}
        onSwipe={handleSwipe}
      >
        <Card
          className={cn(
            "transition-transform duration-200",
            isSwipeActive && "shadow-lg"
          )}
          style={{
            transform: `translateX(${swipeOffset}px)`
          }}
        >
          <CardContent className="p-4">
            {children}
          </CardContent>
        </Card>
      </TouchInteraction>
    </div>
  )
}

// Pull to Refresh
export function PullToRefresh({
  children,
  onRefresh,
  refreshThreshold = 80,
  className
}: {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  refreshThreshold?: number
  className?: string
}) {
  const [pullDistance, setPullDistance] = React.useState(0)
  const [isRefreshing, setIsRefreshing] = React.useState(false)
  const [canRefresh, setCanRefresh] = React.useState(false)
  const isMobile = useIsMobile()
  const containerRef = React.useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!isMobile || isRefreshing) return
    
    const container = containerRef.current
    if (container && container.scrollTop === 0) {
      setCanRefresh(true)
    }
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isMobile || !canRefresh || isRefreshing) return

    const touch = e.touches[0]
    const startY = touch.clientY
    const currentY = touch.clientY
    const distance = Math.max(0, currentY - startY)
    
    setPullDistance(Math.min(distance, refreshThreshold * 1.5))
  }

  const handleTouchEnd = async () => {
    if (!isMobile || !canRefresh || isRefreshing) return

    if (pullDistance >= refreshThreshold) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
      }
    }
    
    setPullDistance(0)
    setCanRefresh(false)
  }

  const refreshProgress = Math.min(pullDistance / refreshThreshold, 1)

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-auto", className)}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull to Refresh Indicator */}
      {(pullDistance > 0 || isRefreshing) && (
        <div
          className="absolute top-0 left-0 right-0 flex items-center justify-center bg-primary/10 transition-all duration-200"
          style={{
            height: `${Math.max(pullDistance, isRefreshing ? 60 : 0)}px`,
            transform: `translateY(-${Math.max(pullDistance, isRefreshing ? 60 : 0)}px)`
          }}
        >
          <div className="text-center">
            <div
              className={cn(
                "w-6 h-6 border-2 border-primary rounded-full mx-auto mb-2",
                isRefreshing ? "animate-spin border-t-transparent" : ""
              )}
              style={{
                transform: `rotate(${refreshProgress * 360}deg)`
              }}
            />
            <p className="text-xs text-primary">
              {isRefreshing ? "Refreshing..." : pullDistance >= refreshThreshold ? "Release to refresh" : "Pull to refresh"}
            </p>
          </div>
        </div>
      )}

      {/* Content */}
      <div
        style={{
          transform: `translateY(${pullDistance}px)`,
          transition: pullDistance === 0 ? "transform 0.2s ease-out" : "none"
        }}
      >
        {children}
      </div>
    </div>
  )
}
