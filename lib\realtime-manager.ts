"use client"

import React from 'react'
import { supabase } from "@/lib/supabase"
import { RealtimePostgresChangesPayload, REALTIME_SUBSCRIBE_STATES } from '@supabase/supabase-js'

// Global realtime subscription manager to prevent multiple GoTrueClient instances
// This singleton ensures only one subscription per table across the entire application

interface SubscriptionInfo {
  channel: any
  callbacks: Set<(payload: RealtimePostgresChangesPayload<any>) => void>
  userFilter?: string
  isActive: boolean
}

class RealtimeManager {
  private subscriptions = new Map<string, SubscriptionInfo>()
  private static instance: RealtimeManager

  static getInstance(): RealtimeManager {
    if (!RealtimeManager.instance) {
      RealtimeManager.instance = new RealtimeManager()
    }
    return RealtimeManager.instance
  }

  private constructor() {
    console.log('🔄 [REALTIME-MANAGER] Initializing singleton realtime manager')
  }

  /**
   * Subscribe to table changes with automatic deduplication
   */
  subscribe(
    table: string,
    callback: (payload: RealtimePostgresChangesPayload<any>) => void,
    options: {
      userId?: string
      userRole?: string
      requiresAuth?: boolean
    } = {}
  ): () => void {
    const { userId, userRole, requiresAuth = true } = options
    
    // Create unique subscription key based on table and user filter
    const userFilter = requiresAuth && userRole !== 'admin' && userId ? `user_id=eq.${userId}` : undefined
    const subscriptionKey = `${table}_${userFilter || 'all'}`

    console.log(`🔄 [REALTIME-MANAGER] Subscribing to ${subscriptionKey}`)

    let subscription = this.subscriptions.get(subscriptionKey)

    if (!subscription) {
      // Create new subscription
      console.log(`🆕 [REALTIME-MANAGER] Creating new subscription for ${subscriptionKey}`)
      
      const channel = supabase
        .channel(`realtime_${subscriptionKey}_${Date.now()}`)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table,
            filter: userFilter,
          },
          (payload: RealtimePostgresChangesPayload<any>) => {
            console.log(`🔄 [REALTIME-MANAGER] Change received for ${subscriptionKey}:`, payload.eventType)
            
            // Notify all callbacks for this subscription
            const sub = this.subscriptions.get(subscriptionKey)
            if (sub) {
              sub.callbacks.forEach(cb => {
                try {
                  cb(payload)
                } catch (error) {
                  console.error(`❌ [REALTIME-MANAGER] Error in callback for ${subscriptionKey}:`, error)
                }
              })
            }
          }
        )
        .subscribe((status: `${REALTIME_SUBSCRIBE_STATES}`) => {
          console.log(`🔄 [REALTIME-MANAGER] Status for ${subscriptionKey}:`, status)
          
          const sub = this.subscriptions.get(subscriptionKey)
          if (sub) {
            sub.isActive = status === 'SUBSCRIBED'
          }
        })

      subscription = {
        channel,
        callbacks: new Set([callback]),
        userFilter,
        isActive: false
      }

      this.subscriptions.set(subscriptionKey, subscription)
    } else {
      // Add callback to existing subscription
      console.log(`➕ [REALTIME-MANAGER] Adding callback to existing subscription ${subscriptionKey}`)
      subscription.callbacks.add(callback)
    }

    // Return unsubscribe function
    return () => {
      console.log(`🗑️ [REALTIME-MANAGER] Unsubscribing callback from ${subscriptionKey}`)
      
      const sub = this.subscriptions.get(subscriptionKey)
      if (sub) {
        sub.callbacks.delete(callback)
        
        // If no more callbacks, remove the subscription
        if (sub.callbacks.size === 0) {
          console.log(`🧹 [REALTIME-MANAGER] Removing subscription ${subscriptionKey} (no more callbacks)`)
          supabase.removeChannel(sub.channel)
          this.subscriptions.delete(subscriptionKey)
        }
      }
    }
  }

  /**
   * Get subscription status for debugging
   */
  getStatus(): Record<string, { callbackCount: number; isActive: boolean; userFilter?: string }> {
    const status: Record<string, { callbackCount: number; isActive: boolean; userFilter?: string }> = {}
    
    this.subscriptions.forEach((sub, key) => {
      status[key] = {
        callbackCount: sub.callbacks.size,
        isActive: sub.isActive,
        userFilter: sub.userFilter
      }
    })
    
    return status
  }

  /**
   * Force cleanup all subscriptions (for debugging)
   */
  cleanup(): void {
    console.log('🧹 [REALTIME-MANAGER] Force cleanup all subscriptions')
    
    this.subscriptions.forEach((sub, key) => {
      console.log(`🗑️ [REALTIME-MANAGER] Removing subscription ${key}`)
      supabase.removeChannel(sub.channel)
    })
    
    this.subscriptions.clear()
  }
}

// Export singleton instance
export const realtimeManager = RealtimeManager.getInstance()

// Export hook for easy integration with existing useOptimizedData
export function useRealtimeSubscription(
  table: string,
  callback: (payload: RealtimePostgresChangesPayload<any>) => void,
  options: {
    enabled?: boolean
    userId?: string
    userRole?: string
    requiresAuth?: boolean
  } = {}
): void {
  const { enabled = true, userId, userRole, requiresAuth = true } = options

  React.useEffect(() => {
    if (!enabled) return

    console.log(`🔄 [REALTIME-HOOK] Setting up subscription for ${table}`)
    
    const unsubscribe = realtimeManager.subscribe(table, callback, {
      userId,
      userRole,
      requiresAuth
    })

    return unsubscribe
  }, [table, enabled, userId, userRole, requiresAuth, callback])
}