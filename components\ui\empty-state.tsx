import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Users,
  Building2,
  Target,
  Briefcase,
  CheckSquare,
  FileText,
  BarChart,
  Settings,
  Plus,
  Search,
  Database,
  AlertCircle
} from "lucide-react"

interface EmptyStateProps {
  type?: "customers" | "companies" | "leads" | "deals" | "tasks" | "reports" | "proposals" | "search" | "error" | "generic"
  title?: string
  description?: string
  actionLabel?: string
  onAction?: () => void
  showIcon?: boolean
  className?: string
  size?: "sm" | "md" | "lg"
}

export function EmptyState({
  type = "generic",
  title,
  description,
  actionLabel,
  onAction,
  showIcon = true,
  className,
  size = "md"
}: EmptyStateProps) {
  const getEmptyStateConfig = () => {
    switch (type) {
      case "customers":
        return {
          icon: Users,
          defaultTitle: "No customers yet",
          defaultDescription: "Start building your customer base by adding your first customer.",
          defaultActionLabel: "Add Customer",
          iconColor: "text-blue-500"
        }
      case "companies":
        return {
          icon: Building2,
          defaultTitle: "No companies found",
          defaultDescription: "Add companies to organize your business relationships and track opportunities.",
          defaultActionLabel: "Add Company",
          iconColor: "text-purple-500"
        }
      case "leads":
        return {
          icon: Target,
          defaultTitle: "No leads to show",
          defaultDescription: "Generate and capture leads to grow your sales pipeline.",
          defaultActionLabel: "Add Lead",
          iconColor: "text-green-500"
        }
      case "deals":
        return {
          icon: Briefcase,
          defaultTitle: "No deals in progress",
          defaultDescription: "Create deals to track your sales opportunities and revenue pipeline.",
          defaultActionLabel: "Create Deal",
          iconColor: "text-orange-500"
        }
      case "tasks":
        return {
          icon: CheckSquare,
          defaultTitle: "No tasks assigned",
          defaultDescription: "Stay organized by creating tasks and tracking your progress.",
          defaultActionLabel: "Add Task",
          iconColor: "text-indigo-500"
        }
      case "reports":
        return {
          icon: BarChart,
          defaultTitle: "No reports available",
          defaultDescription: "Generate reports to analyze your business performance and trends.",
          defaultActionLabel: "Create Report",
          iconColor: "text-cyan-500"
        }
      case "proposals":
        return {
          icon: FileText,
          defaultTitle: "No proposals created",
          defaultDescription: "Create professional proposals to win more deals and close sales.",
          defaultActionLabel: "Create Proposal",
          iconColor: "text-pink-500"
        }
      case "search":
        return {
          icon: Search,
          defaultTitle: "No results found",
          defaultDescription: "Try adjusting your search terms or filters to find what you're looking for.",
          defaultActionLabel: "Clear Filters",
          iconColor: "text-gray-500"
        }
      case "error":
        return {
          icon: AlertCircle,
          defaultTitle: "Something went wrong",
          defaultDescription: "We encountered an error while loading your data. Please try again.",
          defaultActionLabel: "Retry",
          iconColor: "text-red-500"
        }
      default:
        return {
          icon: Database,
          defaultTitle: "No data available",
          defaultDescription: "There's no data to display at the moment.",
          defaultActionLabel: "Add Data",
          iconColor: "text-gray-500"
        }
    }
  }

  const config = getEmptyStateConfig()
  const Icon = config.icon

  const finalTitle = title || config.defaultTitle
  const finalDescription = description || config.defaultDescription
  const finalActionLabel = actionLabel || config.defaultActionLabel

  const sizeClasses = {
    sm: {
      container: "py-8",
      icon: "h-12 w-12",
      title: "text-lg",
      description: "text-sm",
      spacing: "space-y-3"
    },
    md: {
      container: "py-12",
      icon: "h-16 w-16",
      title: "text-xl",
      description: "text-base",
      spacing: "space-y-4"
    },
    lg: {
      container: "py-16",
      icon: "h-20 w-20",
      title: "text-2xl",
      description: "text-lg",
      spacing: "space-y-6"
    }
  }

  const classes = sizeClasses[size]

  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center",
      classes.container,
      classes.spacing,
      className
    )}>
      {showIcon && (
        <div className={cn(
          "rounded-full bg-muted/50 p-4 mb-2",
          "border border-border/50"
        )}>
          <Icon className={cn(classes.icon, config.iconColor)} />
        </div>
      )}
      
      <div className="space-y-2">
        <h3 className={cn(
          "font-semibold text-foreground",
          classes.title
        )}>
          {finalTitle}
        </h3>
        
        <p className={cn(
          "text-muted-foreground max-w-md mx-auto",
          classes.description
        )}>
          {finalDescription}
        </p>
      </div>

      {onAction && (
        <Button 
          onClick={onAction}
          className="mt-4"
          size={size === "sm" ? "sm" : "default"}
        >
          <Plus className="h-4 w-4 mr-2" />
          {finalActionLabel}
        </Button>
      )}
    </div>
  )
}

// Specialized empty state components
export function SearchEmptyState({ 
  searchTerm, 
  onClearSearch,
  className 
}: { 
  searchTerm?: string
  onClearSearch?: () => void
  className?: string 
}) {
  return (
    <EmptyState
      type="search"
      title={searchTerm ? `No results for "${searchTerm}"` : "No results found"}
      description="Try different keywords or remove some filters to see more results."
      actionLabel="Clear Search"
      onAction={onClearSearch}
      className={className}
    />
  )
}

export function ErrorEmptyState({ 
  onRetry,
  errorMessage,
  className 
}: { 
  onRetry?: () => void
  errorMessage?: string
  className?: string 
}) {
  return (
    <EmptyState
      type="error"
      description={errorMessage || "We encountered an error while loading your data. Please try again."}
      actionLabel="Try Again"
      onAction={onRetry}
      className={className}
    />
  )
}

export function LoadingEmptyState({ 
  message = "Loading...",
  className 
}: { 
  message?: string
  className?: string 
}) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center text-center py-12 space-y-4",
      className
    )}>
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <p className="text-muted-foreground">{message}</p>
    </div>
  )
}
