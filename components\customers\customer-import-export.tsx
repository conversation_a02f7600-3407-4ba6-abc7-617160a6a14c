"use client"

import * as React from "react"
import { useState, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Download, Upload, FileText, FileSpreadsheet, Check, 
  AlertCircle, Loader2, X, Settings, Eye, EyeOff, 
  Users, Database, FileCheck, Alert<PERSON>riangle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { CustomerFormData, CustomerDatabaseSchema } from "@/app/types/customer"

// Import/Export Types
export interface CustomerExportOptions {
  format: "csv" | "excel" | "json"
  columns: string[]
  includeHeaders: boolean
  includeAddresses: boolean
  includeContacts: boolean
  includeDocuments: boolean
  dateFormat: "ISO" | "US" | "EU"
  encoding: "UTF-8" | "UTF-16" | "ASCII"
  delimiter?: string
  filters?: {
    status?: string[]
    tier?: string[]
    businessType?: string[]
    dateRange?: { start: string; end: string }
  }
}

export interface CustomerImportMapping {
  [sourceColumn: string]: {
    targetField: keyof CustomerFormData
    required: boolean
    type: "text" | "number" | "date" | "boolean" | "email" | "select"
    validation?: (value: any) => string | null
    transform?: (value: any) => any
  }
}

export interface ImportProgress {
  total: number
  processed: number
  successful: number
  failed: number
  percentage: number
  status: "idle" | "processing" | "complete" | "error"
  errors: Array<{ row: number; field: string; message: string }>
  warnings: Array<{ row: number; field: string; message: string }>
}

export interface ImportPreview {
  headers: string[]
  data: any[][]
  totalRows: number
  sampleRows: any[][]
  detectedTypes: Record<string, string>
}

// Export Column Definitions
const CUSTOMER_EXPORT_COLUMNS = [
  { id: "contact_person", label: "Contact Person", accessor: "contact_person", required: true },
  { id: "title_position", label: "Title/Position", accessor: "title_position" },
  { id: "email", label: "Email", accessor: "email", required: true },
  { id: "phone", label: "Phone", accessor: "phone" },
  { id: "mobile", label: "Mobile", accessor: "mobile" },
  { id: "company", label: "Company", accessor: "company", required: true },
  { id: "website", label: "Website", accessor: "website" },
  { id: "city", label: "City", accessor: "city", required: true },
  { id: "country", label: "Country", accessor: "country", required: true },
  { id: "business_type", label: "Business Type", accessor: "business_type" },
  { id: "industry", label: "Industry", accessor: "industry" },
  { id: "annual_volume", label: "Annual Volume", accessor: "annual_volume", format: "currency" },
  { id: "company_size", label: "Company Size", accessor: "company_size" },
  { id: "tax_id", label: "Tax ID", accessor: "tax_id" },
  { id: "credit_limit", label: "Credit Limit", accessor: "credit_limit", format: "currency" },
  { id: "payment_terms", label: "Payment Terms", accessor: "payment_terms" },
  { id: "currency_preference", label: "Currency", accessor: "currency_preference" },
  { id: "preferred_shipping_method", label: "Shipping Method", accessor: "preferred_shipping_method" },
  { id: "preferred_incoterms", label: "Incoterms", accessor: "preferred_incoterms" },
  { id: "shipping_instructions", label: "Shipping Instructions", accessor: "shipping_instructions" },
  { id: "account_manager", label: "Account Manager", accessor: "account_manager" },
  { id: "customer_since", label: "Customer Since", accessor: "customer_since", format: "date" },
  { id: "customer_tier", label: "Customer Tier", accessor: "customer_tier" },
  { id: "tags", label: "Tags", accessor: "tags", transform: (value: string[]) => value?.join(", ") || "" },
  { id: "required_certificates", label: "Required Certificates", accessor: "required_certificates", transform: (value: string[]) => value?.join(", ") || "" },
  { id: "compliance_requirements", label: "Compliance Requirements", accessor: "compliance_requirements", transform: (value: string[]) => value?.join(", ") || "" },
  { id: "source", label: "Source", accessor: "source" },
  { id: "status", label: "Status", accessor: "status" },
  { id: "notes", label: "Notes", accessor: "notes" }
]

// Customer Export Dialog
interface CustomerExportDialogProps {
  customers: CustomerDatabaseSchema[]
  onExport: (data: any[], options: CustomerExportOptions) => Promise<void>
  trigger?: React.ReactNode
}

export function CustomerExportDialog({
  customers,
  onExport,
  trigger
}: CustomerExportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [progress, setProgress] = useState(0)
  const [options, setOptions] = useState<CustomerExportOptions>({
    format: "csv",
    columns: CUSTOMER_EXPORT_COLUMNS.filter(col => col.required).map(col => col.id),
    includeHeaders: true,
    includeAddresses: false,
    includeContacts: false,
    includeDocuments: false,
    dateFormat: "ISO",
    encoding: "UTF-8",
    delimiter: ","
  })

  const handleExport = async () => {
    setIsExporting(true)
    setProgress(0)

    try {
      // Filter and transform data based on selected columns
      const selectedColumns = CUSTOMER_EXPORT_COLUMNS.filter(col => 
        options.columns.includes(col.id)
      )

      const exportData = customers.map(customer => {
        const row: any = {}
        selectedColumns.forEach(col => {
          let value = (customer as any)[col.accessor]
          
          // Apply transformations
          if (col.transform && value !== undefined && value !== null) {
            value = col.transform(value)
          }
          
          // Format based on type
          if (col.format === "currency" && typeof value === "number") {
            value = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: customer.currency_preference || 'USD'
            }).format(value)
          } else if (col.format === "date" && value) {
            const date = new Date(value)
            switch (options.dateFormat) {
              case "US":
                value = date.toLocaleDateString('en-US')
                break
              case "EU":
                value = date.toLocaleDateString('en-GB')
                break
              default:
                value = date.toISOString().split('T')[0]
            }
          }
          
          row[col.label] = value || ""
        })
        return row
      })

      // Simulate progress
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i)
        await new Promise(resolve => setTimeout(resolve, 50))
      }

      await onExport(exportData, options)
      setIsOpen(false)
    } catch (error) {
      console.error("Export failed:", error)
    } finally {
      setIsExporting(false)
      setProgress(0)
    }
  }

  const toggleColumn = (columnId: string) => {
    const column = CUSTOMER_EXPORT_COLUMNS.find(col => col.id === columnId)
    if (column?.required) return // Can't deselect required columns

    setOptions(prev => ({
      ...prev,
      columns: prev.columns.includes(columnId)
        ? prev.columns.filter(id => id !== columnId)
        : [...prev.columns, columnId]
    }))
  }

  const selectAllColumns = () => {
    setOptions(prev => ({
      ...prev,
      columns: CUSTOMER_EXPORT_COLUMNS.map(col => col.id)
    }))
  }

  const selectRequiredOnly = () => {
    setOptions(prev => ({
      ...prev,
      columns: CUSTOMER_EXPORT_COLUMNS.filter(col => col.required).map(col => col.id)
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export Customers
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Customer Data
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Export Summary */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium">{customers.length} customers selected</p>
                    <p className="text-sm text-muted-foreground">
                      {options.columns.length} columns will be exported
                    </p>
                  </div>
                </div>
                <Badge variant="outline">
                  {options.format.toUpperCase()}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Format & Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Export Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>File Format</Label>
                  <Select
                    value={options.format}
                    onValueChange={(value: any) => setOptions(prev => ({ ...prev, format: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="csv">
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          CSV File
                        </div>
                      </SelectItem>
                      <SelectItem value="excel">
                        <div className="flex items-center gap-2">
                          <FileSpreadsheet className="h-4 w-4" />
                          Excel File
                        </div>
                      </SelectItem>
                      <SelectItem value="json">
                        <div className="flex items-center gap-2">
                          <Database className="h-4 w-4" />
                          JSON File
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Date Format</Label>
                  <Select
                    value={options.dateFormat}
                    onValueChange={(value: any) => setOptions(prev => ({ ...prev, dateFormat: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ISO">ISO (YYYY-MM-DD)</SelectItem>
                      <SelectItem value="US">US (MM/DD/YYYY)</SelectItem>
                      <SelectItem value="EU">EU (DD/MM/YYYY)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Encoding</Label>
                  <Select
                    value={options.encoding}
                    onValueChange={(value: any) => setOptions(prev => ({ ...prev, encoding: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTF-8">UTF-8</SelectItem>
                      <SelectItem value="UTF-16">UTF-16</SelectItem>
                      <SelectItem value="ASCII">ASCII</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {options.format === "csv" && (
                  <div>
                    <Label>Delimiter</Label>
                    <Select
                      value={options.delimiter}
                      onValueChange={(value) => setOptions(prev => ({ ...prev, delimiter: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value=",">Comma (,)</SelectItem>
                        <SelectItem value=";">Semicolon (;)</SelectItem>
                        <SelectItem value="\t">Tab</SelectItem>
                        <SelectItem value="|">Pipe (|)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeHeaders"
                      checked={options.includeHeaders}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, includeHeaders: !!checked }))
                      }
                    />
                    <Label htmlFor="includeHeaders">Include column headers</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeAddresses"
                      checked={options.includeAddresses}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, includeAddresses: !!checked }))
                      }
                    />
                    <Label htmlFor="includeAddresses">Include addresses</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeContacts"
                      checked={options.includeContacts}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, includeContacts: !!checked }))
                      }
                    />
                    <Label htmlFor="includeContacts">Include additional contacts</Label>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Column Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center justify-between">
                  Column Selection
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectRequiredOnly}
                    >
                      Required Only
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectAllColumns}
                    >
                      Select All
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {CUSTOMER_EXPORT_COLUMNS.map((column) => (
                    <div key={column.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={column.id}
                        checked={options.columns.includes(column.id)}
                        onCheckedChange={() => toggleColumn(column.id)}
                        disabled={column.required}
                      />
                      <Label 
                        htmlFor={column.id}
                        className={cn(
                          "flex-1 text-sm",
                          column.required && "font-medium"
                        )}
                      >
                        {column.label}
                        {column.required && (
                          <Badge variant="secondary" className="ml-2 text-xs">
                            Required
                          </Badge>
                        )}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Export Progress */}
          {isExporting && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Exporting customers...</span>
                    <span className="text-sm text-muted-foreground">{progress}%</span>
                  </div>
                  <Progress value={progress} />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isExporting}
            >
              Cancel
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || options.columns.length === 0}
              className="gap-2"
            >
              {isExporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  Export {customers.length} Customers
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Customer Import Dialog
interface CustomerImportDialogProps {
  onImport: (data: CustomerFormData[], mapping: CustomerImportMapping) => Promise<void>
  trigger?: React.ReactNode
}

export function CustomerImportDialog({
  onImport,
  trigger
}: CustomerImportDialogProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [step, setStep] = useState<"upload" | "mapping" | "preview" | "import">("upload")
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<ImportPreview | null>(null)
  const [mapping, setMapping] = useState<CustomerImportMapping>({})
  const [progress, setProgress] = useState<ImportProgress>({
    total: 0,
    processed: 0,
    successful: 0,
    failed: 0,
    percentage: 0,
    status: "idle",
    errors: [],
    warnings: []
  })

  // Default field mapping for common column names
  const getDefaultMapping = (headers: string[]): CustomerImportMapping => {
    const mapping: CustomerImportMapping = {}

    headers.forEach(header => {
      const lowerHeader = header.toLowerCase().trim()

      // Map common variations to our fields
      if (lowerHeader.includes('contact') && lowerHeader.includes('person') || lowerHeader === 'name' || lowerHeader === 'contact_name') {
        mapping[header] = { targetField: 'contact_person', required: true, type: 'text' }
      } else if (lowerHeader.includes('email') || lowerHeader === 'email_address') {
        mapping[header] = { targetField: 'email', required: true, type: 'email' }
      } else if (lowerHeader.includes('company') || lowerHeader === 'organization') {
        mapping[header] = { targetField: 'company', required: true, type: 'text' }
      } else if (lowerHeader.includes('phone') && !lowerHeader.includes('mobile')) {
        mapping[header] = { targetField: 'phone', required: false, type: 'text' }
      } else if (lowerHeader.includes('mobile') || lowerHeader.includes('cell')) {
        mapping[header] = { targetField: 'mobile', required: false, type: 'text' }
      } else if (lowerHeader.includes('city')) {
        mapping[header] = { targetField: 'city', required: true, type: 'text' }
      } else if (lowerHeader.includes('country')) {
        mapping[header] = { targetField: 'country', required: true, type: 'text' }
      } else if (lowerHeader.includes('title') || lowerHeader.includes('position')) {
        mapping[header] = { targetField: 'title_position', required: false, type: 'text' }
      } else if (lowerHeader.includes('website') || lowerHeader.includes('url')) {
        mapping[header] = { targetField: 'website', required: false, type: 'text' }
      } else if (lowerHeader.includes('industry')) {
        mapping[header] = { targetField: 'industry', required: false, type: 'text' }
      } else if (lowerHeader.includes('business') && lowerHeader.includes('type')) {
        mapping[header] = { targetField: 'business_type', required: false, type: 'select' }
      } else if (lowerHeader.includes('annual') && lowerHeader.includes('volume')) {
        mapping[header] = { targetField: 'annual_volume', required: false, type: 'number' }
      } else if (lowerHeader.includes('notes') || lowerHeader.includes('comment')) {
        mapping[header] = { targetField: 'notes', required: false, type: 'text' }
      } else if (lowerHeader.includes('status')) {
        mapping[header] = { targetField: 'status', required: false, type: 'select' }
      } else if (lowerHeader.includes('source')) {
        mapping[header] = { targetField: 'source', required: false, type: 'select' }
      }
    })

    return mapping
  }

  const parseCSV = (text: string): string[][] => {
    const lines = text.split('\n').filter(line => line.trim())
    return lines.map(line => {
      const result = []
      let current = ''
      let inQuotes = false

      for (let i = 0; i < line.length; i++) {
        const char = line[i]

        if (char === '"') {
          inQuotes = !inQuotes
        } else if (char === ',' && !inQuotes) {
          result.push(current.trim())
          current = ''
        } else {
          current += char
        }
      }

      result.push(current.trim())
      return result
    })
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0]
    if (!selectedFile) return

    setFile(selectedFile)

    try {
      const text = await selectedFile.text()
      let data: string[][]

      if (selectedFile.name.endsWith('.csv')) {
        data = parseCSV(text)
      } else if (selectedFile.name.endsWith('.json')) {
        const jsonData = JSON.parse(text)
        if (Array.isArray(jsonData) && jsonData.length > 0) {
          const headers = Object.keys(jsonData[0])
          data = [headers, ...jsonData.map(item => headers.map(h => item[h] || ''))]
        } else {
          throw new Error('Invalid JSON format')
        }
      } else {
        throw new Error('Unsupported file format')
      }

      if (data.length < 2) {
        throw new Error('File must contain at least a header row and one data row')
      }

      const headers = data[0]
      const rows = data.slice(1)

      const preview: ImportPreview = {
        headers,
        data: rows,
        totalRows: rows.length,
        sampleRows: rows.slice(0, 5),
        detectedTypes: {}
      }

      // Detect column types
      headers.forEach((header, index) => {
        const sampleValues = rows.slice(0, 10).map(row => row[index]).filter(Boolean)

        if (sampleValues.every(val => /^\d+$/.test(val))) {
          preview.detectedTypes[header] = 'number'
        } else if (sampleValues.every(val => /^[\w.-]+@[\w.-]+\.\w+$/.test(val))) {
          preview.detectedTypes[header] = 'email'
        } else if (sampleValues.every(val => /^\d{4}-\d{2}-\d{2}/.test(val))) {
          preview.detectedTypes[header] = 'date'
        } else {
          preview.detectedTypes[header] = 'text'
        }
      })

      setPreview(preview)
      setMapping(getDefaultMapping(headers))
      setStep("mapping")

    } catch (error) {
      console.error("Failed to parse file:", error)
      alert("Failed to parse file. Please check the format and try again.")
    }
  }

  const validateData = (data: any[], mapping: CustomerImportMapping): ImportProgress => {
    const errors: Array<{ row: number; field: string; message: string }> = []
    const warnings: Array<{ row: number; field: string; message: string }> = []

    data.forEach((row, index) => {
      Object.entries(mapping).forEach(([sourceColumn, config]) => {
        const value = row[sourceColumn]
        const rowNumber = index + 2 // +2 because index is 0-based and we skip header

        // Check required fields
        if (config.required && (!value || value.toString().trim() === '')) {
          errors.push({
            row: rowNumber,
            field: config.targetField,
            message: `Required field '${config.targetField}' is empty`
          })
        }

        // Validate email format
        if (config.type === 'email' && value && !/^[\w.-]+@[\w.-]+\.\w+$/.test(value)) {
          errors.push({
            row: rowNumber,
            field: config.targetField,
            message: `Invalid email format: ${value}`
          })
        }

        // Validate number format
        if (config.type === 'number' && value && isNaN(Number(value))) {
          errors.push({
            row: rowNumber,
            field: config.targetField,
            message: `Invalid number format: ${value}`
          })
        }

        // Custom validation
        if (config.validation && value) {
          const validationError = config.validation(value)
          if (validationError) {
            errors.push({
              row: rowNumber,
              field: config.targetField,
              message: validationError
            })
          }
        }
      })
    })

    return {
      total: data.length,
      processed: 0,
      successful: 0,
      failed: errors.length,
      percentage: 0,
      status: errors.length > 0 ? "error" : "idle",
      errors,
      warnings
    }
  }

  const transformData = (data: any[], mapping: CustomerImportMapping): CustomerFormData[] => {
    return data.map(row => {
      const customer: Partial<CustomerFormData> = {
        source: "Other",
        status: "Active"
      }

      Object.entries(mapping).forEach(([sourceColumn, config]) => {
        let value = row[sourceColumn]

        if (value !== undefined && value !== null && value !== '') {
          // Apply transformations
          if (config.transform) {
            value = config.transform(value)
          }

          // Type conversions
          if (config.type === 'number') {
            value = Number(value)
          } else if (config.type === 'boolean') {
            value = ['true', '1', 'yes', 'y'].includes(value.toString().toLowerCase())
          }

          customer[config.targetField] = value
        }
      })

      return customer as CustomerFormData
    })
  }

  const handleImport = async () => {
    if (!preview || !file) return

    setStep("import")

    // Validate data
    const validation = validateData(preview.data, mapping)
    setProgress(validation)

    if (validation.errors.length > 0) {
      return // Stop if there are validation errors
    }

    try {
      // Transform data
      const transformedData = transformData(preview.data, mapping)

      // Simulate progress
      for (let i = 0; i <= transformedData.length; i++) {
        setProgress(prev => ({
          ...prev,
          processed: i,
          percentage: Math.round((i / transformedData.length) * 100),
          status: i === transformedData.length ? "complete" : "processing"
        }))

        if (i < transformedData.length) {
          await new Promise(resolve => setTimeout(resolve, 50))
        }
      }

      await onImport(transformedData, mapping)

      setProgress(prev => ({
        ...prev,
        successful: transformedData.length,
        status: "complete"
      }))

      // Close dialog after successful import
      setTimeout(() => {
        setIsOpen(false)
        resetState()
      }, 2000)

    } catch (error) {
      console.error("Import failed:", error)
      setProgress(prev => ({
        ...prev,
        status: "error",
        errors: [...prev.errors, { row: 0, field: "general", message: "Import failed: " + (error as Error).message }]
      }))
    }
  }

  const resetState = () => {
    setStep("upload")
    setFile(null)
    setPreview(null)
    setMapping({})
    setProgress({
      total: 0,
      processed: 0,
      successful: 0,
      failed: 0,
      percentage: 0,
      status: "idle",
      errors: [],
      warnings: []
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open)
      if (!open) resetState()
    }}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="gap-2">
            <Upload className="h-4 w-4" />
            Import Customers
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Import Customer Data
          </DialogTitle>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center justify-center space-x-4 py-4">
          {[
            { id: "upload", label: "Upload", icon: Upload },
            { id: "mapping", label: "Mapping", icon: Settings },
            { id: "preview", label: "Preview", icon: Eye },
            { id: "import", label: "Import", icon: Database }
          ].map((stepItem, index) => (
            <div key={stepItem.id} className="flex items-center">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full border-2",
                step === stepItem.id ? "border-primary bg-primary text-primary-foreground" :
                ["upload", "mapping", "preview", "import"].indexOf(step) > index ? "border-green-500 bg-green-500 text-white" :
                "border-muted bg-muted text-muted-foreground"
              )}>
                <stepItem.icon className="h-4 w-4" />
              </div>
              <span className={cn(
                "ml-2 text-sm",
                step === stepItem.id ? "font-medium" : "text-muted-foreground"
              )}>
                {stepItem.label}
              </span>
              {index < 3 && (
                <div className={cn(
                  "w-8 h-0.5 mx-4",
                  ["upload", "mapping", "preview", "import"].indexOf(step) > index ? "bg-green-500" : "bg-muted"
                )} />
              )}
            </div>
          ))}
        </div>

        <Separator />

        {/* Step Content */}
        <div className="space-y-6">
          {step === "upload" && (
            <Card>
              <CardHeader>
                <CardTitle>Upload Customer Data File</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <div className="space-y-2">
                    <p className="text-lg font-medium">Choose a file to upload</p>
                    <p className="text-sm text-muted-foreground">
                      Supported formats: CSV, Excel, JSON
                    </p>
                  </div>
                  <input
                    type="file"
                    accept=".csv,.xlsx,.xls,.json"
                    onChange={handleFileUpload}
                    className="mt-4"
                  />
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Make sure your file includes columns for required fields: Contact Person, Email, Company, City, and Country.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}

          {step === "mapping" && preview && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Map Columns to Customer Fields</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {preview.headers.map((header, index) => (
                      <div key={index} className="space-y-2">
                        <Label className="font-medium">{header}</Label>
                        <div className="text-xs text-muted-foreground mb-2">
                          Sample: {preview.sampleRows[0]?.[index] || 'N/A'}
                        </div>
                        <Select
                          value={mapping[header]?.targetField || ""}
                          onValueChange={(value) => {
                            if (value === "") {
                              const newMapping = { ...mapping }
                              delete newMapping[header]
                              setMapping(newMapping)
                            } else {
                              setMapping(prev => ({
                                ...prev,
                                [header]: {
                                  targetField: value as keyof CustomerFormData,
                                  required: ['contact_person', 'email', 'company', 'city', 'country'].includes(value),
                                  type: preview.detectedTypes[header] as any || 'text'
                                }
                              }))
                            }
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select field..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">Don't import</SelectItem>
                            {CUSTOMER_EXPORT_COLUMNS.map(col => (
                              <SelectItem key={col.id} value={col.id}>
                                {col.label}
                                {col.required && <Badge variant="secondary" className="ml-2">Required</Badge>}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep("upload")}>
                  Back
                </Button>
                <Button
                  onClick={() => setStep("preview")}
                  disabled={Object.values(mapping).filter(m => m.required).length < 5}
                >
                  Preview Import
                </Button>
              </div>
            </div>
          )}

          {step === "preview" && preview && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Import Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold">{preview.totalRows}</div>
                        <div className="text-sm text-muted-foreground">Total Records</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-green-600">
                          {Object.values(mapping).filter(m => m.required).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Required Fields Mapped</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-blue-600">
                          {Object.keys(mapping).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Total Fields Mapped</div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium mb-2">Sample Data Preview</h4>
                      <div className="overflow-x-auto">
                        <table className="w-full border border-border">
                          <thead>
                            <tr className="bg-muted">
                              {Object.entries(mapping).map(([sourceCol, config]) => (
                                <th key={sourceCol} className="border border-border p-2 text-left text-xs">
                                  {config.targetField}
                                  {config.required && <span className="text-red-500 ml-1">*</span>}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {preview.sampleRows.slice(0, 3).map((row, index) => (
                              <tr key={index}>
                                {Object.entries(mapping).map(([sourceCol, config]) => (
                                  <td key={sourceCol} className="border border-border p-2 text-xs">
                                    {row[preview.headers.indexOf(sourceCol)] || '-'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-between">
                <Button variant="outline" onClick={() => setStep("mapping")}>
                  Back to Mapping
                </Button>
                <Button onClick={handleImport}>
                  Import {preview.totalRows} Records
                </Button>
              </div>
            </div>
          )}

          {step === "import" && (
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Import Progress</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span>{progress.processed} / {progress.total}</span>
                    </div>
                    <Progress value={progress.percentage} />
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-green-600">{progress.successful}</div>
                      <div className="text-xs text-muted-foreground">Successful</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-red-600">{progress.failed}</div>
                      <div className="text-xs text-muted-foreground">Failed</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-yellow-600">{progress.warnings.length}</div>
                      <div className="text-xs text-muted-foreground">Warnings</div>
                    </div>
                  </div>

                  {progress.errors.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-red-600">Errors</h4>
                      <div className="max-h-32 overflow-y-auto space-y-1">
                        {progress.errors.map((error, index) => (
                          <div key={index} className="text-xs bg-red-50 p-2 rounded">
                            Row {error.row}: {error.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {progress.status === "complete" && (
                    <Alert>
                      <Check className="h-4 w-4" />
                      <AlertDescription>
                        Import completed successfully! {progress.successful} customers have been imported.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
