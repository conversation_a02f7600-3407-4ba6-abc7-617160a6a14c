{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/crmnew-main/__tests__", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/crmnew-main/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/crmnew-main/__tests__", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "smoke.spec.ts", "file": "smoke.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Smoke Tests - Basic Functionality", "file": "smoke.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should load homepage without errors", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 3041, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-12T13:16:30.629Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-d3abe3cb3885dbcd6878", "file": "smoke.spec.ts", "line": 9, "column": 7}, {"title": "should navigate to login page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 8931, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1, h2')\nExpected pattern: \u001b[32m/login|sign in/i\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1, h2')\nExpected pattern: \u001b[32m/login|sign in/i\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2')\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\crmnew-main\\__tests__\\smoke.spec.ts:32:42", "location": {"file": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\__tests__\\smoke.spec.ts", "column": 42, "line": 32}, "snippet": "\u001b[0m \u001b[90m 30 |\u001b[39m   test(\u001b[32m'should navigate to login page'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[35m/login|sign in/i\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// Check for login form elements\u001b[39m\n \u001b[90m 35 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\__tests__\\smoke.spec.ts", "column": 42, "line": 32}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoContainText\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('h1, h2')\nExpected pattern: \u001b[32m/login|sign in/i\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toContainText\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('h1, h2')\u001b[22m\n\n\n\u001b[0m \u001b[90m 30 |\u001b[39m   test(\u001b[32m'should navigate to login page'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 31 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/login'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 32 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2'\u001b[39m))\u001b[33m.\u001b[39mtoContainText(\u001b[35m/login|sign in/i\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                          \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 33 |\u001b[39m     \n \u001b[90m 34 |\u001b[39m     \u001b[90m// Check for login form elements\u001b[39m\n \u001b[90m 35 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\crmnew-main\\__tests__\\smoke.spec.ts:32:42\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-12T13:16:30.631Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\test-results\\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\test-results\\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\test-results\\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\crmnew-main\\__tests__\\smoke.spec.ts", "column": 42, "line": 32}}], "status": "unexpected"}], "id": "4219922fea2e2bd3c691-b5098d94d75a1638aa0e", "file": "smoke.spec.ts", "line": 30, "column": 7}, {"title": "should attempt login with test credentials", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 9334, "errors": [], "stdout": [{"text": "Current URL after login attempt: http://localhost:3000/login\n"}, {"text": "Page content after login: Welcome to Nawras CRMEmailPasswordInvalid email or passwordSign InOr continue withGoogleGitHubالعربية$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data=\"$!\",a.setAttribute(\"data-dgst\",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if(\"/$\"===d)if(0===f)break;else f--;else\"$\"!==d&&\"$?\"!==d&&\"$!\"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChi\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-12T13:16:30.680Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-602f30e4fdad917e9cef", "file": "smoke.spec.ts", "line": 39, "column": 7}, {"title": "should check dashboard accessibility (if logged in)", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 5585, "errors": [], "stdout": [{"text": "Dashboard access URL: http://localhost:3000/login?callbackUrl=https%3A%2F%2Fsales.nawrasinchina.com%2Fdashboard\n"}, {"text": "Dashboard requires authentication - redirected to login\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-12T13:16:30.733Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-5359e6eed03ab7a9ebc9", "file": "smoke.spec.ts", "line": 61, "column": 7}, {"title": "should check for 404 errors on main routes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 17290, "errors": [], "stdout": [{"text": "Route /dashboard/customers: Status 200 - Accessible\n"}, {"text": "Route /dashboard/deals: Status 200 - Accessible\n"}, {"text": "Route /dashboard/leads: Status 200 - Accessible\n"}, {"text": "Route /dashboard/opportunities: Status 200 - Accessible\n"}, {"text": "Route /dashboard/companies: Status 200 - Accessible\n"}, {"text": "Route /dashboard/tasks: Status 200 - Accessible\n"}, {"text": "Route /dashboard/reports: Status 200 - Accessible\n"}, {"text": "Route /dashboard/settings: Status 200 - Accessible\n"}, {"text": "Route /dashboard/admin: Status 200 - Accessible\n"}, {"text": "Route accessibility: 9/9 routes accessible\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-12T13:16:33.999Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "4219922fea2e2bd3c691-5637a675bf8250fd37a6", "file": "smoke.spec.ts", "line": 86, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-07-12T13:16:13.657Z", "duration": 38252.217, "expected": 4, "skipped": 0, "unexpected": 1, "flaky": 0}}