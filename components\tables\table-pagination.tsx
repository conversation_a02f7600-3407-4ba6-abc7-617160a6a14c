"use client"

import * as React from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight,
  MoreHorizontal, Loader2
} from "lucide-react"
import { cn } from "@/lib/utils"

// Pagination Types
export interface PaginationState {
  pageIndex: number
  pageSize: number
}

export interface PaginationInfo {
  totalItems: number
  totalPages: number
  currentPage: number
  startItem: number
  endItem: number
  hasNextPage: boolean
  hasPreviousPage: boolean
}

export interface TablePaginationProps {
  pagination: PaginationState
  onPaginationChange: (pagination: PaginationState) => void
  totalItems: number
  pageSizeOptions?: number[]
  showPageSizeSelector?: boolean
  showPageNumbers?: boolean
  showItemRange?: boolean
  showGoToPage?: boolean
  loading?: boolean
  className?: string
  variant?: "default" | "simple" | "compact"
}

// Calculate pagination info
function usePaginationInfo(pagination: PaginationState, totalItems: number): PaginationInfo {
  return React.useMemo(() => {
    const totalPages = Math.ceil(totalItems / pagination.pageSize)
    const currentPage = pagination.pageIndex + 1
    const startItem = pagination.pageIndex * pagination.pageSize + 1
    const endItem = Math.min(startItem + pagination.pageSize - 1, totalItems)
    
    return {
      totalItems,
      totalPages,
      currentPage,
      startItem,
      endItem,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1
    }
  }, [pagination, totalItems])
}

// Page Numbers Component
function PageNumbers({
  currentPage,
  totalPages,
  onPageChange,
  maxVisible = 7
}: {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  maxVisible?: number
}) {
  const getVisiblePages = () => {
    if (totalPages <= maxVisible) {
      return Array.from({ length: totalPages }, (_, i) => i + 1)
    }

    const half = Math.floor(maxVisible / 2)
    let start = Math.max(1, currentPage - half)
    let end = Math.min(totalPages, start + maxVisible - 1)

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1)
    }

    const pages = []
    
    // Always show first page
    if (start > 1) {
      pages.push(1)
      if (start > 2) {
        pages.push("ellipsis-start")
      }
    }

    // Add visible pages
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }

    // Always show last page
    if (end < totalPages) {
      if (end < totalPages - 1) {
        pages.push("ellipsis-end")
      }
      pages.push(totalPages)
    }

    return pages
  }

  const visiblePages = getVisiblePages()

  return (
    <div className="flex items-center gap-1">
      {visiblePages.map((page, index) => {
        if (typeof page === "string") {
          return (
            <div key={page} className="px-2">
              <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
            </div>
          )
        }

        return (
          <Button
            key={page}
            variant={page === currentPage ? "default" : "outline"}
            size="sm"
            className="w-8 h-8 p-0"
            onClick={() => onPageChange(page - 1)}
          >
            {page}
          </Button>
        )
      })}
    </div>
  )
}

// Go to Page Component
function GoToPage({
  currentPage,
  totalPages,
  onPageChange
}: {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
}) {
  const [inputValue, setInputValue] = React.useState("")
  const [isOpen, setIsOpen] = React.useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const page = parseInt(inputValue)
    if (page >= 1 && page <= totalPages) {
      onPageChange(page - 1)
      setInputValue("")
      setIsOpen(false)
    }
  }

  if (!isOpen) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="text-xs"
      >
        Go to page
      </Button>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="flex items-center gap-2">
      <Input
        type="number"
        min={1}
        max={totalPages}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        placeholder={`1-${totalPages}`}
        className="w-20 h-8 text-xs"
        autoFocus
        onBlur={() => {
          if (!inputValue) setIsOpen(false)
        }}
      />
      <Button type="submit" size="sm" className="h-8 px-2 text-xs">
        Go
      </Button>
    </form>
  )
}

// Main Pagination Component
export function TablePagination({
  pagination,
  onPaginationChange,
  totalItems,
  pageSizeOptions = [10, 25, 50, 100],
  showPageSizeSelector = true,
  showPageNumbers = true,
  showItemRange = true,
  showGoToPage = false,
  loading = false,
  className,
  variant = "default"
}: TablePaginationProps) {
  const info = usePaginationInfo(pagination, totalItems)

  const handlePageChange = (newPageIndex: number) => {
    onPaginationChange({
      ...pagination,
      pageIndex: newPageIndex
    })
  }

  const handlePageSizeChange = (newPageSize: number) => {
    onPaginationChange({
      pageIndex: 0,
      pageSize: newPageSize
    })
  }

  if (totalItems === 0) {
    return null
  }

  // Simple variant - just prev/next buttons
  if (variant === "simple") {
    return (
      <div className={cn("flex items-center justify-between", className)}>
        {showItemRange && (
          <div className="text-sm text-muted-foreground">
            {info.startItem}-{info.endItem} of {info.totalItems}
          </div>
        )}
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.pageIndex - 1)}
            disabled={!info.hasPreviousPage || loading}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(pagination.pageIndex + 1)}
            disabled={!info.hasNextPage || loading}
          >
            Next
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </div>
    )
  }

  // Compact variant - minimal controls
  if (variant === "compact") {
    return (
      <div className={cn("flex items-center justify-center gap-2", className)}>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.pageIndex - 1)}
          disabled={!info.hasPreviousPage || loading}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <span className="text-sm text-muted-foreground px-2">
          {info.currentPage} / {info.totalPages}
        </span>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.pageIndex + 1)}
          disabled={!info.hasNextPage || loading}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    )
  }

  // Default variant - full controls
  return (
    <div className={cn("flex items-center justify-between gap-4 flex-wrap", className)}>
      {/* Left side - Item range and page size */}
      <div className="flex items-center gap-4 text-sm text-muted-foreground">
        {showItemRange && (
          <div className="flex items-center gap-2">
            {loading && <Loader2 className="h-3 w-3 animate-spin" />}
            <span>
              Showing {info.startItem} to {info.endItem} of {info.totalItems} results
            </span>
          </div>
        )}
        
        {showPageSizeSelector && (
          <div className="flex items-center gap-2">
            <span>Rows per page:</span>
            <Select
              value={pagination.pageSize.toString()}
              onValueChange={(value) => handlePageSizeChange(Number(value))}
              disabled={loading}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pageSizeOptions.map((size) => (
                  <SelectItem key={size} value={size.toString()}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Right side - Navigation controls */}
      <div className="flex items-center gap-2">
        {showGoToPage && info.totalPages > 10 && (
          <GoToPage
            currentPage={info.currentPage}
            totalPages={info.totalPages}
            onPageChange={handlePageChange}
          />
        )}
        
        {/* First page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(0)}
          disabled={!info.hasPreviousPage || loading}
          className="w-8 h-8 p-0"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        
        {/* Previous page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.pageIndex - 1)}
          disabled={!info.hasPreviousPage || loading}
          className="w-8 h-8 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        {/* Page numbers */}
        {showPageNumbers && info.totalPages > 1 && (
          <PageNumbers
            currentPage={info.currentPage}
            totalPages={info.totalPages}
            onPageChange={handlePageChange}
          />
        )}
        
        {/* Next page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(pagination.pageIndex + 1)}
          disabled={!info.hasNextPage || loading}
          className="w-8 h-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        
        {/* Last page button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(info.totalPages - 1)}
          disabled={!info.hasNextPage || loading}
          className="w-8 h-8 p-0"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}

// Virtual Scrolling Hook
export function useVirtualScrolling<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = React.useState(0)

  const visibleRange = React.useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )
    
    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])

  const visibleItems = React.useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
  }, [items, visibleRange])

  const totalHeight = items.length * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight

  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange
  }
}

// Virtual Table Component
export function VirtualTable<T>({
  items,
  itemHeight = 50,
  height = 400,
  renderItem,
  className
}: {
  items: T[]
  itemHeight?: number
  height?: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
}) {
  const {
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange
  } = useVirtualScrolling({
    items,
    itemHeight,
    containerHeight: height
  })

  return (
    <div
      className={cn("overflow-auto border rounded-lg", className)}
      style={{ height }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0
          }}
        >
          {visibleItems.map((item, index) => (
            <div
              key={visibleRange.startIndex + index}
              style={{ height: itemHeight }}
              className="border-b last:border-b-0"
            >
              {renderItem(item, visibleRange.startIndex + index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
