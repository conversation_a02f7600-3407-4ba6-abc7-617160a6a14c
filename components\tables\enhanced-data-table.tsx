"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu"
import { 
  Search, Filter, ArrowUpDown, ArrowUp, ArrowDown, MoreHorizontal,
  Eye, EyeOff, Download, RefreshCw, Settings, ChevronLeft, ChevronRight,
  Ch<PERSON>rons<PERSON><PERSON>t, Chev<PERSON><PERSON><PERSON>, <PERSON><PERSON>2, X, Plus
} from "lucide-react"
import { cn } from "@/lib/utils"

// Table Configuration Types
export interface TableColumn<T = any> {
  id: string
  header: string
  accessorKey?: keyof T
  cell?: (row: T) => React.ReactNode
  sortable?: boolean
  filterable?: boolean
  width?: string | number
  minWidth?: string | number
  maxWidth?: string | number
  align?: "left" | "center" | "right"
  sticky?: boolean
  hidden?: boolean
  meta?: {
    filterType?: "text" | "select" | "date" | "number" | "boolean"
    filterOptions?: Array<{ label: string; value: any }>
    format?: "currency" | "date" | "percentage" | "number"
  }
}

export interface TableFilter {
  id: string
  value: any
  operator?: "equals" | "contains" | "startsWith" | "endsWith" | "gt" | "lt" | "gte" | "lte"
}

export interface TableSort {
  id: string
  desc: boolean
}

export interface TablePagination {
  pageIndex: number
  pageSize: number
}

export interface TableSelection {
  selectedRows: Set<string>
  isAllSelected: boolean
  isIndeterminate: boolean
}

export interface EnhancedDataTableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  loading?: boolean
  error?: string
  
  // Pagination
  pagination?: TablePagination
  onPaginationChange?: (pagination: TablePagination) => void
  totalCount?: number
  pageSizeOptions?: number[]
  
  // Sorting
  sorting?: TableSort[]
  onSortingChange?: (sorting: TableSort[]) => void
  
  // Filtering
  filters?: TableFilter[]
  onFiltersChange?: (filters: TableFilter[]) => void
  globalFilter?: string
  onGlobalFilterChange?: (filter: string) => void
  
  // Selection
  selection?: TableSelection
  onSelectionChange?: (selection: TableSelection) => void
  enableSelection?: boolean
  
  // Actions
  onRowClick?: (row: T) => void
  onRowDoubleClick?: (row: T) => void
  rowActions?: Array<{
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: (row: T) => void
    variant?: "default" | "destructive"
    disabled?: (row: T) => boolean
  }>
  
  // Bulk Actions
  bulkActions?: Array<{
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: (selectedRows: T[]) => void
    variant?: "default" | "destructive"
  }>
  
  // Customization
  title?: string
  description?: string
  searchPlaceholder?: string
  emptyMessage?: string
  className?: string
  variant?: "default" | "compact" | "comfortable"
  striped?: boolean
  bordered?: boolean
  
  // Features
  enableGlobalSearch?: boolean
  enableColumnFilters?: boolean
  enableColumnVisibility?: boolean
  enableExport?: boolean
  enableRefresh?: boolean
  onRefresh?: () => void
  onExport?: (format: "csv" | "excel") => void
}

export function EnhancedDataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  error,
  pagination,
  onPaginationChange,
  totalCount,
  pageSizeOptions = [10, 25, 50, 100],
  sorting = [],
  onSortingChange,
  filters = [],
  onFiltersChange,
  globalFilter = "",
  onGlobalFilterChange,
  selection,
  onSelectionChange,
  enableSelection = false,
  onRowClick,
  onRowDoubleClick,
  rowActions = [],
  bulkActions = [],
  title,
  description,
  searchPlaceholder = "Search...",
  emptyMessage = "No data available",
  className,
  variant = "default",
  striped = false,
  bordered = false,
  enableGlobalSearch = true,
  enableColumnFilters = true,
  enableColumnVisibility = true,
  enableExport = false,
  enableRefresh = false,
  onRefresh,
  onExport
}: EnhancedDataTableProps<T>) {
  const [columnVisibility, setColumnVisibility] = React.useState<Record<string, boolean>>(
    columns.reduce((acc, col) => ({ ...acc, [col.id]: !col.hidden }), {})
  )
  const [activeFilters, setActiveFilters] = React.useState<TableFilter[]>(filters)

  // Get visible columns
  const visibleColumns = React.useMemo(() => 
    columns.filter(col => columnVisibility[col.id] !== false),
    [columns, columnVisibility]
  )

  // Handle sorting
  const handleSort = (columnId: string) => {
    if (!onSortingChange) return
    
    const existingSort = sorting.find(s => s.id === columnId)
    let newSorting: TableSort[]
    
    if (!existingSort) {
      newSorting = [{ id: columnId, desc: false }]
    } else if (!existingSort.desc) {
      newSorting = [{ id: columnId, desc: true }]
    } else {
      newSorting = []
    }
    
    onSortingChange(newSorting)
  }

  // Get sort icon
  const getSortIcon = (columnId: string) => {
    const sort = sorting.find(s => s.id === columnId)
    if (!sort) return <ArrowUpDown className="h-3 w-3 text-muted-foreground" />
    return sort.desc 
      ? <ArrowDown className="h-3 w-3 text-primary" />
      : <ArrowUp className="h-3 w-3 text-primary" />
  }

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return
    
    const newSelection: TableSelection = {
      selectedRows: checked ? new Set(data.map((_, index) => index.toString())) : new Set(),
      isAllSelected: checked,
      isIndeterminate: false
    }
    onSelectionChange(newSelection)
  }

  const handleSelectRow = (rowIndex: number, checked: boolean) => {
    if (!onSelectionChange || !selection) return
    
    const newSelectedRows = new Set(selection.selectedRows)
    const rowId = rowIndex.toString()
    
    if (checked) {
      newSelectedRows.add(rowId)
    } else {
      newSelectedRows.delete(rowId)
    }
    
    const isAllSelected = newSelectedRows.size === data.length
    const isIndeterminate = newSelectedRows.size > 0 && newSelectedRows.size < data.length
    
    onSelectionChange({
      selectedRows: newSelectedRows,
      isAllSelected,
      isIndeterminate
    })
  }

  // Handle pagination
  const handlePageChange = (newPageIndex: number) => {
    if (!onPaginationChange || !pagination) return
    onPaginationChange({ ...pagination, pageIndex: newPageIndex })
  }

  const handlePageSizeChange = (newPageSize: number) => {
    if (!onPaginationChange || !pagination) return
    onPaginationChange({ pageIndex: 0, pageSize: newPageSize })
  }

  // Calculate pagination info
  const totalPages = totalCount ? Math.ceil(totalCount / (pagination?.pageSize || 10)) : 0
  const currentPage = (pagination?.pageIndex || 0) + 1
  const startRow = (pagination?.pageIndex || 0) * (pagination?.pageSize || 10) + 1
  const endRow = Math.min(startRow + (pagination?.pageSize || 10) - 1, totalCount || data.length)

  // Get selected rows data
  const selectedRowsData = React.useMemo(() => {
    if (!selection) return []
    return data.filter((_, index) => selection.selectedRows.has(index.toString()))
  }, [data, selection])

  // Variant classes
  const variantClasses = {
    default: "text-sm",
    compact: "text-xs",
    comfortable: "text-base"
  }

  const cellPaddingClasses = {
    default: "p-4",
    compact: "p-2",
    comfortable: "p-6"
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      {(title || description) && (
        <div>
          {title && <h2 className="heading-2 text-foreground">{title}</h2>}
          {description && <p className="body-small text-muted-foreground mt-1">{description}</p>}
        </div>
      )}

      {/* Toolbar */}
      <div className="flex items-center justify-between gap-4 flex-wrap">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {/* Global Search */}
          {enableGlobalSearch && (
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={searchPlaceholder}
                value={globalFilter}
                onChange={(e) => onGlobalFilterChange?.(e.target.value)}
                className="pl-10"
              />
            </div>
          )}

          {/* Active Filters */}
          {activeFilters.length > 0 && (
            <div className="flex items-center gap-2 flex-wrap">
              {activeFilters.map((filter, index) => (
                <Badge key={index} variant="secondary" className="gap-1">
                  {filter.id}: {filter.value}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => {
                      const newFilters = activeFilters.filter((_, i) => i !== index)
                      setActiveFilters(newFilters)
                      onFiltersChange?.(newFilters)
                    }}
                  />
                </Badge>
              ))}
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* Bulk Actions */}
          {enableSelection && selection && selection.selectedRows.size > 0 && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  Actions ({selection.selectedRows.size})
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {bulkActions.map((action, index) => {
                  const Icon = action.icon
                  return (
                    <DropdownMenuItem
                      key={index}
                      onClick={() => action.onClick(selectedRowsData)}
                      className={action.variant === "destructive" ? "text-destructive" : ""}
                    >
                      {Icon && <Icon className="h-4 w-4 mr-2" />}
                      {action.label}
                    </DropdownMenuItem>
                  )
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Column Filters */}
          {enableColumnFilters && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>Filter Columns</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {columns.filter(col => col.filterable).map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    checked={activeFilters.some(f => f.id === column.id)}
                    onCheckedChange={(checked: boolean) => {
                      // Add filter logic here
                    }}
                  >
                    {column.header}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Column Visibility */}
          {enableColumnVisibility && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Columns
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>Toggle Columns</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {columns.map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    checked={columnVisibility[column.id] !== false}
                    onCheckedChange={(checked: boolean) => {
                      setColumnVisibility(prev => ({
                        ...prev,
                        [column.id]: checked
                      }))
                    }}
                  >
                    {column.header}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Export */}
          {enableExport && onExport && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onExport("csv")}>
                  Export as CSV
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onExport("excel")}>
                  Export as Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Refresh */}
          {enableRefresh && onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh} disabled={loading}>
              <RefreshCw className={cn("h-4 w-4", loading && "animate-spin")} />
            </Button>
          )}
        </div>
      </div>

      {/* Table */}
      <Card className={cn(bordered && "border")}>
        <CardContent className="p-0">
          {error ? (
            <div className="p-8 text-center">
              <p className="text-destructive">{error}</p>
            </div>
          ) : (
            <div className="relative overflow-auto">
              <Table className={cn(variantClasses[variant])}>
                <TableHeader>
                  <TableRow className={cn(bordered && "border-b")}>
                    {/* Selection Column */}
                    {enableSelection && (
                      <TableHead className={cellPaddingClasses[variant]}>
                        <Checkbox
                          checked={selection?.isAllSelected || false}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                    )}
                    
                    {/* Data Columns */}
                    {visibleColumns.map((column) => (
                      <TableHead
                        key={column.id}
                        className={cn(
                          cellPaddingClasses[variant],
                          column.sortable && "cursor-pointer hover:bg-accent/50 transition-colors",
                          column.align === "center" && "text-center",
                          column.align === "right" && "text-right",
                          column.sticky && "sticky left-0 bg-background"
                        )}
                        style={{
                          width: column.width,
                          minWidth: column.minWidth,
                          maxWidth: column.maxWidth
                        }}
                        onClick={() => column.sortable && handleSort(column.id)}
                      >
                        <div className="flex items-center gap-2">
                          {column.header}
                          {column.sortable && getSortIcon(column.id)}
                        </div>
                      </TableHead>
                    ))}
                    
                    {/* Actions Column */}
                    {rowActions.length > 0 && (
                      <TableHead className={cellPaddingClasses[variant]}>
                        Actions
                      </TableHead>
                    )}
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell 
                        colSpan={visibleColumns.length + (enableSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                        className="text-center py-8"
                      >
                        <div className="flex items-center justify-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : data.length === 0 ? (
                    <TableRow>
                      <TableCell 
                        colSpan={visibleColumns.length + (enableSelection ? 1 : 0) + (rowActions.length > 0 ? 1 : 0)}
                        className="text-center py-8 text-muted-foreground"
                      >
                        {emptyMessage}
                      </TableCell>
                    </TableRow>
                  ) : (
                    data.map((row, rowIndex) => (
                      <TableRow
                        key={rowIndex}
                        className={cn(
                          "hover:bg-accent/30 transition-colors",
                          striped && rowIndex % 2 === 1 && "bg-muted/20",
                          bordered && "border-b",
                          onRowClick && "cursor-pointer"
                        )}
                        onClick={() => onRowClick?.(row)}
                        onDoubleClick={() => onRowDoubleClick?.(row)}
                      >
                        {/* Selection Cell */}
                        {enableSelection && (
                          <TableCell className={cellPaddingClasses[variant]}>
                            <Checkbox
                              checked={selection?.selectedRows.has(rowIndex.toString()) || false}
                              onCheckedChange={(checked: boolean) => handleSelectRow(rowIndex, checked)}
                            />
                          </TableCell>
                        )}
                        
                        {/* Data Cells */}
                        {visibleColumns.map((column) => (
                          <TableCell
                            key={column.id}
                            className={cn(
                              cellPaddingClasses[variant],
                              column.align === "center" && "text-center",
                              column.align === "right" && "text-right",
                              column.sticky && "sticky left-0 bg-background"
                            )}
                            style={{
                              width: column.width,
                              minWidth: column.minWidth,
                              maxWidth: column.maxWidth
                            }}
                          >
                            {column.cell 
                              ? column.cell(row)
                              : column.accessorKey 
                                ? row[column.accessorKey]
                                : null
                            }
                          </TableCell>
                        ))}
                        
                        {/* Actions Cell */}
                        {rowActions.length > 0 && (
                          <TableCell className={cellPaddingClasses[variant]}>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent>
                                {rowActions.map((action, actionIndex) => {
                                  const Icon = action.icon
                                  const isDisabled = action.disabled?.(row)
                                  return (
                                    <DropdownMenuItem
                                      key={actionIndex}
                                      onClick={() => !isDisabled && action.onClick(row)}
                                      disabled={isDisabled}
                                      className={action.variant === "destructive" ? "text-destructive" : ""}
                                    >
                                      {Icon && <Icon className="h-4 w-4 mr-2" />}
                                      {action.label}
                                    </DropdownMenuItem>
                                  )
                                })}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        )}
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination && totalCount && totalCount > 0 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>
              Showing {startRow} to {endRow} of {totalCount} results
            </span>
            {enableSelection && selection && selection.selectedRows.size > 0 && (
              <span>
                ({selection.selectedRows.size} selected)
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Rows per page:</span>
              <Select
                value={pagination.pageSize.toString()}
                onValueChange={(value) => handlePageSizeChange(Number(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {pageSizeOptions.map((size) => (
                    <SelectItem key={size} value={size.toString()}>
                      {size}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(0)}
                disabled={currentPage === 1}
              >
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.pageIndex - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <span className="text-sm text-muted-foreground px-2">
                Page {currentPage} of {totalPages}
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.pageIndex + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(totalPages - 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
