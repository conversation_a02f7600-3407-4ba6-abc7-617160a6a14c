<testsuites id="" name="" tests="5" failures="1" skipped="0" errors="0" time="38.252216999999995">
<testsuite name="smoke.spec.ts" timestamp="2025-07-12T13:16:28.745Z" hostname="chromium" tests="5" failures="1" skipped="0" time="44.181" errors="0">
<testcase name="Smoke Tests - Basic Functionality › should load homepage without errors" classname="smoke.spec.ts" time="3.041">
</testcase>
<testcase name="Smoke Tests - Basic Functionality › should navigate to login page" classname="smoke.spec.ts" time="8.931">
<failure message="smoke.spec.ts:30:7 should navigate to login page" type="FAILURE">
<![CDATA[  [chromium] › smoke.spec.ts:30:7 › Smoke Tests - Basic Functionality › should navigate to login page 

    Error: Timed out 5000ms waiting for expect(locator).toContainText(expected)

    Locator: locator('h1, h2')
    Expected pattern: /login|sign in/i
    Received: <element(s) not found>
    Call log:
      - Expect "toContainText" with timeout 5000ms
      - waiting for locator('h1, h2')


      30 |   test('should navigate to login page', async ({ page }) => {
      31 |     await page.goto('/login');
    > 32 |     await expect(page.locator('h1, h2')).toContainText(/login|sign in/i);
         |                                          ^
      33 |     
      34 |     // Check for login form elements
      35 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
        at C:\Users\<USER>\Desktop\crmnew-main\__tests__\smoke.spec.ts:32:42

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\test-results\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\test-results\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\test-failed-1.png]]

[[ATTACHMENT|smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\video.webm]]

[[ATTACHMENT|smoke-Smoke-Tests---Basic--d077f-ould-navigate-to-login-page-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Smoke Tests - Basic Functionality › should attempt login with test credentials" classname="smoke.spec.ts" time="9.334">
<system-out>
<![CDATA[Current URL after login attempt: http://localhost:3000/login
Page content after login: Welcome to Nawras CRMEmailPasswordInvalid email or passwordSign InOr continue withGoogleGitHubالعربية$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChi
]]>
</system-out>
</testcase>
<testcase name="Smoke Tests - Basic Functionality › should check dashboard accessibility (if logged in)" classname="smoke.spec.ts" time="5.585">
<system-out>
<![CDATA[Dashboard access URL: http://localhost:3000/login?callbackUrl=https%3A%2F%2Fsales.nawrasinchina.com%2Fdashboard
Dashboard requires authentication - redirected to login
]]>
</system-out>
</testcase>
<testcase name="Smoke Tests - Basic Functionality › should check for 404 errors on main routes" classname="smoke.spec.ts" time="17.29">
<system-out>
<![CDATA[Route /dashboard/customers: Status 200 - Accessible
Route /dashboard/deals: Status 200 - Accessible
Route /dashboard/leads: Status 200 - Accessible
Route /dashboard/opportunities: Status 200 - Accessible
Route /dashboard/companies: Status 200 - Accessible
Route /dashboard/tasks: Status 200 - Accessible
Route /dashboard/reports: Status 200 - Accessible
Route /dashboard/settings: Status 200 - Accessible
Route /dashboard/admin: Status 200 - Accessible
Route accessibility: 9/9 routes accessible
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>