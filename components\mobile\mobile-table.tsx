"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  MoreVertical, ChevronRight, Filter, Search, SortAsc, 
  SortDesc, Grid, List, Eye, Edit, Trash2
} from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { SwipeableCard, TouchButton } from "./touch-interactions"

// Mobile Table Types
export interface MobileTableColumn<T = any> {
  id: string
  label: string
  accessor?: keyof T
  render?: (item: T) => React.ReactNode
  sortable?: boolean
  width?: "auto" | "sm" | "md" | "lg"
  align?: "left" | "center" | "right"
  priority?: "high" | "medium" | "low" // For responsive hiding
}

export interface MobileTableAction<T = any> {
  label: string
  icon?: React.ComponentType<{ className?: string }>
  onClick: (item: T) => void
  variant?: "default" | "destructive"
  color?: string
}

export interface MobileTableProps<T = any> {
  data: T[]
  columns: MobileTableColumn<T>[]
  keyExtractor: (item: T) => string
  title?: string
  searchable?: boolean
  sortable?: boolean
  filterable?: boolean
  actions?: MobileTableAction<T>[]
  onItemClick?: (item: T) => void
  onRefresh?: () => Promise<void>
  loading?: boolean
  emptyMessage?: string
  className?: string
  variant?: "card" | "list" | "grid"
}

// Mobile Table Card View
function MobileTableCard<T>({
  item,
  columns,
  actions = [],
  onItemClick,
  keyExtractor
}: {
  item: T
  columns: MobileTableColumn<T>[]
  actions?: MobileTableAction<T>[]
  onItemClick?: (item: T) => void
  keyExtractor: (item: T) => string
}) {
  const primaryColumns = columns.filter(col => col.priority === "high").slice(0, 2)
  const secondaryColumns = columns.filter(col => col.priority === "medium").slice(0, 3)
  const tertiaryColumns = columns.filter(col => col.priority === "low")

  const renderColumnValue = (column: MobileTableColumn<T>): React.ReactNode => {
    if (column.render) {
      return column.render(item)
    }
    if (column.accessor) {
      const value = item[column.accessor]
      return value != null ? String(value) : null
    }
    return null
  }

  const leftAction = actions.find(action => action.variant === "destructive")
  const rightAction = actions.find(action => action.variant !== "destructive")

  return (
    <SwipeableCard
      onSwipeLeft={leftAction ? () => leftAction.onClick(item) : undefined}
      onSwipeRight={rightAction ? () => rightAction.onClick(item) : undefined}
      leftAction={leftAction ? {
        icon: leftAction.icon ? <leftAction.icon className="h-5 w-5" /> : <Trash2 className="h-5 w-5" />,
        label: leftAction.label,
        color: "red"
      } : undefined}
      rightAction={rightAction ? {
        icon: rightAction.icon ? <rightAction.icon className="h-5 w-5" /> : <Edit className="h-5 w-5" />,
        label: rightAction.label,
        color: "green"
      } : undefined}
    >
      <div
        className="cursor-pointer"
        onClick={() => onItemClick?.(item)}
      >
        {/* Primary Information */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            {primaryColumns.map((column) => (
              <div key={column.id} className="mb-1">
                {column.id === primaryColumns[0].id ? (
                  <h3 className="font-semibold text-foreground truncate">
                    {renderColumnValue(column)}
                  </h3>
                ) : (
                  <p className="text-sm text-muted-foreground truncate">
                    {renderColumnValue(column)}
                  </p>
                )}
              </div>
            ))}
          </div>
          
          {actions.length > 0 && (
            <Button variant="ghost" size="sm" className="ml-2">
              <MoreVertical className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Secondary Information */}
        {secondaryColumns.length > 0 && (
          <div className="grid grid-cols-2 gap-2 mb-3">
            {secondaryColumns.map((column) => (
              <div key={column.id} className="min-w-0">
                <p className="text-xs text-muted-foreground truncate">
                  {column.label}
                </p>
                <p className="text-sm font-medium truncate">
                  {renderColumnValue(column)}
                </p>
              </div>
            ))}
          </div>
        )}

        {/* Tertiary Information */}
        {tertiaryColumns.length > 0 && (
          <>
            <Separator className="my-2" />
            <div className="flex flex-wrap gap-2">
              {tertiaryColumns.map((column) => (
                <div key={column.id} className="flex items-center gap-1">
                  <span className="text-xs text-muted-foreground">
                    {column.label}:
                  </span>
                  <span className="text-xs">
                    {renderColumnValue(column)}
                  </span>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Navigation Indicator */}
        <div className="flex justify-end mt-2">
          <ChevronRight className="h-4 w-4 text-muted-foreground" />
        </div>
      </div>
    </SwipeableCard>
  )
}

// Mobile Table List View
function MobileTableList<T>({
  item,
  columns,
  actions = [],
  onItemClick,
  keyExtractor
}: {
  item: T
  columns: MobileTableColumn<T>[]
  actions?: MobileTableAction<T>[]
  onItemClick?: (item: T) => void
  keyExtractor: (item: T) => string
}) {
  const primaryColumn = columns.find(col => col.priority === "high")
  const secondaryColumn = columns.find(col => col.priority === "medium")

  const renderColumnValue = (column: MobileTableColumn<T>): React.ReactNode => {
    if (column.render) {
      return column.render(item)
    }
    if (column.accessor) {
      const value = item[column.accessor]
      return value != null ? String(value) : null
    }
    return null
  }

  return (
    <div
      className="flex items-center gap-3 p-3 hover:bg-accent/50 cursor-pointer"
      onClick={() => onItemClick?.(item)}
    >
      {/* Avatar/Icon */}
      <Avatar className="h-10 w-10 flex-shrink-0">
        <AvatarFallback className="text-xs">
          {primaryColumn ? String(renderColumnValue(primaryColumn)).slice(0, 2).toUpperCase() : "??"}
        </AvatarFallback>
      </Avatar>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <p className="font-medium truncate">
          {primaryColumn ? renderColumnValue(primaryColumn) : "Unknown"}
        </p>
        {secondaryColumn && (
          <p className="text-sm text-muted-foreground truncate">
            {renderColumnValue(secondaryColumn)}
          </p>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center gap-1">
        {actions.slice(0, 2).map((action, index) => (
          <TouchButton
            key={index}
            variant="ghost"
            size="sm"
            onPress={() => action.onClick(item)}
            className="h-8 w-8 p-0"
          >
            {action.icon ? (
              <action.icon className="h-4 w-4" />
            ) : (
              <MoreVertical className="h-4 w-4" />
            )}
          </TouchButton>
        ))}
        <ChevronRight className="h-4 w-4 text-muted-foreground" />
      </div>
    </div>
  )
}

// Mobile Table Grid View
function MobileTableGrid<T>({
  item,
  columns,
  actions = [],
  onItemClick,
  keyExtractor
}: {
  item: T
  columns: MobileTableColumn<T>[]
  actions?: MobileTableAction<T>[]
  onItemClick?: (item: T) => void
  keyExtractor: (item: T) => string
}) {
  const primaryColumn = columns.find(col => col.priority === "high")
  const secondaryColumns = columns.filter(col => col.priority === "medium").slice(0, 2)

  const renderColumnValue = (column: MobileTableColumn<T>): React.ReactNode => {
    if (column.render) {
      return column.render(item)
    }
    if (column.accessor) {
      const value = item[column.accessor]
      return value != null ? String(value) : null
    }
    return null
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardContent 
        className="p-4 text-center"
        onClick={() => onItemClick?.(item)}
      >
        {/* Avatar/Icon */}
        <Avatar className="h-12 w-12 mx-auto mb-3">
          <AvatarFallback>
            {primaryColumn ? String(renderColumnValue(primaryColumn)).slice(0, 2).toUpperCase() : "??"}
          </AvatarFallback>
        </Avatar>

        {/* Primary Info */}
        <h4 className="font-medium truncate mb-1">
          {primaryColumn ? renderColumnValue(primaryColumn) : "Unknown"}
        </h4>

        {/* Secondary Info */}
        {secondaryColumns.map((column) => (
          <p key={column.id} className="text-xs text-muted-foreground truncate">
            {renderColumnValue(column)}
          </p>
        ))}

        {/* Actions */}
        {actions.length > 0 && (
          <div className="flex justify-center gap-1 mt-3">
            {actions.slice(0, 3).map((action, index) => (
              <TouchButton
                key={index}
                variant="outline"
                size="sm"
                onPress={() => action.onClick(item)}
                className="h-8 w-8 p-0"
              >
                {action.icon ? (
                  <action.icon className="h-3 w-3" />
                ) : (
                  <MoreVertical className="h-3 w-3" />
                )}
              </TouchButton>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Main Mobile Table Component
export function MobileTable<T>({
  data,
  columns,
  keyExtractor,
  title,
  searchable = true,
  sortable = true,
  filterable = true,
  actions = [],
  onItemClick,
  onRefresh,
  loading = false,
  emptyMessage = "No data available",
  className,
  variant = "card"
}: MobileTableProps<T>) {
  const isMobile = useIsMobile()
  const [searchQuery, setSearchQuery] = React.useState("")
  const [sortColumn, setSortColumn] = React.useState<string | null>(null)
  const [sortDirection, setSortDirection] = React.useState<"asc" | "desc">("asc")
  const [viewMode, setViewMode] = React.useState<"card" | "list" | "grid">(variant)

  // Filter and sort data
  const processedData = React.useMemo(() => {
    let filtered = data

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(item => {
        return columns.some(column => {
          if (column.accessor) {
            const value = String(item[column.accessor]).toLowerCase()
            return value.includes(searchQuery.toLowerCase())
          }
          return false
        })
      })
    }

    // Apply sorting
    if (sortColumn) {
      const column = columns.find(col => col.id === sortColumn)
      if (column && column.accessor) {
        filtered = [...filtered].sort((a, b) => {
          const aValue = a[column.accessor!]
          const bValue = b[column.accessor!]
          
          if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
          if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
          return 0
        })
      }
    }

    return filtered
  }, [data, searchQuery, sortColumn, sortDirection, columns])

  const handleSort = (columnId: string) => {
    if (sortColumn === columnId) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(columnId)
      setSortDirection("asc")
    }
  }

  if (!isMobile) {
    return null // Use regular table on desktop
  }

  const renderItem = (item: T) => {
    const key = keyExtractor(item)
    
    switch (viewMode) {
      case "list":
        return (
          <MobileTableList
            key={key}
            item={item}
            columns={columns}
            actions={actions}
            onItemClick={onItemClick}
            keyExtractor={keyExtractor}
          />
        )
      case "grid":
        return (
          <MobileTableGrid
            key={key}
            item={item}
            columns={columns}
            actions={actions}
            onItemClick={onItemClick}
            keyExtractor={keyExtractor}
          />
        )
      default:
        return (
          <MobileTableCard
            key={key}
            item={item}
            columns={columns}
            actions={actions}
            onItemClick={onItemClick}
            keyExtractor={keyExtractor}
          />
        )
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      {title && (
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold">{title}</h2>
          <div className="flex items-center gap-2">
            <TouchButton
              variant="outline"
              size="sm"
              onPress={() => setViewMode(viewMode === "card" ? "list" : viewMode === "list" ? "grid" : "card")}
            >
              {viewMode === "card" ? <List className="h-4 w-4" /> : 
               viewMode === "list" ? <Grid className="h-4 w-4" /> : 
               <List className="h-4 w-4" />}
            </TouchButton>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      {(searchable || sortable || filterable) && (
        <div className="flex gap-2">
          {searchable && (
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-lg text-sm"
              />
            </div>
          )}
          
          {sortable && (
            <TouchButton variant="outline" size="sm">
              {sortDirection === "asc" ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </TouchButton>
          )}
          
          {filterable && (
            <TouchButton variant="outline" size="sm">
              <Filter className="h-4 w-4" />
            </TouchButton>
          )}
        </div>
      )}

      {/* Content */}
      {loading ? (
        <div className="space-y-3">
          {Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-4 bg-muted rounded mb-2" />
                <div className="h-3 bg-muted rounded w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : processedData.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">{emptyMessage}</p>
          </CardContent>
        </Card>
      ) : (
        <div className={cn(
          viewMode === "grid" ? "grid grid-cols-2 gap-3" : "space-y-3"
        )}>
          {processedData.map(renderItem)}
        </div>
      )}
    </div>
  )
}
