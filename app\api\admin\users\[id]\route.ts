import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'
import { authOptions } from '@/lib/auth-config'

// Create server-side Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Helper function to check if user is admin
async function checkAdminAccess() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return { error: 'Unauthorized', status: 401 }
  }

  const userRole = (session.user as any).role
  if (userRole !== 'admin') {
    return { error: 'Admin access required', status: 403 }
  }

  return { user: session.user, role: userRole }
}

// GET - Fetch specific user (admin only)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🔍 [ADMIN-USER-API] Fetching user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const resolvedParams = await params
    const userId = resolvedParams.id

    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, email, full_name, role, department, phone, status, created_at, updated_at')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('❌ [ADMIN-USER-API] Database error:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    console.log('✅ [ADMIN-USER-API] User fetched successfully')
    return NextResponse.json({ data: user })

  } catch (error: any) {
    console.error('❌ [ADMIN-USER-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update specific user (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🔍 [ADMIN-USER-API] Updating user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const resolvedParams = await params
    const userId = resolvedParams.id
    const body = await request.json()

    const { full_name, role, department, phone, status } = body

    const updateData = {
      full_name,
      role,
      department,
      phone,
      status,
      updated_at: new Date().toISOString()
    }

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData]
      }
    })

    const { data: updatedUser, error } = await supabaseAdmin
      .from('users')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      console.error('❌ [ADMIN-USER-API] Error updating user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!updatedUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    console.log('✅ [ADMIN-USER-API] User updated successfully:', updatedUser.email)
    return NextResponse.json({ data: updatedUser })

  } catch (error: any) {
    console.error('❌ [ADMIN-USER-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete specific user (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    console.log('🔍 [ADMIN-USER-API] Deleting user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const resolvedParams = await params
    const userId = resolvedParams.id

    // Prevent admin from deleting themselves
    const currentUserId = (authCheck.user as any).id
    if (userId === currentUserId) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 })
    }

    // Check if user exists first
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('id, email')
      .eq('id', userId)
      .single()

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId)

    if (error) {
      console.error('❌ [ADMIN-USER-API] Error deleting user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ [ADMIN-USER-API] User deleted successfully:', existingUser.email)
    return NextResponse.json({ message: 'User deleted successfully' })

  } catch (error: any) {
    console.error('❌ [ADMIN-USER-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
