import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  CheckCircle, 
  AlertCircle, 
  Info, 
  XCircle, 
  X,
  AlertTriangle
} from "lucide-react"
import { useState, useEffect } from "react"

interface NotificationProps {
  type?: "success" | "error" | "warning" | "info"
  title?: string
  message: string
  duration?: number
  onClose?: () => void
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
  showIcon?: boolean
  closable?: boolean
}

export function Notification({
  type = "info",
  title,
  message,
  duration = 5000,
  onClose,
  action,
  className,
  showIcon = true,
  closable = true
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => onClose?.(), 300) // Wait for animation
      }, duration)

      return () => clearTimeout(timer)
    }
  }, [duration, onClose])

  const handleClose = () => {
    setIsVisible(false)
    setTimeout(() => onClose?.(), 300)
  }

  const getTypeConfig = () => {
    switch (type) {
      case "success":
        return {
          icon: CheckCircle,
          bgColor: "bg-green-50 border-green-200",
          iconColor: "text-green-600",
          titleColor: "text-green-800",
          messageColor: "text-green-700"
        }
      case "error":
        return {
          icon: XCircle,
          bgColor: "bg-red-50 border-red-200",
          iconColor: "text-red-600",
          titleColor: "text-red-800",
          messageColor: "text-red-700"
        }
      case "warning":
        return {
          icon: AlertTriangle,
          bgColor: "bg-yellow-50 border-yellow-200",
          iconColor: "text-yellow-600",
          titleColor: "text-yellow-800",
          messageColor: "text-yellow-700"
        }
      case "info":
      default:
        return {
          icon: Info,
          bgColor: "bg-blue-50 border-blue-200",
          iconColor: "text-blue-600",
          titleColor: "text-blue-800",
          messageColor: "text-blue-700"
        }
    }
  }

  const config = getTypeConfig()
  const Icon = config.icon

  if (!isVisible) return null

  return (
    <div
      className={cn(
        "relative flex items-start gap-3 p-4 border rounded-lg shadow-sm transition-all duration-300",
        config.bgColor,
        isVisible ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-2",
        className
      )}
    >
      {showIcon && (
        <Icon className={cn("h-5 w-5 mt-0.5 flex-shrink-0", config.iconColor)} />
      )}
      
      <div className="flex-1 min-w-0">
        {title && (
          <h4 className={cn("text-sm font-medium mb-1", config.titleColor)}>
            {title}
          </h4>
        )}
        <p className={cn("text-sm", config.messageColor)}>
          {message}
        </p>
        
        {action && (
          <div className="mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={action.onClick}
              className="text-xs"
            >
              {action.label}
            </Button>
          </div>
        )}
      </div>

      {closable && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-6 w-6 p-0 hover:bg-black/5"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
    </div>
  )
}

// Notification container for managing multiple notifications
export function NotificationContainer({ 
  notifications, 
  position = "top-right",
  className 
}: {
  notifications: Array<NotificationProps & { id: string }>
  position?: "top-right" | "top-left" | "bottom-right" | "bottom-left" | "top-center" | "bottom-center"
  className?: string
}) {
  const getPositionClasses = () => {
    switch (position) {
      case "top-left":
        return "top-4 left-4"
      case "top-center":
        return "top-4 left-1/2 transform -translate-x-1/2"
      case "top-right":
        return "top-4 right-4"
      case "bottom-left":
        return "bottom-4 left-4"
      case "bottom-center":
        return "bottom-4 left-1/2 transform -translate-x-1/2"
      case "bottom-right":
        return "bottom-4 right-4"
      default:
        return "top-4 right-4"
    }
  }

  return (
    <div
      className={cn(
        "fixed z-50 flex flex-col gap-2 max-w-sm w-full",
        getPositionClasses(),
        className
      )}
    >
      {notifications.map((notification) => (
        <Notification key={notification.id} {...notification} />
      ))}
    </div>
  )
}

// Hook for managing notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<Array<NotificationProps & { id: string }>>([])

  const addNotification = (notification: NotificationProps) => {
    const id = Math.random().toString(36).substr(2, 9)
    setNotifications(prev => [...prev, { ...notification, id }])
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAll = () => {
    setNotifications([])
  }

  // Convenience methods
  const success = (message: string, options?: Partial<NotificationProps>) => {
    addNotification({ ...options, type: "success", message })
  }

  const error = (message: string, options?: Partial<NotificationProps>) => {
    addNotification({ ...options, type: "error", message })
  }

  const warning = (message: string, options?: Partial<NotificationProps>) => {
    addNotification({ ...options, type: "warning", message })
  }

  const info = (message: string, options?: Partial<NotificationProps>) => {
    addNotification({ ...options, type: "info", message })
  }

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  }
}

// Specialized notification components
export function SuccessNotification(props: Omit<NotificationProps, "type">) {
  return <Notification {...props} type="success" />
}

export function ErrorNotification(props: Omit<NotificationProps, "type">) {
  return <Notification {...props} type="error" />
}

export function WarningNotification(props: Omit<NotificationProps, "type">) {
  return <Notification {...props} type="warning" />
}

export function InfoNotification(props: Omit<NotificationProps, "type">) {
  return <Notification {...props} type="info" />
}
