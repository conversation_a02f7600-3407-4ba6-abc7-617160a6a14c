import { NextAuthOptions } from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import GitHubProvider from "next-auth/providers/github"
import CredentialsProvider from "next-auth/providers/credentials"
import { createClient } from '@supabase/supabase-js'

// Create server-side Supabase client for authentication
const supabaseAuth = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          console.log("❌ [AUTH] Missing credentials")
          return null
        }

        try {
          console.log("🔍 [AUTH] Attempting to authenticate user:", credentials.email)

          // Query users table directly using service role
          const { data: users, error } = await supabaseAuth
            .from('users')
            .select('*')
            .eq('email', credentials.email)
            .limit(1)

          if (error) {
            console.error("❌ [AUTH] Database error:", error)
            return null
          }

          if (!users || users.length === 0) {
            console.log("❌ [AUTH] User not found:", credentials.email)
            return null
          }

          const user = users[0]
          console.log("🔍 [AUTH] Found user:", { id: user.id, email: user.email, role: user.role })

          // Simple password check (in production, use proper hashing)
          if (user.password !== credentials.password) {
            console.log("❌ [AUTH] Invalid password for user:", credentials.email)
            return null
          }

          console.log("✅ [AUTH] Authentication successful for user:", credentials.email)

          return {
            id: user.id,
            email: user.email,
            name: user.name || user.email,
            role: user.role || 'user',
            image: user.avatar_url || null,
          }
        } catch (error) {
          console.error("❌ [AUTH] Authentication error:", error)
          return null
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.role = (user as any).role
      }
      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        ;(session.user as any).id = token.id as string
        ;(session.user as any).role = token.role
      }
      return session
    },
  },
  pages: {
    signIn: '/login',
    error: '/login',
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
}
