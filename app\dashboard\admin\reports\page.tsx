"use client"

import React, { useState, useEffect, useMemo } from "react"
import { AdminGuard } from "@/components/admin-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Target, 
  Download, 
  RefreshCw,
  PieChart,
  Activity,
  Calendar,
  Building2,
  UserCheck,
  FileText,
  Database
} from "lucide-react"
import { useOptimizedData } from "@/hooks/use-optimized-data"
import { useAuth } from "@/components/auth-provider"
import { format, subDays, startOfMonth, endOfMonth } from "date-fns"

interface ReportMetrics {
  totalRevenue: number
  totalDeals: number
  conversionRate: number
  winRate: number
  averageDealValue: number
  totalCustomers: number
  totalUsers: number
  totalCompanies: number
}

export default function AdminReports() {
  const { user } = useAuth()
  const [dateRange, setDateRange] = useState("30")
  const [reportType, setReportType] = useState("overview")
  const [loading, setLoading] = useState(true)
  const [metrics, setMetrics] = useState<ReportMetrics>({
    totalRevenue: 0,
    totalDeals: 0,
    conversionRate: 0,
    winRate: 0,
    averageDealValue: 0,
    totalCustomers: 0,
    totalUsers: 0,
    totalCompanies: 0
  })

  // Fetch data for reports (using opportunities instead of deals)
  const { data: customers } = useOptimizedData({
    table: 'customers',
    select: 'id, status, created_at, user_id',
    orderBy: { column: 'created_at', ascending: false }
  })

  const { data: users } = useOptimizedData({
    table: 'users',
    select: 'id, role, created_at, email',
    orderBy: { column: 'created_at', ascending: false }
  })

  const { data: companies } = useOptimizedData({
    table: 'companies',
    select: 'id, status, created_at, user_id',
    orderBy: { column: 'created_at', ascending: false }
  })

  const { data: opportunities } = useOptimizedData({
    table: 'opportunities',
    select: 'id, value, stage, probability, created_at, user_id',
    orderBy: { column: 'created_at', ascending: false }
  })

  // Calculate date range
  const getDateRange = () => {
    const now = new Date()
    switch (dateRange) {
      case "7":
        return { start: subDays(now, 7), end: now }
      case "30":
        return { start: subDays(now, 30), end: now }
      case "90":
        return { start: subDays(now, 90), end: now }
      case "month":
        return { start: startOfMonth(now), end: endOfMonth(now) }
      default:
        return { start: subDays(now, 30), end: now }
    }
  }

  // Filter data by date range
  const filteredData = useMemo(() => {
    const { start, end } = getDateRange()

    return {
      opportunities: opportunities?.filter(item => {
        const itemDate = new Date(item.created_at)
        return itemDate >= start && itemDate <= end
      }) || [],
      customers: customers?.filter(item => {
        const itemDate = new Date(item.created_at)
        return itemDate >= start && itemDate <= end
      }) || [],
      companies: companies?.filter(item => {
        const itemDate = new Date(item.created_at)
        return itemDate >= start && itemDate <= end
      }) || []
    }
  }, [opportunities, customers, companies, dateRange])

  // Calculate metrics (updated to use opportunities instead of deals)
  useEffect(() => {
    if (opportunities && customers && users && companies) {
      const wonOpportunities = filteredData.opportunities.filter(opp => opp.stage === 'closed_won')
      const lostOpportunities = filteredData.opportunities.filter(opp => opp.stage === 'closed_lost')
      const totalRevenue = wonOpportunities.reduce((sum, opp) => sum + (opp.value || 0), 0)
      const totalOpportunitiesValue = filteredData.opportunities.reduce((sum, opp) => sum + (opp.value || 0), 0)

      setMetrics({
        totalRevenue,
        totalDeals: filteredData.opportunities.length, // Keep the same property name for compatibility
        conversionRate: filteredData.opportunities.length > 0 ? (wonOpportunities.length / filteredData.opportunities.length) * 100 : 0,
        winRate: (wonOpportunities.length + lostOpportunities.length) > 0 ? (wonOpportunities.length / (wonOpportunities.length + lostOpportunities.length)) * 100 : 0,
        averageDealValue: filteredData.opportunities.length > 0 ? totalOpportunitiesValue / filteredData.opportunities.length : 0,
        totalCustomers: filteredData.customers.length,
        totalUsers: users.length,
        totalCompanies: filteredData.companies.length
      })
      setLoading(false)
    }
  }, [filteredData, opportunities, customers, users, companies])

  // Export functionality
  const handleExport = (format: 'csv' | 'pdf') => {
    // Implementation for export functionality
    console.log(`Exporting report as ${format}`)
  }

  const salesMetrics = [
    {
      title: "Total Revenue",
      value: `$${metrics.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      change: "+12.5%",
      changeType: "positive" as const
    },
    {
      title: "Conversion Rate",
      value: `${metrics.conversionRate.toFixed(1)}%`,
      icon: TrendingUp,
      change: "+2.1%",
      changeType: "positive" as const
    },
    {
      title: "Win Rate",
      value: `${metrics.winRate.toFixed(1)}%`,
      icon: Target,
      change: "+5.3%",
      changeType: "positive" as const
    },
    {
      title: "Avg Deal Value",
      value: `$${metrics.averageDealValue.toLocaleString()}`,
      icon: BarChart3,
      change: "-1.2%",
      changeType: "negative" as const
    }
  ]

  const userMetrics = [
    {
      title: "Total Users",
      value: metrics.totalUsers,
      icon: Users,
      description: "System users"
    },
    {
      title: "New Customers",
      value: metrics.totalCustomers,
      icon: UserCheck,
      description: "In selected period"
    },
    {
      title: "Companies",
      value: metrics.totalCompanies,
      icon: Building2,
      description: "Active companies"
    },
    {
      title: "Active Opportunities",
      value: metrics.totalDeals,
      icon: FileText,
      description: "In pipeline"
    }
  ]

  return (
    <AdminGuard>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">System Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive analytics and insights for system performance
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-[180px]">
                <Calendar className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="month">This month</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" size="sm" onClick={() => handleExport('csv')}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Report Tabs */}
        <Tabs value={reportType} onValueChange={setReportType} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sales">Sales Analytics</TabsTrigger>
            <TabsTrigger value="users">User Analytics</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Sales Metrics */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Sales Analytics</h3>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {salesMetrics.map((metric, index) => (
                  <Card key={index}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                      <metric.icon className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{metric.value}</div>
                      <p className="text-xs text-muted-foreground">
                        <span className={metric.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                          {metric.change}
                        </span>
                        {" "}from last period
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* User Metrics */}
            <div>
              <h3 className="text-lg font-semibold mb-4">User Analytics</h3>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {userMetrics.map((metric, index) => (
                  <Card key={index}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
                      <metric.icon className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{metric.value}</div>
                      <p className="text-xs text-muted-foreground">{metric.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sales" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Opportunity Status Distribution</CardTitle>
                  <CardDescription>Breakdown of opportunities by current stage</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['Open', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost'].map((stage) => {
                      const count = filteredData.opportunities.filter(opp => opp.stage === stage).length
                      const percentage = filteredData.opportunities.length > 0 ? (count / filteredData.opportunities.length) * 100 : 0
                      return (
                        <div key={stage} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary" />
                            <span className="text-sm">{stage}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{count}</span>
                            <span className="text-xs text-muted-foreground">({percentage.toFixed(1)}%)</span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Revenue Trends</CardTitle>
                  <CardDescription>Revenue performance over time</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <PieChart className="h-12 w-12 mx-auto mb-4" />
                    <p>Chart visualization would be displayed here</p>
                    <p className="text-sm">Total Revenue: ${metrics.totalRevenue.toLocaleString()}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="users" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>User Role Distribution</CardTitle>
                  <CardDescription>Breakdown of users by role</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {['admin', 'manager', 'user'].map((role) => {
                      const count = users?.filter(user => user.role === role).length || 0
                      const percentage = users && users.length > 0 ? (count / users.length) * 100 : 0
                      return (
                        <div key={role} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-primary" />
                            <span className="text-sm capitalize">{role}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{count}</span>
                            <span className="text-xs text-muted-foreground">({percentage.toFixed(1)}%)</span>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Customer Source Distribution</CardTitle>
                  <CardDescription>Where customers are coming from</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4" />
                    <p>Customer source analytics would be displayed here</p>
                    <p className="text-sm">Total Customers: {metrics.totalCustomers}</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>System and business performance indicators</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    <div className="text-center p-4 border rounded-lg">
                      <Database className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                      <div className="text-2xl font-bold">{(opportunities?.length || 0) + (customers?.length || 0)}</div>
                      <div className="text-sm text-muted-foreground">Total Records</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <Activity className="h-8 w-8 mx-auto mb-2 text-green-500" />
                      <div className="text-2xl font-bold">98.5%</div>
                      <div className="text-sm text-muted-foreground">System Uptime</div>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                      <div className="text-2xl font-bold">{metrics.conversionRate.toFixed(1)}%</div>
                      <div className="text-sm text-muted-foreground">Conversion Rate</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminGuard>
  )
}
