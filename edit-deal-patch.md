# Edit Deal Functionality Patch

This file contains the missing Edit Deal functionality that needs to be added to the deals page.

## Missing handleUpdateDeal Function

Add this function after the resetForm function (around line 220):

```typescript
// Handle update deal
const handleUpdateDeal = async () => {
  if (!formData.title.trim() || !formData.company.trim() || !selectedDeal) {
    toast({
      title: t("Error"),
      description: t("Please fill in all required fields"),
      variant: "destructive",
    })
    return
  }

  setIsSubmitting(true)
  try {
    const processedFormData = { ...formData }
    // Handle empty date fields
    if (processedFormData.expected_close_date === '') {
      processedFormData.expected_close_date = null
    }

    const result = await update(selectedDeal.id, processedFormData, 'deals')
    if (result) {
      setIsEditDealOpen(false)
      setSelectedDeal(null)
      resetForm()
      toast({
        title: t("Success"),
        description: t("Deal updated successfully"),
      })
    }
  } catch (error) {
    console.error("Error updating deal:", error)
    toast({
      title: t("Error"),
      description: t("Failed to update deal"),
      variant: "destructive",
    })
  } finally {
    setIsSubmitting(false)
  }
}
```

## Missing Edit Deal Dialog

Add this dialog before the Delete Confirmation Dialog (around line 650):

```tsx
{/* Edit Deal Dialog */}
<Dialog open={isEditDealOpen} onOpenChange={setIsEditDealOpen}>
  <DialogContent className="sm:max-w-[500px]">
    <DialogHeader>
      <DialogTitle>{t("Edit Deal")}</DialogTitle>
      <DialogDescription>{t("Update deal information in your sales pipeline")}</DialogDescription>
    </DialogHeader>
    <div className="grid gap-4 py-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-title">{t("Deal Title")} *</Label>
          <Input
            id="edit-title"
            value={formData.title}
            onChange={(e) => updateFormField("title", e.target.value)}
            placeholder="Enterprise Contract"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-company">{t("Company")} *</Label>
          <Input
            id="edit-company"
            value={formData.company}
            onChange={(e) => updateFormField("company", e.target.value)}
            placeholder="Company Inc."
            required
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-value">{t("Deal Value")} *</Label>
          <Input
            id="edit-value"
            type="number"
            value={formData.value}
            onChange={(e) => updateFormField("value", Number(e.target.value))}
            placeholder="50000"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-probability">{t("Probability (%)")}</Label>
          <Input
            id="edit-probability"
            type="number"
            min="0"
            max="100"
            value={formData.probability}
            onChange={(e) => updateFormField("probability", Number(e.target.value))}
          />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="edit-stage">{t("Stage")}</Label>
          <Select
            value={formData.stage}
            onValueChange={(value) => updateFormField("stage", value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {defaultColumns.map((col) => (
                <SelectItem key={col.id} value={col.id}>
                  {col.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="edit-expected_close_date">{t("Expected Close Date")}</Label>
          <Input
            id="edit-expected_close_date"
            type="date"
            value={formData.expected_close_date || ""}
            onChange={(e) => updateFormField("expected_close_date", e.target.value)}
          />
        </div>
      </div>
      <div className="space-y-2">
        <Label htmlFor="edit-contact_person">{t("Contact Person")}</Label>
        <Input
          id="edit-contact_person"
          value={formData.contact_person}
          onChange={(e) => updateFormField("contact_person", e.target.value)}
          placeholder="John Doe"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="edit-description">{t("Description")}</Label>
        <Textarea
          id="edit-description"
          value={formData.description}
          onChange={(e) => updateFormField("description", e.target.value)}
          placeholder="Deal details and notes..."
          className="min-h-[80px]"
        />
      </div>
    </div>
    <DialogFooter>
      <Button variant="outline" onClick={() => {
        setIsEditDealOpen(false)
        setSelectedDeal(null)
        resetForm()
      }}>
        {t("Cancel")}
      </Button>
      <Button onClick={handleUpdateDeal} disabled={isSubmitting}>
        {isSubmitting ? t("Updating...") : t("Update Deal")}
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

## Status

- ✅ handleUpdateDeal function: Implemented locally, needs deployment
- ✅ Edit Deal dialog: Implemented locally, needs deployment
- ✅ Edit button click handler: Already working in current version
- ✅ Form pre-population: Already working in current version