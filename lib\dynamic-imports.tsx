import dynamic from 'next/dynamic'
import { ComponentType } from 'react'
import { Skeleton } from '@/components/ui/skeleton'

// Loading fallback components
const TableSkeleton = () => (
  <div className="space-y-2">
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-8 w-full" />
    <Skeleton className="h-8 w-full" />
    <Skeleton className="h-8 w-full" />
  </div>
)

const CardSkeleton = () => (
  <div className="border rounded-lg p-4 space-y-3">
    <Skeleton className="h-6 w-1/3" />
    <Skeleton className="h-4 w-full" />
    <Skeleton className="h-4 w-2/3" />
  </div>
)

const ChartSkeleton = () => (
  <div className="border rounded-lg p-4">
    <Skeleton className="h-6 w-1/4 mb-4" />
    <Skeleton className="h-48 w-full" />
  </div>
)

const FormSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-20 w-full" />
    <Skeleton className="h-10 w-32" />
  </div>
)

// Dynamic imports for heavy components
export const DynamicEnhancedCustomerForm = dynamic(
  () => import('@/components/customers/enhanced-customer-form-v2').then(mod => ({ default: mod.EnhancedCustomerFormV2 })),
  {
    loading: () => <FormSkeleton />,
    ssr: false
  }
)

export const DynamicPerformanceMonitor = dynamic(
  () => import('@/components/performance-monitor').then(mod => ({ default: mod.PerformanceMonitor })),
  {
    loading: () => null,
    ssr: false
  }
)

// Analytics components (heavy charts) - Updated for deployment
export const DynamicAnalyticsChart = dynamic(
  () => import('@/components/charts/PipelineChart').then(mod => ({ default: mod.PipelineChart })).catch(() => ({ default: () => <ChartSkeleton /> })),
  {
    loading: () => <ChartSkeleton />,
    ssr: false
  }
)

export const DynamicReportsTable = dynamic(
  () => import('@/components/tables/enhanced-data-table').then(mod => ({ default: mod.EnhancedDataTable })).catch(() => ({ default: () => <TableSkeleton /> })),
  {
    loading: () => <TableSkeleton />,
    ssr: false
  }
)

// User management components
export const DynamicUserManagement = dynamic(
  () => import('@/app/dashboard/admin/users/page').then(mod => ({ default: mod.default })).catch(() => ({ default: () => <div>User Management Loading...</div> })),
  {
    loading: () => <TableSkeleton />,
    ssr: false
  }
)

// Advanced components that might not be needed immediately
export const DynamicAdvancedFilters = dynamic(
  () => import('@/components/tables/table-filters').then(mod => ({ default: mod.TableFilters })).catch(() => ({ default: () => <div>Filters Loading...</div> })),
  {
    loading: () => <Skeleton className="h-10 w-full" />,
    ssr: false
  }
)

export const DynamicExportTools = dynamic(
  () => import('@/components/tables/data-export-import').then(mod => ({ default: mod.DataExportDialog })).catch(() => ({ default: () => <div>Export Tools Loading...</div> })),
  {
    loading: () => <Skeleton className="h-8 w-24" />,
    ssr: false
  }
)

// Utility function to create dynamic imports with custom loading
export function createDynamicImport<T = any>(
  importFn: () => Promise<{ default: ComponentType<T> }>,
  loadingComponent?: () => React.ReactNode,
  errorComponent?: ComponentType<T>
) {
  return dynamic(
    () => importFn().catch(() => ({
      default: (errorComponent || (() => <div>Component failed to load</div>)) as ComponentType<T>
    })),
    {
      loading: loadingComponent || (() => <Skeleton className="h-20 w-full" />),
      ssr: false
    }
  )
}

// Preload functions for critical components
export const preloadCustomerForm = () => {
  import('@/components/customers/enhanced-customer-form-v2')
}

export const preloadAnalytics = () => {
  Promise.all([
    import('@/components/charts/PipelineChart').catch(() => null),
    import('@/components/tables/enhanced-data-table').catch(() => null)
  ])
}

// Bundle size optimization - lazy load heavy libraries
export const loadChartLibrary = () => {
  return import('recharts').catch(() => null)
}

export const loadExportLibrary = () => {
  // @ts-ignore - xlsx module exists but TypeScript may have issues with dynamic import
  return import('xlsx').catch(() => null)
}

export const loadDateLibrary = () => {
  return import('date-fns').catch(() => null)
}
