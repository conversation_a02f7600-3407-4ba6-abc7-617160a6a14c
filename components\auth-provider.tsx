"use client"

import { createContext, useContext, useEffect, useState, useMemo, useRef } from "react"
import { supabase } from "@/lib/supabase"
import type { AuthError, User as SupabaseUser, AuthChangeEvent, Session } from "@supabase/supabase-js"

// Extended user type with role information
interface UserWithRole extends SupabaseUser {
  role?: string
  full_name?: string
  department?: string
}

type AuthContextType = {
  user: UserWithRole | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  isAdmin: () => boolean
  hasRole: (role: string) => boolean
  retryConnection: () => void
  activateBypass: () => void
  activateEmergencyBypass: () => void
  resetAuthentication: () => Promise<void>
  bypassActive: boolean
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  error: null,
  signIn: async () => ({ error: new Error("Not implemented") as AuthError }),
  signUp: async () => ({ error: new Error("Not implemented") as AuthError }),
  signOut: async () => {},
  refreshSession: async () => {},
  isAdmin: () => false,
  hasRole: () => false,
  retryConnection: () => {},
  activateBypass: () => {},
  activateEmergencyBypass: () => {},
  resetAuthentication: async () => {},
  bypassActive: false,
})

// ✅ INCREASED TIMEOUTS: Extended timeouts to resolve session timeout issues
const AUTH_TIMEOUT = 30000 // 30 seconds (increased to handle slow network connections)
const MAX_RETRIES = 3 // Increased retries for better reliability
const RETRY_DELAY = 2000 // 2 second delay for retry
const DEVELOPMENT_MODE = process.env.NODE_ENV === 'development'
const ENABLE_AUTH_BYPASS = process.env.NEXT_PUBLIC_ENABLE_AUTH_BYPASS === 'true'

// Using singleton Supabase client to prevent multiple GoTrueClient instances

// Enhanced bypass detection with multiple methods
const getBypassStatus = () => {
  if (typeof window === 'undefined') return false

  // Method 1: URL parameter
  const urlBypass = window.location.search.includes('bypass=true')

  // Method 2: localStorage flag
  const localStorageBypass = localStorage.getItem('auth_bypass') === 'true'

  // Method 3: Special testing domain
  const testingDomain = window.location.hostname.includes('test') || window.location.hostname.includes('staging')

  // Method 4: Development environment
  const devBypass = DEVELOPMENT_MODE && ENABLE_AUTH_BYPASS

  // Method 5: Emergency bypass (for production issues)
  const emergencyBypass = localStorage.getItem('emergency_bypass') === 'true'

  return urlBypass || localStorageBypass || testingDomain || devBypass || emergencyBypass
}

const PRODUCTION_BYPASS = getBypassStatus()

// Mock user for development
const MOCK_USER: UserWithRole = {
  id: 'dev-user-123',
  email: '<EMAIL>',
  role: 'admin',
  full_name: 'Development User',
  department: 'Development',
  aud: 'authenticated',
  created_at: new Date().toISOString(),
  app_metadata: {},
  user_metadata: { full_name: 'Development User' }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserWithRole | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // ✅ CIRCUIT BREAKER: Prevent infinite authentication loops (optimized)
  const [authAttempts, setAuthAttempts] = useState(0)
  const [lastAuthAttempt, setLastAuthAttempt] = useState<number>(0)
  const MAX_AUTH_ATTEMPTS = 5 // Increased from 3 to 5 for better tolerance
  const AUTH_COOLDOWN = 15000 // Reduced from 30s to 15s for faster recovery
  const isInitializedRef = useRef(false)

  // Health check function with improved error handling
  const checkConnection = async (): Promise<boolean> => {
    try {
      console.log('🔍 Checking Supabase connection health...')

      // Simple health check - just try to get session without querying tables
      const { data: { session }, error } = await Promise.race([
        supabase.auth.getSession(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Health check timeout')), 10000)
        )
      ]) as any

      if (error) {
        console.warn('⚠️ Health check failed:', error.message)
        return false
      }

      console.log('✅ Supabase connection healthy')
      return true
    } catch (error) {
      console.warn('⚠️ Health check error:', error)
      return false
    }
  }

  // Bypass activation function
  const activateBypass = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_bypass', 'true')
      console.log('🚧 Authentication bypass activated')
      window.location.reload()
    }
  }

  // Emergency bypass activation
  const activateEmergencyBypass = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('emergency_bypass', 'true')
      console.log('🚨 Emergency authentication bypass activated')
      window.location.reload()
    }
  }

  // ✅ UNIFIED SESSION STORAGE: Store in both localStorage AND cookies
  const persistSession = (session: any) => {
    if (typeof window !== 'undefined' && session) {
      // Store in localStorage (existing functionality)
      const sessionData = {
        user: session.user,
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at,
        timestamp: Date.now()
      }
      localStorage.setItem('crm_session_backup', JSON.stringify(sessionData))

      // ✅ NEW: Also store essential data in cookies for server-side access
      setCookieSession(session)
    }
  }

  // ✅ COOKIE SESSION STORAGE: Store essential session data in cookies
  const setCookieSession = (session: any) => {
    if (typeof window === 'undefined' || !session?.user) return

    try {
      // Calculate expiration (use session expiration or 24 hours)
      const expiresAt = session.expires_at ? new Date(session.expires_at * 1000) : new Date(Date.now() + 24 * 60 * 60 * 1000)

      // Store essential session data in cookies (minimal for size constraints)
      const cookieData = {
        user_id: session.user.id,
        email: session.user.email,
        role: session.user.user_metadata?.role || session.user.role || 'user',
        expires_at: session.expires_at,
        timestamp: Date.now()
      }

      // Set secure cookie with proper flags
      const cookieValue = btoa(JSON.stringify(cookieData)) // Base64 encode for safety
      const isProduction = window.location.protocol === 'https:'
      const cookieFlags = [
        `expires=${expiresAt.toUTCString()}`,
        'path=/',
        'SameSite=Lax',
        ...(isProduction ? ['Secure'] : [])
      ].join('; ')

      document.cookie = `nawras_session=${cookieValue}; ${cookieFlags}`
      console.log('✅ Session stored in both localStorage and cookies')
    } catch (error) {
      console.warn('⚠️ Failed to store session in cookies:', error)
      // Continue with localStorage-only storage (graceful degradation)
    }
  }

  // ✅ UNIFIED SESSION RESTORATION: Try cookies first, then localStorage
  const restoreSessionFromBackup = (): any | null => {
    if (typeof window === 'undefined') return null

    // Try cookie-based session first
    const cookieSession = restoreSessionFromCookie()
    if (cookieSession) {
      console.log('✅ Session restored from cookies')
      return cookieSession
    }

    // Fallback to localStorage
    try {
      const backup = localStorage.getItem('crm_session_backup')
      if (!backup) return null

      const sessionData = JSON.parse(backup)
      const now = Date.now()
      const sessionAge = now - sessionData.timestamp

      // Only use backup if it's less than 1 hour old
      if (sessionAge < 3600000) {
        console.log('✅ Session restored from localStorage backup')
        return sessionData
      } else {
        localStorage.removeItem('crm_session_backup')
        return null
      }
    } catch {
      return null
    }
  }

  // ✅ COOKIE SESSION RESTORATION: Restore session from cookies
  const restoreSessionFromCookie = () => {
    if (typeof window === 'undefined') return null

    try {
      // Parse cookies to find nawras_session
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {} as Record<string, string>)

      const sessionCookie = cookies['nawras_session']
      if (!sessionCookie) return null

      // Decode and parse cookie data
      const cookieData = JSON.parse(atob(sessionCookie))

      // Check if session is still valid
      const now = Math.floor(Date.now() / 1000)
      if (cookieData.expires_at && now > cookieData.expires_at) {
        console.log('🔄 Cookie session expired, clearing...')
        clearCookieSession()
        return null
      }

      // Convert cookie data back to session format
      return {
        user: {
          id: cookieData.user_id,
          email: cookieData.email,
          role: cookieData.role,
          user_metadata: { role: cookieData.role }
        },
        expires_at: cookieData.expires_at,
        timestamp: cookieData.timestamp
      }
    } catch (error) {
      console.warn('⚠️ Failed to restore session from cookies:', error)
      return null
    }
  }

  // ✅ COOKIE SESSION CLEANUP: Clear session cookies
  const clearCookieSession = () => {
    if (typeof window === 'undefined') return

    try {
      document.cookie = 'nawras_session=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      console.log('✅ Session cookies cleared')
    } catch (error) {
      console.warn('⚠️ Failed to clear session cookies:', error)
    }
  }

  // Alternative token refresh with retry
  const alternativeTokenRefresh = async (refreshToken: string): Promise<any> => {
    const maxRetries = 3
    let lastError: Error | null = null

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/auth/v1/token?grant_type=refresh_token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''
          },
          body: JSON.stringify({
            refresh_token: refreshToken
          })
        })

        if (response.ok) {
          const data = await response.json()
          console.log(`✅ Alternative token refresh successful (attempt ${i + 1})`)
          return data
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Token refresh failed')
        console.warn(`⚠️ Token refresh attempt ${i + 1} failed:`, lastError.message)

        // Wait before retry (exponential backoff)
        if (i < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000))
        }
      }
    }

    throw lastError || new Error('All token refresh attempts failed')
  }

  // Timeout wrapper for async operations
  const withTimeout = <T,>(promiseOrBuilder: Promise<T> | any, timeoutMs: number): Promise<T> => {
    const promise = typeof promiseOrBuilder.then === 'function' ? promiseOrBuilder : promiseOrBuilder
    return Promise.race([
      promise,
      new Promise<T>((_, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error(`Operation timed out after ${timeoutMs}ms`))
        }, timeoutMs)
        timeoutRef.current = timeout
      })
    ])
  }

  // Function to fetch user role from users table with timeout
  const fetchUserRole = async (userId: string): Promise<{ role?: string; full_name?: string; department?: string } | null> => {
    try {
      const rolePromise = supabase
        .from('users')
        .select('role, full_name, department')
        .eq('id', userId)
        .single()

      // ✅ RESILIENT TIMEOUT: Increased to 20s for network reliability
      const { data: userProfile, error } = await Promise.race([
        rolePromise,
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('User role fetch timeout after 20 seconds')), 20000)
        )
      ])

      if (error) {
        console.warn('Could not fetch user role:', error.message)
        return { role: 'user', full_name: undefined, department: undefined } // Default fallback
      }

      return userProfile
    } catch (error) {
      console.warn('Error fetching user role:', error)
      return { role: 'user', full_name: undefined, department: undefined } // Default fallback
    }
  }

  // Function to enhance user with role information
  const enhanceUserWithRole = async (authUser: SupabaseUser): Promise<UserWithRole> => {
    try {
      const userProfile = await fetchUserRole(authUser.id)

      return {
        ...authUser,
        role: userProfile?.role || 'user',
        full_name: userProfile?.full_name || authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || 'User',
        department: userProfile?.department || 'General'
      }
    } catch (error) {
      console.warn('⚠️ Role enhancement failed, using basic user data:', error)
      // ✅ RESILIENT FALLBACK: Always return a valid user object
      return {
        ...authUser,
        role: 'user',
        full_name: authUser.user_metadata?.full_name || authUser.email?.split('@')[0] || 'User',
        department: 'General'
      }
    }
  }

  // Retry mechanism with exponential backoff
  const retryConnection = () => {
    if (retryCount < MAX_RETRIES) {
      setRetryCount(prev => prev + 1)
      setError(null)
      setLoading(true)
      console.log(`🔄 Retrying authentication connection (attempt ${retryCount + 1}/${MAX_RETRIES})`)

      // Exponential backoff: 2s, 4s, 8s
      const delay = RETRY_DELAY * Math.pow(2, retryCount)
      setTimeout(() => {
        // Force refresh the session
        supabase.auth.refreshSession().catch(console.error)
      }, delay)
    } else {
      console.log('🚧 Max retries reached')
      setError('Unable to connect to authentication service. Please try refreshing the page or check your internet connection.')
      setLoading(false)
    }
  }

  // 🔧 COMPREHENSIVE CLIENT-SIDE AUTHENTICATION RESET
  const resetAuthentication = async (): Promise<void> => {
    try {
      console.log('🔄 STARTING COMPREHENSIVE AUTHENTICATION RESET...')

      // Step 1: Clear all authentication state
      setUser(null)
      setLoading(true)
      setError(null)
      setRetryCount(0)

      // Step 2: Clear all localStorage authentication data
      if (typeof window !== 'undefined') {
        console.log('🧹 Clearing localStorage authentication data...')

        // Clear all CRM-related authentication data
        const keysToRemove = [
          'crm_session_backup',
          'auth_bypass',
          'emergency_bypass',
          'supabase.auth.token',
          'sb-' + process.env.NEXT_PUBLIC_SUPABASE_URL?.split('//')[1]?.split('.')[0] + '-auth-token',
          'supabase.session',
          'auth.session',
          'user.session'
        ]

        keysToRemove.forEach(key => {
          localStorage.removeItem(key)
          console.log(`🗑️ Removed localStorage key: ${key}`)
        })

        // Clear any keys that start with 'sb-' (Supabase keys)
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase') || key.includes('auth')) {
            localStorage.removeItem(key)
            console.log(`🗑️ Removed localStorage key: ${key}`)
          }
        })
      }

      // Step 3: Clear sessionStorage
      if (typeof window !== 'undefined') {
        console.log('🧹 Clearing sessionStorage...')
        sessionStorage.clear()
      }

      // Step 4: Clear any existing timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
        timeoutRef.current = null
      }

      // Step 5: Reset initialization flag
      isInitializedRef.current = false

      // Step 6: Sign out from Supabase to clear server-side session
      console.log('🚪 Signing out from Supabase...')
      await supabase.auth.signOut({ scope: 'global' })

      // Step 7: Wait a moment for cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Step 8: Force a fresh authentication check
      console.log('🔄 Forcing fresh authentication check...')
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error) {
        console.warn('⚠️ Error getting fresh session:', error.message)
        setError('Authentication reset completed, but unable to verify session. Please refresh the page.')
      } else {
        console.log('✅ Fresh session check completed')
        if (session?.user) {
          setUser(session.user as UserWithRole)
        } else {
          setUser(null)
        }
      }

      setLoading(false)
      console.log('✅ AUTHENTICATION RESET COMPLETED SUCCESSFULLY')

    } catch (error) {
      console.error('❌ Error during authentication reset:', error)
      setError('Authentication reset failed. Please refresh the page manually.')
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isInitializedRef.current && retryCount === 0) return

    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Set overall timeout for authentication initialization
    const authTimeout = setTimeout(() => {
      if (loading) {
        console.warn('⚠️ Authentication initialization timed out')
        setError('Authentication service timeout. Please try refreshing the page.')
        setLoading(false)
      }
    }, AUTH_TIMEOUT)

    // ✅ CIRCUIT BREAKER: Get initial session with circuit breaker protection
    const getInitialSession = async () => {
      try {
        setError(null)
        console.log('🔐 Initializing authentication...')

        // ✅ CIRCUIT BREAKER: Check if we're in cooldown period
        const now = Date.now()
        if (authAttempts >= MAX_AUTH_ATTEMPTS && (now - lastAuthAttempt) < AUTH_COOLDOWN) {
          const remainingCooldown = Math.ceil((AUTH_COOLDOWN - (now - lastAuthAttempt)) / 1000)
          console.warn(`🚨 Authentication circuit breaker active. Cooldown: ${remainingCooldown}s`)
          setError(`Too many authentication attempts. Please wait ${remainingCooldown} seconds.`)
          setLoading(false)
          return
        }

        // Reset attempts if cooldown period has passed
        if (authAttempts >= MAX_AUTH_ATTEMPTS && (now - lastAuthAttempt) >= AUTH_COOLDOWN) {
          console.log('✅ Circuit breaker reset - cooldown period completed')
          setAuthAttempts(0)
        }

        // Check if we should use bypass mode
        const shouldBypass = getBypassStatus()
        if (shouldBypass) {
          console.log('🚧 Using mock authentication (bypass mode enabled)')
          setUser(MOCK_USER)
          setLoading(false)
          setRetryCount(0)
          isInitializedRef.current = true
          clearTimeout(authTimeout)
          return
        }

        // ✅ CIRCUIT BREAKER: Track authentication attempt
        setAuthAttempts(prev => prev + 1)
        setLastAuthAttempt(now)

        // ✅ RESILIENT SESSION: Extended timeout to resolve session timeout issues
        console.log('🔐 RESILIENT: Getting session with 45-second timeout...')

        const sessionResult = await Promise.race([
          supabase.auth.getSession(),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Supabase session timeout after 45 seconds')), 45000)
          )
        ])

        const { data: { session }, error } = sessionResult

        if (error) {
          console.warn('⚠️ Session error:', error.message)
          throw new Error(`Session error: ${error.message}`)
        }

        if (session?.user) {
          console.log('✅ User session found, enhancing with role...')
          console.log('🔍 SESSION DEBUG: User ID:', session.user.id)
          console.log('🔍 SESSION DEBUG: User Email:', session.user.email)
          console.log('🔍 SESSION DEBUG: Session expires at:', session.expires_at)

          const enhancedUser = await enhanceUserWithRole(session.user)
          setUser(enhancedUser)
          persistSession(session) // Backup the session

          console.log('✅ Authentication successful - User object set in context')
          console.log('🔍 USER DEBUG: Enhanced user role:', enhancedUser.role)
          console.log('🔍 USER DEBUG: Enhanced user name:', enhancedUser.full_name)
        } else {
          console.log('ℹ️ No active session found, checking backup...')

          // Try to restore from backup
          const backupSession = restoreSessionFromBackup()
          if (backupSession) {
            console.log('🔄 BACKUP DEBUG: Restoring from backup session')
            const enhancedUser = await enhanceUserWithRole(backupSession.user)
            setUser(enhancedUser)
            console.log('✅ Session restored from backup')
          } else {
            console.log('❌ No backup session available - setting user to null')
            setUser(null)
          }
        }

        setLoading(false)
        setRetryCount(0) // Reset retry count on success
        isInitializedRef.current = true
        clearTimeout(authTimeout)
      } catch (error) {
        console.error("❌ Error in getInitialSession:", error)

        // Check if this is a timeout error and offer bypass
        const isTimeoutError = error instanceof Error && error.message.includes('timeout')
        const isNetworkError = error instanceof Error && (
          error.message.includes('fetch') ||
          error.message.includes('network') ||
          error.message.includes('Failed to fetch')
        )

        if (retryCount < MAX_RETRIES - 1) {
          console.log(`⏳ Retrying authentication (${retryCount + 1}/${MAX_RETRIES}) in ${RETRY_DELAY}ms...`)
          setTimeout(() => {
            retryConnection()
          }, RETRY_DELAY)
        } else {
          const errorMessage = error instanceof Error ? error.message : 'Authentication failed after multiple attempts'

          if (isTimeoutError) {
            setError(`Session timeout: ${errorMessage}. Please check your internet connection and try again.`)
          } else if (isNetworkError) {
            setError(`Network error: ${errorMessage}. Please check your internet connection.`)
          } else {
            setError(errorMessage)
          }

          setLoading(false)
          clearTimeout(authTimeout)
        }
      }
    }

    getInitialSession()

    // Listen for auth changes with error handling
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event: AuthChangeEvent, session: Session | null) => {
      try {
        console.log(`🔄 Auth state changed: ${event}`)

        if (session?.user) {
          // ✅ RESILIENT ENHANCEMENT: Always succeed with fallback
          try {
            const enhancedUser = await enhanceUserWithRole(session.user)
            setUser(enhancedUser)
            console.log('✅ User enhanced successfully with role:', enhancedUser.role)
          } catch (error) {
            console.warn('⚠️ User enhancement failed, using basic user data:', error)
            // ✅ GUARANTEED FALLBACK: Always provide a valid user object
            setUser({
              ...session.user,
              role: 'user',
              full_name: session.user.user_metadata?.full_name || session.user.email?.split('@')[0] || 'User',
              department: 'General'
            })
          }
        } else {
          setUser(null)
        }

        if (isInitializedRef.current) {
          setLoading(false)
        }
      } catch (error) {
        console.error('❌ Error in auth state change:', error)
        setError('Authentication state change failed')
      }
    })

    return () => {
      subscription.unsubscribe()
      clearTimeout(authTimeout)
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [retryCount])

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      return { error }
    } catch (error) {
      return { error: error as AuthError }
    }
  }

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      })

      // If signup successful, create user profile
      if (data.user && !error) {
        const { error: profileError } = await supabase
          .from('users')
          .insert([
            {
              id: data.user.id,
              email: data.user.email,
              full_name: fullName,
              role: 'user', // Default role
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
            },
          ])

        if (profileError) {
          console.error('Error creating user profile:', profileError)
        }
      }

      return { error }
    } catch (error) {
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    try {
      // Clear both localStorage and cookies
      localStorage.removeItem('crm_session_backup')
      clearCookieSession()

      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error("Error signing out:", error)
      }

      // Reset user state
      setUser(null)
      console.log('✅ Signed out and cleared all session data')
    } catch (error) {
      console.error("Error in signOut:", error)
    }
  }

  const refreshSession = async () => {
    try {
      const { error } = await supabase.auth.refreshSession()
      if (error) {
        console.error("Error refreshing session:", error)
      }
    } catch (error) {
      console.error("Error in refreshSession:", error)
    }
  }

  // Role checking functions
  const isAdmin = (): boolean => {
    return user?.role === 'admin'
  }

  const hasRole = (role: string): boolean => {
    return user?.role === role
  }

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    refreshSession,
    isAdmin,
    hasRole,
    retryConnection,
    activateBypass,
    activateEmergencyBypass,
    resetAuthentication,
    bypassActive: getBypassStatus(),
  }), [user, loading, error, signIn, signUp, signOut, refreshSession, isAdmin, hasRole, retryConnection, resetAuthentication])

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}