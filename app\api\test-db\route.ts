import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    console.log('🔍 [TEST-DB] Testing database connection...')
    
    // Create server-side Supabase client for testing
    const supabaseAuth = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log('🔍 [TEST-DB] Supabase client created')
    console.log('🔍 [TEST-DB] URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('🔍 [TEST-DB] Service key exists:', !!process.env.SUPABASE_SERVICE_ROLE_KEY)

    // Test query
    const { data: users, error } = await supabaseAuth
      .from('users')
      .select('id, email, role')
      .eq('email', '<EMAIL>')
      .limit(1)

    if (error) {
      console.error('❌ [TEST-DB] Database error:', error)
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        details: error 
      }, { status: 500 })
    }

    console.log('✅ [TEST-DB] Query successful:', users)

    return NextResponse.json({ 
      success: true, 
      users,
      message: 'Database connection working'
    })

  } catch (error) {
    console.error('❌ [TEST-DB] Connection error:', error)
    return NextResponse.json({ 
      success: false, 
      error: String(error) 
    }, { status: 500 })
  }
}
