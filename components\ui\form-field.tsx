import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, CheckCircle, Info } from "lucide-react"
import { forwardRef } from "react"

interface FormFieldProps {
  label: string
  name: string
  type?: "text" | "email" | "password" | "number" | "tel" | "url" | "textarea" | "select"
  placeholder?: string
  value?: string | number
  onChange?: (value: string) => void
  onBlur?: () => void
  error?: string
  success?: string
  info?: string
  required?: boolean
  disabled?: boolean
  className?: string
  options?: { value: string; label: string }[]
  rows?: number
}

export const FormField = forwardRef<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement, FormFieldProps>(
  ({
    label,
    name,
    type = "text",
    placeholder,
    value,
    onChange,
    onBlur,
    error,
    success,
    info,
    required = false,
    disabled = false,
    className,
    options = [],
    rows = 3,
    ...props
  }, ref) => {
    const hasError = !!error
    const hasSuccess = !!success
    const hasInfo = !!info

    const fieldClasses = cn(
      "transition-colors duration-200",
      hasError && "border-red-500 focus:border-red-500 focus:ring-red-500/20",
      hasSuccess && "border-green-500 focus:border-green-500 focus:ring-green-500/20",
      !hasError && !hasSuccess && "focus:border-primary focus:ring-primary/20"
    )

    const renderField = () => {
      switch (type) {
        case "textarea":
          return (
            <Textarea
              ref={ref as React.Ref<HTMLTextAreaElement>}
              name={name}
              placeholder={placeholder}
              value={value}
              onChange={(e) => onChange?.(e.target.value)}
              onBlur={onBlur}
              disabled={disabled}
              rows={rows}
              className={cn(fieldClasses, className)}
              {...props}
            />
          )

        case "select":
          return (
            <Select
              value={value as string}
              onValueChange={onChange}
              disabled={disabled}
            >
              <SelectTrigger className={cn(fieldClasses, className)}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )

        default:
          return (
            <Input
              ref={ref as React.Ref<HTMLInputElement>}
              type={type}
              name={name}
              placeholder={placeholder}
              value={value}
              onChange={(e) => onChange?.(e.target.value)}
              onBlur={onBlur}
              disabled={disabled}
              className={cn(fieldClasses, className)}
              {...props}
            />
          )
      }
    }

    const renderMessage = () => {
      if (hasError) {
        return (
          <div className="flex items-center gap-2 text-sm text-red-600 mt-1">
            <AlertCircle className="h-4 w-4" />
            {error}
          </div>
        )
      }

      if (hasSuccess) {
        return (
          <div className="flex items-center gap-2 text-sm text-green-600 mt-1">
            <CheckCircle className="h-4 w-4" />
            {success}
          </div>
        )
      }

      if (hasInfo) {
        return (
          <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
            <Info className="h-4 w-4" />
            {info}
          </div>
        )
      }

      return null
    }

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={name}
          className={cn(
            "text-sm font-medium",
            hasError && "text-red-600",
            hasSuccess && "text-green-600",
            required && "after:content-['*'] after:ml-0.5 after:text-red-500"
          )}
        >
          {label}
        </Label>
        
        {renderField()}
        {renderMessage()}
      </div>
    )
  }
)

FormField.displayName = "FormField"

// Specialized form field components
export function EmailField(props: Omit<FormFieldProps, "type">) {
  return <FormField {...props} type="email" />
}

export function PasswordField(props: Omit<FormFieldProps, "type">) {
  return <FormField {...props} type="password" />
}

export function NumberField(props: Omit<FormFieldProps, "type">) {
  return <FormField {...props} type="number" />
}

export function TextAreaField(props: Omit<FormFieldProps, "type">) {
  return <FormField {...props} type="textarea" />
}

export function SelectField(props: Omit<FormFieldProps, "type">) {
  return <FormField {...props} type="select" />
}

// Form validation helpers
export function validateEmail(email: string): string | null {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!email) return "Email is required"
  if (!emailRegex.test(email)) return "Please enter a valid email address"
  return null
}

export function validateRequired(value: string | number, fieldName: string): string | null {
  if (!value || (typeof value === "string" && value.trim() === "")) {
    return `${fieldName} is required`
  }
  return null
}

export function validateMinLength(value: string, minLength: number, fieldName: string): string | null {
  if (value && value.length < minLength) {
    return `${fieldName} must be at least ${minLength} characters`
  }
  return null
}

export function validateMaxLength(value: string, maxLength: number, fieldName: string): string | null {
  if (value && value.length > maxLength) {
    return `${fieldName} must be no more than ${maxLength} characters`
  }
  return null
}

export function validateNumber(value: string | number, fieldName: string): string | null {
  const num = typeof value === "string" ? parseFloat(value) : value
  if (isNaN(num)) {
    return `${fieldName} must be a valid number`
  }
  return null
}

export function validatePositiveNumber(value: string | number, fieldName: string): string | null {
  const num = typeof value === "string" ? parseFloat(value) : value
  if (isNaN(num) || num <= 0) {
    return `${fieldName} must be a positive number`
  }
  return null
}
