"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Activity, Clock, Database, Zap } from "lucide-react"

interface PerformanceMetrics {
  pageLoadTime: number
  databaseQueryTime: number
  renderTime: number
  memoryUsage: number
  cacheHitRate: number
  totalQueries: number
  cachedQueries: number
  // ✅ ENHANCED: Additional performance metrics
  slowQueries: number
  averageQueryTime: number
  activeConnections: number
  lastQueryTime: number
}

interface QueryLog {
  id: string
  table: string
  duration: number
  timestamp: number
  cached: boolean
  error?: string
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    pageLoadTime: 0,
    databaseQueryTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    totalQueries: 0,
    cachedQueries: 0,
    slowQueries: 0,
    averageQueryTime: 0,
    activeConnections: 0,
    lastQueryTime: 0
  })
  const [isVisible, setIsVisible] = useState(false)
  const startTimeRef = useRef<number>(Date.now())

  useEffect(() => {
    // Monitor page load performance
    const measurePerformance = () => {
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        const pageLoadTime = navigation.loadEventEnd - navigation.loadEventStart
        
        // Estimate memory usage (if available)
        const memoryInfo = (performance as any).memory
        const memoryUsage = memoryInfo ? memoryInfo.usedJSHeapSize / 1024 / 1024 : 0

        setMetrics(prev => ({
          ...prev,
          pageLoadTime: Math.round(pageLoadTime),
          memoryUsage: Math.round(memoryUsage * 100) / 100
        }))
      }
    }

    // Monitor render performance
    const measureRenderTime = () => {
      const renderTime = Date.now() - startTimeRef.current
      setMetrics(prev => ({
        ...prev,
        renderTime: renderTime
      }))
    }

    // Measure performance after component mount
    setTimeout(measurePerformance, 1000)
    setTimeout(measureRenderTime, 100)

    // Listen for custom performance events
    const handlePerformanceEvent = (event: CustomEvent) => {
      const { type, duration, cached } = event.detail
      
      setMetrics(prev => {
        const newMetrics = { ...prev }
        
        if (type === 'database-query') {
          newMetrics.databaseQueryTime = duration
          newMetrics.totalQueries += 1
          if (cached) {
            newMetrics.cachedQueries += 1
          }
          newMetrics.cacheHitRate = Math.round((newMetrics.cachedQueries / newMetrics.totalQueries) * 100)
        }
        
        return newMetrics
      })
    }

    window.addEventListener('performance-metric', handlePerformanceEvent as EventListener)
    
    return () => {
      window.removeEventListener('performance-metric', handlePerformanceEvent as EventListener)
    }
  }, [])

  const getPerformanceStatus = (value: number, thresholds: { good: number, fair: number }) => {
    if (value <= thresholds.good) return { status: 'excellent', color: 'bg-green-500' }
    if (value <= thresholds.fair) return { status: 'good', color: 'bg-yellow-500' }
    return { status: 'needs-improvement', color: 'bg-red-500' }
  }

  const pageLoadStatus = getPerformanceStatus(metrics.pageLoadTime, { good: 1000, fair: 3000 })
  const dbQueryStatus = getPerformanceStatus(metrics.databaseQueryTime, { good: 200, fair: 500 })
  const renderStatus = getPerformanceStatus(metrics.renderTime, { good: 100, fair: 300 })

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 z-50"
      >
        <Activity className="h-4 w-4 mr-2" />
        Performance
      </Button>
    )
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm flex items-center">
            <Zap className="h-4 w-4 mr-2" />
            Performance Monitor
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="h-6 w-6 p-0"
          >
            ×
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            <span className="text-sm">Page Load</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-mono">{metrics.pageLoadTime}ms</span>
            <div className={`w-2 h-2 rounded-full ${pageLoadStatus.color}`} />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Database className="h-4 w-4 mr-2" />
            <span className="text-sm">DB Query</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-mono">{metrics.databaseQueryTime}ms</span>
            <div className={`w-2 h-2 rounded-full ${dbQueryStatus.color}`} />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Activity className="h-4 w-4 mr-2" />
            <span className="text-sm">Render</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-mono">{metrics.renderTime}ms</span>
            <div className={`w-2 h-2 rounded-full ${renderStatus.color}`} />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm">Memory</span>
          <span className="text-sm font-mono">{metrics.memoryUsage}MB</span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm">Cache Hit Rate</span>
          <Badge variant={metrics.cacheHitRate > 70 ? "default" : "secondary"}>
            {metrics.cacheHitRate}%
          </Badge>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-sm">Total Queries</span>
          <span className="text-sm font-mono">{metrics.totalQueries}</span>
        </div>
      </CardContent>
    </Card>
  )
}

// Utility function to emit performance metrics
export const emitPerformanceMetric = (type: string, duration: number, cached = false) => {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('performance-metric', {
      detail: { type, duration, cached }
    }))
  }
}
