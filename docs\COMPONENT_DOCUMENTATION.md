# Nawras CRM - Component Documentation

## Overview

This document provides comprehensive documentation for all React components in the Nawras CRM application. Components are organized by category and include usage examples, props documentation, and best practices.

## Component Categories

### 1. Layout Components
Components that handle page structure and navigation.

### 2. Feature Components
Module-specific components with business logic.

### 3. UI Components
Reusable, generic UI elements.

### 4. Form Components
Standardized form system components.

### 5. Provider Components
Context providers for global state management.

## Layout Components

### AppSidebar
**Location**: `components/app-sidebar.tsx`
**Purpose**: Main navigation sidebar with role-based menu items and quick stats.

```typescript
interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  // Inherits all Sidebar props
}

// Usage
<AppSidebar className="border-r border-sidebar-border" />
```

**Features:**
- Role-based navigation (admin vs regular user)
- Real-time quick stats display
- Language toggle functionality
- User profile dropdown
- Responsive design

**Navigation Structure:**
- **Dashboard**: Main overview page
- **Sales & CRM**: Customers, Companies, Leads, Opportunities, Deals, Proposals
- **Management**: Tasks, Reports
- **Admin Only**: Settings, User Management

### DashboardLayout
**Location**: `components/layout/dashboard-layout.tsx`
**Purpose**: Flexible layout container for dashboard pages.

```typescript
interface DashboardLayoutProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  padding?: "sm" | "md" | "lg"
}

// Usage
<DashboardLayout maxWidth="xl" padding="lg">
  <DashboardHeader title="Customers" />
  <DashboardContent>
    {/* Page content */}
  </DashboardContent>
</DashboardLayout>
```

**Sub-components:**
- `DashboardHeader`: Page header with title and actions
- `DashboardContent`: Main content area
- `DashboardSidebar`: Optional sidebar content

## Feature Components

### Customer Components

#### EnhancedCustomerForm
**Location**: `components/customers/enhanced-customer-form.tsx`
**Purpose**: Comprehensive customer creation and editing form.

```typescript
interface EnhancedCustomerFormProps {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

// Usage
<EnhancedCustomerForm
  initialData={customer}
  onSubmit={handleSaveCustomer}
  onCancel={() => setShowForm(false)}
  isLoading={isSaving}
/>
```

**Features:**
- Multi-section form with collapsible sections
- Real-time validation
- Progress tracking
- File upload support
- Address management
- Business information fields

#### CustomerAnalytics
**Location**: `components/customers/customer-analytics.tsx`
**Purpose**: Customer analytics dashboard with charts and metrics.

```typescript
interface CustomerAnalyticsProps {
  customerId?: string
  dateRange?: DateRange
  showComparison?: boolean
}

// Usage
<CustomerAnalytics
  customerId="customer-123"
  dateRange={{ from: startDate, to: endDate }}
  showComparison={true}
/>
```

#### EnhancedCustomersTable
**Location**: `components/customers/enhanced-customers-table.tsx`
**Purpose**: Advanced data table for customer management.

```typescript
interface EnhancedCustomersTableProps {
  customers: Customer[]
  onEdit: (customer: Customer) => void
  onDelete: (customerId: string) => void
  onView: (customer: Customer) => void
  loading?: boolean
  searchTerm?: string
  filters?: CustomerFilters
}
```

**Features:**
- Sorting and filtering
- Bulk actions
- Export functionality
- Responsive design
- Pagination
- Search integration

### Deal Components

#### EnhancedKanbanBoard
**Location**: `components/deals/enhanced-kanban-board.tsx`
**Purpose**: Drag-and-drop Kanban board for deal pipeline management.

```typescript
interface EnhancedKanbanBoardProps {
  deals: Deal[]
  stages: DealStage[]
  onDealMove: (dealId: string, newStage: DealStage) => void
  onDealEdit: (deal: Deal) => void
  onDealCreate: (stage: DealStage) => void
  loading?: boolean
}

// Usage
<EnhancedKanbanBoard
  deals={deals}
  stages={dealStages}
  onDealMove={handleDealMove}
  onDealEdit={handleEditDeal}
  onDealCreate={handleCreateDeal}
/>
```

**Features:**
- Drag-and-drop functionality
- Stage-based organization
- Deal cards with key information
- Quick actions (edit, delete, view)
- Real-time updates

#### DealFormWizard
**Location**: `components/deals/deal-form-wizard.tsx`
**Purpose**: Multi-step form for creating complex deals.

```typescript
interface DealFormWizardProps {
  initialData?: Partial<DealFormData>
  onSubmit: (data: DealFormData) => Promise<void>
  onCancel: () => void
  customers: Customer[]
}
```

**Steps:**
1. Basic Information
2. Financial Details
3. Timeline & Milestones
4. Documents & Notes

## UI Components

### Button
**Location**: `components/ui/button.tsx`
**Purpose**: Standardized button component with variants.

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  asChild?: boolean
}

// Usage
<Button variant="outline" size="lg" onClick={handleClick}>
  Save Changes
</Button>
```

### Card
**Location**: `components/ui/card.tsx`
**Purpose**: Container component for content sections.

```typescript
// Sub-components
<Card>
  <CardHeader>
    <CardTitle>Customer Details</CardTitle>
    <CardDescription>Manage customer information</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Main content */}
  </CardContent>
  <CardFooter>
    {/* Actions */}
  </CardFooter>
</Card>
```

### Dialog
**Location**: `components/ui/dialog.tsx`
**Purpose**: Modal dialog component.

```typescript
// Usage
<Dialog open={isOpen} onOpenChange={setIsOpen}>
  <DialogTrigger asChild>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Confirm Action</DialogTitle>
      <DialogDescription>
        Are you sure you want to delete this customer?
      </DialogDescription>
    </DialogHeader>
    <DialogFooter>
      <Button variant="outline" onClick={() => setIsOpen(false)}>
        Cancel
      </Button>
      <Button variant="destructive" onClick={handleConfirm}>
        Delete
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### DataTable
**Location**: `components/ui/table.tsx`
**Purpose**: Enhanced table component with sorting and filtering.

```typescript
interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  searchKey?: string
  searchPlaceholder?: string
  onRowClick?: (row: T) => void
  loading?: boolean
  pagination?: boolean
}

// Usage
<DataTable
  data={customers}
  columns={customerColumns}
  searchKey="name"
  searchPlaceholder="Search customers..."
  onRowClick={handleRowClick}
  pagination={true}
/>
```

## Form Components

### FormLayout
**Location**: `components/forms/form-layout.tsx`
**Purpose**: Standardized form container with consistent styling.

```typescript
interface FormLayoutProps {
  title: string
  description?: string
  onSubmit: (e: React.FormEvent) => void
  onCancel: () => void
  submitLabel?: string
  cancelLabel?: string
  isSubmitting?: boolean
  showProgress?: boolean
  progress?: number
  variant?: "default" | "modal" | "page" | "inline"
  size?: "sm" | "md" | "lg" | "xl"
  children: React.ReactNode
}
```

### FormField
**Location**: `components/forms/form-field.tsx`
**Purpose**: Standardized form field with validation and accessibility.

```typescript
interface FormFieldProps {
  id: string
  label: string
  type?: InputType
  placeholder?: string
  value: any
  onChange: (value: any) => void
  error?: string
  required?: boolean
  disabled?: boolean
  icon?: React.ComponentType
  description?: string
  validation?: ValidationRule
}

// Usage
<FormField
  id="email"
  label="Email Address"
  type="email"
  icon={Mail}
  placeholder="<EMAIL>"
  value={formData.email}
  onChange={(value) => setFormData({...formData, email: value})}
  error={errors.email}
  required={true}
  validation={{ required: true, email: true }}
/>
```

### FormSection
**Location**: `components/forms/form-layout.tsx`
**Purpose**: Collapsible form sections for organizing content.

```typescript
interface FormSectionProps {
  section: {
    id: string
    title: string
    description?: string
    icon?: React.ComponentType
    required?: boolean
    fields: string[]
  }
  isExpanded: boolean
  isCompleted: boolean
  hasErrors: boolean
  onToggle: () => void
  children: React.ReactNode
}
```

## Provider Components

### AuthProvider
**Location**: `components/auth-provider.tsx`
**Purpose**: Authentication context and session management.

```typescript
interface AuthContextType {
  user: User | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<AuthResponse>
  signUp: (email: string, password: string) => Promise<AuthResponse>
  signOut: () => Promise<void>
  refreshSession: () => Promise<void>
  isAdmin: () => boolean
  hasRole: (role: string) => boolean
  retryConnection: () => void
}

// Usage
const { user, loading, signIn, signOut } = useAuth()
```

### ThemeProvider
**Location**: `components/theme-provider.tsx`
**Purpose**: Theme management and dark/light mode switching.

```typescript
interface ThemeProviderProps {
  children: React.ReactNode
  attribute?: string
  defaultTheme?: string
  enableSystem?: boolean
  disableTransitionOnChange?: boolean
}

// Usage in layout
<ThemeProvider
  attribute="class"
  defaultTheme="light"
  enableSystem
  disableTransitionOnChange
>
  {children}
</ThemeProvider>
```

### LanguageProvider
**Location**: `components/language-provider.tsx`
**Purpose**: Internationalization and language switching.

```typescript
interface LanguageContextType {
  language: 'en' | 'ar'
  t: (key: string) => string
  toggleLanguage: () => void
  setLanguage: (lang: 'en' | 'ar') => void
}

// Usage
const { t, language, toggleLanguage } = useLanguage()
```

## Custom Hooks

### useOptimizedData
**Location**: `hooks/use-optimized-data.ts`
**Purpose**: Optimized data fetching with caching and real-time updates.

```typescript
interface UseOptimizedDataOptions {
  table: string
  requiresAuth?: boolean
  realtime?: boolean
  cacheKey?: string
  filters?: Record<string, any>
  orderBy?: { column: string; ascending: boolean }
}

// Usage
const { data, loading, error, refetch } = useOptimizedData<Customer>({
  table: "customers",
  requiresAuth: true,
  realtime: true,
  cacheKey: "customers-list",
  filters: { status: "Active" },
  orderBy: { column: "created_at", ascending: false }
})
```

### useRole
**Location**: `hooks/use-role.tsx`
**Purpose**: Role-based access control and permission checking.

```typescript
interface UseRoleReturn {
  isAdmin: boolean
  canViewAllData: boolean
  canEditUsers: boolean
  canDeleteData: boolean
  hasPermission: (permission: string) => boolean
  userRole: string | null
}

// Usage
const { isAdmin, canViewAllData, hasPermission } = useRole()
```

### usePerformance
**Location**: `hooks/use-performance.ts`
**Purpose**: Performance monitoring and metrics collection.

```typescript
interface UsePerformanceOptions {
  trackPageViews?: boolean
  trackUserInteractions?: boolean
  trackApiCalls?: boolean
}

// Usage
const { trackEvent, getMetrics } = usePerformance({
  trackPageViews: true,
  trackUserInteractions: true
})
```

## Component Best Practices

### 1. Component Structure
```typescript
// Standard component structure
interface ComponentProps {
  // Props with clear types
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // 1. Hooks (state, context, custom hooks)
  const [state, setState] = useState()
  const { user } = useAuth()
  
  // 2. Derived state and computations
  const computedValue = useMemo(() => {
    return expensiveCalculation(state)
  }, [state])
  
  // 3. Event handlers
  const handleAction = useCallback(() => {
    // Handler implementation
  }, [])
  
  // 4. Effects
  useEffect(() => {
    // Effect implementation
  }, [])
  
  // 5. Early returns for loading/error states
  if (loading) return <LoadingSpinner />
  if (error) return <ErrorMessage error={error} />
  
  // 6. Main render
  return (
    <div>
      {/* JSX */}
    </div>
  )
}
```

### 2. Props Design
- Use TypeScript interfaces for props
- Provide default values where appropriate
- Use union types for variants
- Include optional props for flexibility

### 3. Performance Optimization
- Use `React.memo` for expensive components
- Implement `useCallback` for event handlers
- Use `useMemo` for expensive calculations
- Avoid inline object/function creation in render

### 4. Accessibility
- Include proper ARIA labels
- Ensure keyboard navigation
- Use semantic HTML elements
- Test with screen readers

### 5. Error Handling
- Implement error boundaries
- Provide fallback UI for errors
- Log errors for debugging
- Show user-friendly error messages

## Testing Components

### Unit Testing Example
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('calls onClick handler', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
```

### Integration Testing
```typescript
import { render, screen, waitFor } from '@testing-library/react'
import { CustomerForm } from '@/components/customers/customer-form'
import { AuthProvider } from '@/components/auth-provider'

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('CustomerForm Integration', () => {
  it('submits form with valid data', async () => {
    const handleSubmit = jest.fn()
    renderWithProviders(
      <CustomerForm onSubmit={handleSubmit} onCancel={() => {}} />
    )
    
    // Fill form and submit
    // Assert submission
  })
})
```

This component documentation provides comprehensive information about all components in the Nawras CRM application. Each component includes usage examples, props documentation, and implementation guidelines to ensure consistent development practices.
