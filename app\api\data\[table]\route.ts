import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// Define allowed tables for security
const ALLOWED_TABLES = [
  'customers',
  'companies', 
  'leads',
  'deals',
  'opportunities', // This is a view, but we'll handle it
  'tasks',
  'proposals',
  'users'
]

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ table: string }> }
) {
  try {
    // Use the same authentication pattern as working modules
    const supabase = await createClient()

    // Get the current user from Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = user.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    console.log(`🔍 [DATA-API] Fetching data from ${table} for user:`, userId)

    // Parse query parameters
    const { searchParams } = new URL(request.url)
    const select = searchParams.get('select') || '*'
    const orderBy = searchParams.get('orderBy')
    const orderAsc = searchParams.get('orderAsc') === 'true'
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    // Build query
    let query = supabase
      .from(actualTable)
      .select(select)
      .eq('user_id', userId)

    // Add ordering if specified
    if (orderBy) {
      query = query.order(orderBy, { ascending: orderAsc })
    } else {
      // Default ordering by created_at if column exists
      query = query.order('created_at', { ascending: false })
    }

    // Add limit if specified
    if (limit) {
      query = query.limit(limit)
    }

    const { data, error } = await query

    if (error) {
      console.error(`❌ [DATA-API] Error fetching ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully fetched ${data?.length || 0} records from ${table}`)

    return NextResponse.json({
      data: data || [],
      count: data?.length || 0,
      table: table
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error in ${params} API:`, error)
    
    return NextResponse.json(
      { 
        error: `Failed to fetch data from table`,
        details: error.message 
      },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ table: string }> }
) {
  try {
    // Use the same authentication pattern as working modules
    const supabase = await createClient()

    // Get the current user from Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = user.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    const body = await request.json()
    
    // Ensure user_id is set to the authenticated user
    const dataToInsert = {
      ...body,
      user_id: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    console.log(`🔍 [DATA-API] Creating record in ${table} for user:`, userId)

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    const { data, error } = await supabase
      .from(actualTable)
      .insert(dataToInsert)
      .select()
      .single()

    if (error) {
      console.error(`❌ [DATA-API] Error creating ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully created record in ${table}`)

    return NextResponse.json({
      data: data,
      message: 'Record created successfully'
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error creating record:`, error)
    
    return NextResponse.json(
      {
        error: 'Failed to create record',
        details: error.message
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ table: string }> }
) {
  try {
    // Use the same authentication pattern as working modules
    const supabase = await createClient()

    // Get the current user from Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = user.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { id, ...updateData } = body

    if (!id) {
      return NextResponse.json(
        { error: 'Record ID is required' },
        { status: 400 }
      )
    }

    // Add updated_at timestamp
    const dataToUpdate = {
      ...updateData,
      updated_at: new Date().toISOString()
    }

    console.log(`🔍 [DATA-API] Updating record in ${table} for user:`, userId)

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    const { data, error } = await supabase
      .from(actualTable)
      .update(dataToUpdate)
      .eq('id', id)
      .eq('user_id', userId) // Ensure user can only update their own records
      .select()
      .single()

    if (error) {
      console.error(`❌ [DATA-API] Error updating ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully updated record in ${table}`)

    return NextResponse.json({
      data: data,
      message: 'Record updated successfully'
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error updating record:`, error)

    return NextResponse.json(
      {
        error: 'Failed to update record',
        details: error.message
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ table: string }> }
) {
  try {
    // Use the same authentication pattern as working modules
    const supabase = await createClient()

    // Get the current user from Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = user.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { error: 'Record ID is required' },
        { status: 400 }
      )
    }

    console.log(`🔍 [DATA-API] Deleting record from ${table} for user:`, userId)

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    const { error } = await supabase
      .from(actualTable)
      .delete()
      .eq('id', id)
      .eq('user_id', userId) // Ensure user can only delete their own records

    if (error) {
      console.error(`❌ [DATA-API] Error deleting ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully deleted record from ${table}`)

    return NextResponse.json({
      message: 'Record deleted successfully'
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error deleting record:`, error)

    return NextResponse.json(
      {
        error: 'Failed to delete record',
        details: error.message
      },
      { status: 500 }
    )
  }
}
