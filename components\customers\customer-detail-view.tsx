"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import {
  User, Building2, Mail, Phone, MapPin, Globe, Calendar,
  Star, DollarSign, CreditCard, Truck, FileText, Activity,
  Edit, Trash2, Plus, MessageSquare, Clock, TrendingUp,
  Package, Receipt, Users, Target, CheckCircle, AlertCircle, X
} from "lucide-react"
import { CustomerDatabaseSchema } from "@/app/types/customer"
import { cn } from "@/lib/utils"

type Customer = CustomerDatabaseSchema

interface CustomerDetailViewProps {
  customer: Customer
  onEdit: () => void
  onDelete: () => void
  onClose: () => void
}

interface ActivityItem {
  id: string
  type: "deal" | "task" | "note" | "email" | "call" | "meeting"
  title: string
  description: string
  date: Date
  status?: "completed" | "pending" | "cancelled"
  amount?: number
}

interface RelatedRecord {
  id: string
  type: "deal" | "opportunity" | "proposal" | "task"
  title: string
  status: string
  value?: number
  date: Date
}

export function CustomerDetailView({ customer, onEdit, onDelete, onClose }: CustomerDetailViewProps) {
  const [activeTab, setActiveTab] = useState("overview")
  
  // Mock data - in real app, this would come from API
  const [activities] = useState<ActivityItem[]>([
    {
      id: "1",
      type: "deal",
      title: "New Deal Created",
      description: "Created deal for Q4 equipment purchase",
      date: new Date(2024, 11, 15),
      status: "pending",
      amount: 50000
    },
    {
      id: "2",
      type: "email",
      title: "Follow-up Email Sent",
      description: "Sent product catalog and pricing information",
      date: new Date(2024, 11, 10),
      status: "completed"
    },
    {
      id: "3",
      type: "call",
      title: "Discovery Call",
      description: "Initial consultation call - 45 minutes",
      date: new Date(2024, 11, 5),
      status: "completed"
    },
    {
      id: "4",
      type: "note",
      title: "Customer Requirements",
      description: "Documented specific technical requirements and timeline",
      date: new Date(2024, 11, 1),
      status: "completed"
    }
  ])

  const [relatedRecords] = useState<RelatedRecord[]>([
    {
      id: "1",
      type: "deal",
      title: "Q4 Equipment Purchase",
      status: "In Progress",
      value: 50000,
      date: new Date(2024, 11, 15)
    },
    {
      id: "2",
      type: "opportunity",
      title: "Annual Maintenance Contract",
      status: "Qualified",
      value: 12000,
      date: new Date(2024, 10, 20)
    },
    {
      id: "3",
      type: "proposal",
      title: "Custom Integration Proposal",
      status: "Sent",
      value: 25000,
      date: new Date(2024, 10, 15)
    }
  ])

  const getTierBadgeVariant = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'secondary'
      case 'Silver': return 'outline'
      case 'Gold': return 'warning'
      case 'Platinum': return 'info'
      case 'VIP': return 'destructive'
      default: return 'secondary'
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'deal': return DollarSign
      case 'task': return CheckCircle
      case 'note': return FileText
      case 'email': return Mail
      case 'call': return Phone
      case 'meeting': return Users
      default: return Activity
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'deal': return 'text-success'
      case 'task': return 'text-info'
      case 'note': return 'text-muted-foreground'
      case 'email': return 'text-primary'
      case 'call': return 'text-warning'
      case 'meeting': return 'text-destructive'
      default: return 'text-muted-foreground'
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: customer.currency_preference || 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarFallback className="bg-primary text-primary-foreground text-lg font-semibold">
              {(customer.contact_person || customer.company || "C").charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <h1 className="heading-2 text-foreground">
              {customer.contact_person || customer.company}
            </h1>
            <div className="flex items-center gap-2 mt-1">
              {customer.title_position && (
                <span className="text-muted-foreground">{customer.title_position}</span>
              )}
              <Badge variant={getTierBadgeVariant(customer.customer_tier || "Bronze")}>
                <Star className="h-3 w-3 mr-1" />
                {customer.customer_tier || 'Bronze'}
              </Badge>
              <Badge variant={customer.status === 'Active' ? 'success' : 'secondary'}>
                {customer.status}
              </Badge>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="outline" onClick={onDelete} className="text-destructive hover:text-destructive">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
          <Button variant="ghost" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-success/10 rounded-lg">
                <DollarSign className="h-4 w-4 text-success" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-lg font-semibold">
                  {formatCurrency(relatedRecords.reduce((sum, record) => sum + (record.value || 0), 0))}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-info/10 rounded-lg">
                <Target className="h-4 w-4 text-info" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Deals</p>
                <p className="text-lg font-semibold">
                  {relatedRecords.filter(r => r.type === 'deal').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-warning/10 rounded-lg">
                <Activity className="h-4 w-4 text-warning" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Activities</p>
                <p className="text-lg font-semibold">{activities.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Calendar className="h-4 w-4 text-primary" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Customer Since</p>
                <p className="text-lg font-semibold">
                  {new Date(customer.created_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="related">Related Records</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {customer.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Email</p>
                      <a 
                        href={`mailto:${customer.email}`}
                        className="text-primary hover:underline"
                      >
                        {customer.email}
                      </a>
                    </div>
                  </div>
                )}
                
                {customer.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Phone</p>
                      <a 
                        href={`tel:${customer.phone}`}
                        className="text-primary hover:underline"
                      >
                        {customer.phone}
                      </a>
                    </div>
                  </div>
                )}
                
                {customer.mobile && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Mobile</p>
                      <a 
                        href={`tel:${customer.mobile}`}
                        className="text-primary hover:underline"
                      >
                        {customer.mobile}
                      </a>
                    </div>
                  </div>
                )}
                
                {(customer.city || customer.country) && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Location</p>
                      <p>{customer.city}, {customer.country}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Company Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Company</p>
                    <p className="font-medium">{customer.company}</p>
                  </div>
                </div>
                
                {customer.website && (
                  <div className="flex items-center gap-3">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Website</p>
                      <a 
                        href={customer.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline"
                      >
                        {customer.website}
                      </a>
                    </div>
                  </div>
                )}
                
                {customer.industry && (
                  <div className="flex items-center gap-3">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Industry</p>
                      <p>{customer.industry}</p>
                    </div>
                  </div>
                )}
                
                {customer.business_type && (
                  <div className="flex items-center gap-3">
                    <Star className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">Business Type</p>
                      <Badge variant="outline">{customer.business_type}</Badge>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Financial Information */}
          {(customer.credit_limit || customer.payment_terms || customer.currency_preference) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {customer.credit_limit && (
                    <div>
                      <p className="text-sm text-muted-foreground">Credit Limit</p>
                      <p className="text-lg font-semibold">{formatCurrency(customer.credit_limit)}</p>
                    </div>
                  )}
                  
                  {customer.payment_terms && (
                    <div>
                      <p className="text-sm text-muted-foreground">Payment Terms</p>
                      <p className="text-lg font-semibold">{customer.payment_terms}</p>
                    </div>
                  )}
                  
                  {customer.currency_preference && (
                    <div>
                      <p className="text-sm text-muted-foreground">Currency</p>
                      <p className="text-lg font-semibold">{customer.currency_preference}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          {customer.notes && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Notes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground whitespace-pre-wrap">{customer.notes}</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="heading-3">Recent Activity</h3>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Activity
            </Button>
          </div>
          
          <div className="space-y-4">
            {activities.map((activity, index) => {
              const Icon = getActivityIcon(activity.type)
              return (
                <Card key={activity.id}>
                  <CardContent className="p-4">
                    <div className="flex items-start gap-4">
                      <div className={cn("p-2 rounded-lg bg-muted", getActivityColor(activity.type))}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{activity.title}</h4>
                          <div className="flex items-center gap-2">
                            {activity.amount && (
                              <Badge variant="success">
                                {formatCurrency(activity.amount)}
                              </Badge>
                            )}
                            <span className="text-sm text-muted-foreground">
                              {activity.date.toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {activity.description}
                        </p>
                        {activity.status && (
                          <Badge 
                            variant={activity.status === 'completed' ? 'success' : 'secondary'}
                            className="mt-2"
                          >
                            {activity.status}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="related" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="heading-3">Related Records</h3>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create New
            </Button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {relatedRecords.map((record) => (
              <Card key={record.id} className="hover-lift transition-all duration-normal cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Badge variant="outline" className="capitalize">
                      {record.type}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {record.date.toLocaleDateString()}
                    </span>
                  </div>
                  <h4 className="font-medium mb-1">{record.title}</h4>
                  <div className="flex items-center justify-between">
                    <Badge variant={record.status === 'In Progress' ? 'info' : 'secondary'}>
                      {record.status}
                    </Badge>
                    {record.value && (
                      <span className="font-semibold text-success">
                        {formatCurrency(record.value)}
                      </span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="documents" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="heading-3">Documents & Files</h3>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Upload Document
            </Button>
          </div>
          
          <Card>
            <CardContent className="p-8 text-center">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h4 className="font-medium mb-2">No documents uploaded</h4>
              <p className="text-muted-foreground mb-4">
                Upload contracts, proposals, and other important documents
              </p>
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Upload First Document
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
