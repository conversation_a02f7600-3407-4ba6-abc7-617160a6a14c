'use client'

import React, { useState, useCallback, useRef } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  Download, 
  Filter, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  Settings,
  Eye,
  EyeOff,
  Maximize2,
  Share2
} from 'lucide-react'
import { formatters } from './chart-config'

// Chart export functionality
export const useChartExport = () => {
  const exportToCSV = useCallback((data: any[], filename: string) => {
    if (!data || data.length === 0) return

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [])

  const exportToPNG = useCallback((chartRef: React.RefObject<HTMLDivElement>, filename: string) => {
    if (!chartRef.current) return

    // Use html2canvas or similar library for PNG export
    // For now, we'll use a simple approach
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (ctx) {
      // This is a simplified implementation
      // In production, you'd use html2canvas or similar
      console.log('Exporting chart as PNG:', filename)
    }
  }, [])

  const exportToJSON = useCallback((data: any[], filename: string) => {
    const jsonContent = JSON.stringify(data, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }, [])

  return { exportToCSV, exportToPNG, exportToJSON }
}

// Chart filter controls
interface ChartFiltersProps {
  onDateRangeChange?: (range: { start: Date; end: Date }) => void
  onCategoryFilter?: (categories: string[]) => void
  onValueRangeChange?: (range: { min: number; max: number }) => void
  availableCategories?: string[]
  dateRange?: { start: Date; end: Date }
  valueRange?: { min: number; max: number }
}

export const ChartFilters: React.FC<ChartFiltersProps> = ({
  onDateRangeChange,
  onCategoryFilter,
  onValueRangeChange,
  availableCategories = [],
  dateRange,
  valueRange
}) => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [localValueRange, setLocalValueRange] = useState(valueRange || { min: 0, max: 100 })

  const handleCategoryToggle = useCallback((category: string) => {
    const newSelection = selectedCategories.includes(category)
      ? selectedCategories.filter(c => c !== category)
      : [...selectedCategories, category]
    
    setSelectedCategories(newSelection)
    onCategoryFilter?.(newSelection)
  }, [selectedCategories, onCategoryFilter])

  const handleValueRangeChange = useCallback((values: number[]) => {
    const newRange = { min: values[0], max: values[1] }
    setLocalValueRange(newRange)
    onValueRangeChange?.(newRange)
  }, [onValueRangeChange])

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          <Filter className="h-4 w-4 mr-2" />
          Filters
          {selectedCategories.length > 0 && (
            <Badge variant="secondary" className="ml-2">
              {selectedCategories.length}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80">
        <div className="space-y-4">
          <div>
            <h4 className="font-medium mb-2">Date Range</h4>
            <Select>
              <SelectTrigger>
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 90 days</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {availableCategories.length > 0 && (
            <div>
              <h4 className="font-medium mb-2">Categories</h4>
              <div className="space-y-2">
                {availableCategories.map(category => (
                  <div key={category} className="flex items-center space-x-2">
                    <Switch
                      id={category}
                      checked={selectedCategories.includes(category)}
                      onCheckedChange={() => handleCategoryToggle(category)}
                    />
                    <Label htmlFor={category} className="text-sm">
                      {category}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          )}

          {valueRange && (
            <div>
              <h4 className="font-medium mb-2">Value Range</h4>
              <div className="px-2">
                <Slider
                  value={[localValueRange.min, localValueRange.max]}
                  onValueChange={handleValueRangeChange}
                  min={valueRange.min}
                  max={valueRange.max}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{formatters.compactNumber(localValueRange.min)}</span>
                  <span>{formatters.compactNumber(localValueRange.max)}</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Chart zoom and pan controls
interface ChartZoomControlsProps {
  onZoomIn?: () => void
  onZoomOut?: () => void
  onReset?: () => void
  onToggleFullscreen?: () => void
  isFullscreen?: boolean
  zoomLevel?: number
}

export const ChartZoomControls: React.FC<ChartZoomControlsProps> = ({
  onZoomIn,
  onZoomOut,
  onReset,
  onToggleFullscreen,
  isFullscreen = false,
  zoomLevel = 1
}) => {
  return (
    <div className="flex items-center gap-1">
      <Button variant="outline" size="sm" onClick={onZoomOut}>
        <ZoomOut className="h-4 w-4" />
      </Button>
      <Badge variant="outline" className="px-2">
        {Math.round(zoomLevel * 100)}%
      </Badge>
      <Button variant="outline" size="sm" onClick={onZoomIn}>
        <ZoomIn className="h-4 w-4" />
      </Button>
      <Button variant="outline" size="sm" onClick={onReset}>
        <RotateCcw className="h-4 w-4" />
      </Button>
      <Button variant="outline" size="sm" onClick={onToggleFullscreen}>
        <Maximize2 className="h-4 w-4" />
      </Button>
    </div>
  )
}

// Chart export controls
interface ChartExportControlsProps {
  data: any[]
  chartRef?: React.RefObject<HTMLDivElement>
  filename?: string
}

export const ChartExportControls: React.FC<ChartExportControlsProps> = ({
  data,
  chartRef,
  filename = 'chart-data'
}) => {
  const { exportToCSV, exportToPNG, exportToJSON } = useChartExport()

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-48">
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => exportToCSV(data, filename)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export as CSV
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => exportToJSON(data, filename)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export as JSON
          </Button>
          {chartRef && (
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start"
              onClick={() => exportToPNG(chartRef, filename)}
            >
              <Download className="h-4 w-4 mr-2" />
              Export as PNG
            </Button>
          )}
        </div>
      </PopoverContent>
    </Popover>
  )
}

// Chart settings panel
interface ChartSettingsProps {
  showGrid?: boolean
  showLegend?: boolean
  showTooltips?: boolean
  animationsEnabled?: boolean
  onSettingChange?: (setting: string, value: boolean) => void
}

export const ChartSettings: React.FC<ChartSettingsProps> = ({
  showGrid = true,
  showLegend = true,
  showTooltips = true,
  animationsEnabled = true,
  onSettingChange
}) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-64">
        <div className="space-y-4">
          <h4 className="font-medium">Chart Settings</h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="show-grid" className="text-sm">Show Grid</Label>
              <Switch
                id="show-grid"
                checked={showGrid}
                onCheckedChange={(checked) => onSettingChange?.('showGrid', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="show-legend" className="text-sm">Show Legend</Label>
              <Switch
                id="show-legend"
                checked={showLegend}
                onCheckedChange={(checked) => onSettingChange?.('showLegend', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="show-tooltips" className="text-sm">Show Tooltips</Label>
              <Switch
                id="show-tooltips"
                checked={showTooltips}
                onCheckedChange={(checked) => onSettingChange?.('showTooltips', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="animations" className="text-sm">Animations</Label>
              <Switch
                id="animations"
                checked={animationsEnabled}
                onCheckedChange={(checked) => onSettingChange?.('animationsEnabled', checked)}
              />
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
