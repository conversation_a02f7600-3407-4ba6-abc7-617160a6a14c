"use client"

import React, { useState, useEffect } from "react"
import { AdminGuard } from "@/components/admin-guard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  Database, 
  Activity, 
  Settings, 
  Shield, 
  BarChart3, 
  UserCheck, 
  AlertTriangle,
  TrendingUp,
  Server,
  Clock,
  CheckCircle
} from "lucide-react"
import { useOptimizedData } from "@/hooks/use-optimized-data"
import { useSession } from "next-auth/react"
import { useLanguage } from "@/components/language-provider"
import Link from "next/link"

interface SystemMetrics {
  totalUsers: number
  totalCustomers: number
  totalRevenue: number
  activeUsers: number
  systemHealth: 'healthy' | 'warning' | 'critical'
  lastBackup: string
}

export default function AdminDashboard() {
  const { data: session } = useSession()
  const user = session?.user
  const { t } = useLanguage()
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalUsers: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    activeUsers: 0,
    systemHealth: 'healthy',
    lastBackup: new Date().toISOString()
  })
  const [loading, setLoading] = useState(true)

  // Fetch system metrics
  const { data: users } = useOptimizedData({
    table: 'users',
    select: 'id, email, role, created_at, full_name',
    orderBy: { column: 'created_at', ascending: false }
  })

  const { data: customers } = useOptimizedData({
    table: 'customers',
    select: 'id, created_at',
    orderBy: { column: 'created_at', ascending: false }
  })



  useEffect(() => {
    if (users && customers) {
      setMetrics({
        totalUsers: users.length,
        totalCustomers: customers.length,
        totalRevenue: 0, // Remove deals-based revenue calculation
        activeUsers: users.filter(u => u.role !== 'inactive').length,
        systemHealth: 'healthy',
        lastBackup: new Date().toISOString()
      })
      setLoading(false)
    }
  }, [users, customers])

  const quickActions = [
    {
      title: "User Management",
      description: "Manage user accounts and permissions",
      icon: Users,
      href: "/dashboard/admin/users",
      color: "bg-blue-500"
    },
    {
      title: "System Reports",
      description: "View detailed analytics and reports",
      icon: BarChart3,
      href: "/dashboard/admin/reports",
      color: "bg-green-500"
    },
    {
      title: "Database Management",
      description: "Monitor database health and performance",
      icon: Database,
      href: "/dashboard/settings",
      color: "bg-purple-500"
    },
    {
      title: "System Settings",
      description: "Configure system-wide settings",
      icon: Settings,
      href: "/dashboard/settings",
      color: "bg-orange-500"
    }
  ]

  const systemStats = [
    {
      title: "Total Users",
      value: metrics.totalUsers,
      icon: Users,
      change: "+12%",
      changeType: "positive" as const
    },
    {
      title: "Total Customers",
      value: metrics.totalCustomers,
      icon: UserCheck,
      change: "+8%",
      changeType: "positive" as const
    },
    {
      title: "Total Revenue",
      value: `$${metrics.totalRevenue.toLocaleString()}`,
      icon: BarChart3,
      change: "+23%",
      changeType: "positive" as const
    }
  ]

  return (
    <AdminGuard>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
            <p className="text-muted-foreground">
              Welcome back, {user?.full_name || user?.email}. Here's your system overview.
            </p>
          </div>
          <Badge variant="secondary" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Administrator
          </Badge>
        </div>

        {/* System Health Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">All Systems Operational</span>
              </div>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Server className="h-4 w-4" />
                  <span>Database: Online</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>Last Backup: {new Date(metrics.lastBackup).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {systemStats.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span className={stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'}>
                    {stat.change}
                  </span>
                  {" "}from last month
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action, index) => (
              <Link key={index} href={action.href}>
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-lg ${action.color} flex items-center justify-center mb-2`}>
                      <action.icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-lg">{action.title}</CardTitle>
                    <CardDescription>{action.description}</CardDescription>
                  </CardHeader>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent System Activity</CardTitle>
            <CardDescription>Latest administrative actions and system events</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Users className="h-5 w-5 text-blue-500" />
                <div className="flex-1">
                  <p className="font-medium">New user registered</p>
                  <p className="text-sm text-muted-foreground">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Database className="h-5 w-5 text-green-500" />
                <div className="flex-1">
                  <p className="font-medium">Database backup completed</p>
                  <p className="text-sm text-muted-foreground">1 hour ago</p>
                </div>
              </div>
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Settings className="h-5 w-5 text-orange-500" />
                <div className="flex-1">
                  <p className="font-medium">System settings updated</p>
                  <p className="text-sm text-muted-foreground">3 hours ago</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminGuard>
  )
}
