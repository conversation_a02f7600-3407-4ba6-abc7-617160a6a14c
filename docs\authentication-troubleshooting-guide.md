# Authentication Troubleshooting Guide

## Quick Diagnosis

### Symptoms and Solutions

| Symptom | Likely Cause | Immediate Action | Long-term Solution |
|---------|--------------|------------------|-------------------|
| Stuck on "Authenticating..." | Authentication provider timeout | Wait for reset interface (20s) | Check Supabase service status |
| Reset interface doesn't appear | Layout-level authentication issue | Manual page refresh (F5) | Investigate layout implementation |
| Reset button doesn't work | Client-side state corruption | Try "Refresh Page" button | Clear browser cache/storage |
| Login page stuck loading | Server-side authentication failure | Check network connectivity | Contact Supabase support |
| Works in incognito mode | Browser cache/storage issues | Clear site data | Implement better cache management |

## Detailed Troubleshooting Steps

### Step 1: Identify the Authentication State

Check browser console for error messages:

```javascript
// Common error patterns:
"Session request timeout" → Supabase connectivity issue
"Authentication provider not found" → Component initialization failure
"Invalid session" → Corrupted authentication data
"Network error" → Internet connectivity problem
```

### Step 2: Determine Scope of Issue

Test different pages to understand the scope:

1. **Login page** (`/login`) - Should always work
2. **Dashboard** (`/dashboard`) - Main protected page
3. **Specific modules** (`/dashboard/customers`, `/dashboard/deals`) - Individual page issues

### Step 3: Apply Appropriate Recovery Method

#### For Stuck Authentication (Most Common)

1. **Wait for automatic reset** (20 seconds)
2. **Click "Reset Authentication"** when interface appears
3. **If reset fails**, try "Refresh Page"
4. **If still stuck**, try "Return to Dashboard"

#### For Login Issues

1. **Clear browser cache** for the site
2. **Disable browser extensions** temporarily
3. **Try incognito/private mode**
4. **Check network connectivity**

#### For Persistent Issues

1. **Clear all site data**:
   ```javascript
   // In browser console:
   localStorage.clear()
   sessionStorage.clear()
   // Then refresh page
   ```

2. **Reset browser state**:
   - Clear cookies for the domain
   - Clear cached images and files
   - Restart browser

## Error Code Reference

### Console Error Messages

#### Authentication Errors
```
❌ Error in getInitialSession: Error: Session request timeout
→ Supabase authentication service is unreachable
→ Action: Wait and retry, check service status

❌ Authentication provider not initialized
→ React component initialization failed
→ Action: Refresh page, check component dependencies

❌ Invalid session token
→ Stored authentication data is corrupted
→ Action: Use authentication reset functionality
```

#### Network Errors
```
Failed to load resource: the server responded with a status of 404
→ Authentication endpoint not found
→ Action: Check API configuration, verify deployment

Failed to load resource: net::ERR_NETWORK_CHANGED
→ Network connectivity changed during request
→ Action: Refresh page, check internet connection
```

#### Component Errors
```
Cannot read properties of undefined (reading 'user')
→ Authentication context not available
→ Action: Check component hierarchy, verify provider setup

Hydration failed because the initial UI does not match
→ Server/client authentication state mismatch
→ Action: Clear cache, refresh page
```

## Service Status Checks

### Supabase Service Health

Check these indicators for Supabase service status:

1. **Supabase Status Page**: https://status.supabase.com/
2. **Network Tab**: Look for failed requests to `*.supabase.co`
3. **Console Logs**: Check for Supabase-specific error messages

### Application Health

Verify application deployment status:

1. **Vercel Dashboard**: Check deployment status
2. **Build Logs**: Look for authentication-related build errors
3. **Runtime Logs**: Monitor for authentication failures

## Recovery Procedures

### User-Level Recovery

#### Immediate Actions (< 1 minute)
1. Wait for authentication reset interface
2. Click "Reset Authentication" button
3. If unsuccessful, try "Refresh Page"

#### Short-term Actions (1-5 minutes)
1. Clear browser cache for the site
2. Try different browser or incognito mode
3. Check internet connectivity
4. Navigate directly to login page

#### Extended Actions (5+ minutes)
1. Clear all browser data for the site
2. Restart browser completely
3. Try from different device/network
4. Contact support with error details

### Developer-Level Recovery

#### Code-Level Fixes
```typescript
// Force authentication reset in console:
localStorage.removeItem('supabase.auth.token')
sessionStorage.clear()
window.location.reload()

// Check authentication state:
console.log('Auth state:', {
  user: window.supabase?.auth?.user(),
  session: window.supabase?.auth?.session()
})
```

#### Configuration Checks
1. Verify Supabase project configuration
2. Check environment variables
3. Validate API keys and URLs
4. Review authentication provider setup

## Prevention Strategies

### Code-Level Prevention

1. **Implement proper error boundaries**:
   ```typescript
   <ErrorBoundary fallback={<AuthenticationError />}>
     <AuthProvider>
       <App />
     </AuthProvider>
   </ErrorBoundary>
   ```

2. **Add connection health checks**:
   ```typescript
   const checkAuthHealth = async () => {
     try {
       await supabase.auth.getSession()
       return true
     } catch (error) {
       console.warn('Auth health check failed:', error)
       return false
     }
   }
   ```

3. **Implement retry logic**:
   ```typescript
   const retryAuthentication = async (maxRetries = 3) => {
     for (let i = 0; i < maxRetries; i++) {
       try {
         return await authenticateUser()
       } catch (error) {
         if (i === maxRetries - 1) throw error
         await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
       }
     }
   }
   ```

### Infrastructure-Level Prevention

1. **Monitor service health**: Set up alerts for authentication failures
2. **Implement circuit breakers**: Prevent cascading failures
3. **Use connection pooling**: Optimize database connections
4. **Set up redundancy**: Multiple authentication providers if needed

## Monitoring and Alerting

### Key Metrics to Monitor

1. **Authentication Success Rate**: Should be > 95%
2. **Average Authentication Time**: Should be < 3 seconds
3. **Reset Interface Trigger Rate**: Should be < 5% of sessions
4. **Service Timeout Frequency**: Should be minimal

### Alert Conditions

```yaml
# Example monitoring configuration
alerts:
  - name: "High Authentication Failure Rate"
    condition: "auth_failure_rate > 10%"
    action: "notify_dev_team"
  
  - name: "Authentication Service Timeout"
    condition: "auth_timeout_count > 5 in 5min"
    action: "check_supabase_status"
  
  - name: "Reset Interface High Usage"
    condition: "reset_trigger_rate > 15%"
    action: "investigate_root_cause"
```

## Escalation Procedures

### Level 1: User Self-Service
- Use built-in authentication reset functionality
- Follow basic troubleshooting steps
- Check service status pages

### Level 2: Technical Support
- Provide detailed error logs
- Share browser/device information
- Describe steps to reproduce issue

### Level 3: Development Team
- Investigate code-level issues
- Check service configurations
- Implement fixes and deploy

### Level 4: Infrastructure Team
- Address service-level problems
- Scale authentication infrastructure
- Coordinate with third-party providers

## Testing Procedures

### Manual Testing Checklist

- [ ] Authentication reset appears after 20 seconds
- [ ] Reset button clears authentication data
- [ ] Refresh button reloads page properly
- [ ] Return to dashboard navigation works
- [ ] Login page remains accessible during issues
- [ ] Console errors are properly logged

### Automated Testing

```typescript
// Example test for authentication reset
describe('Authentication Reset System', () => {
  it('should show reset interface after timeout', async () => {
    // Mock stuck authentication
    mockAuthProvider.mockImplementation(() => ({
      loading: true,
      user: null,
      error: null
    }))
    
    render(<DashboardLayout><TestComponent /></DashboardLayout>)
    
    // Wait for timeout
    await waitFor(() => {
      expect(screen.getByText('Reset Authentication')).toBeInTheDocument()
    }, { timeout: 25000 })
  })
})
```

---

**Emergency Contact**: <EMAIL>
**Last Updated**: July 10, 2025
**Next Review**: August 10, 2025
