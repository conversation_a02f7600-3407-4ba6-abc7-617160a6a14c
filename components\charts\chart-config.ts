// Chart Configuration and Theme Settings
export const CHART_COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  accent: '#F59E0B',
  danger: '#EF4444',
  warning: '#F97316',
  info: '#06B6D4',
  success: '#22C55E',
  muted: '#6B7280',
  
  // Pipeline stages
  pipeline: {
    prospecting: '#3B82F6',
    qualification: '#06B6D4',
    proposal: '#F59E0B',
    negotiation: '#F97316',
    'closed-won': '#22C55E',
    'closed-lost': '#EF4444'
  },
  
  // Lead sources
  leadSources: {
    website: '#3B82F6',
    'social-media': '#8B5CF6',
    'email-campaign': '#06B6D4',
    'cold-call': '#F59E0B',
    referral: '#22C55E',
    'trade-show': '#F97316',
    advertisement: '#EF4444',
    other: '#6B7280'
  },
  
  // Customer tiers
  customerTiers: {
    bronze: '#CD7F32',
    silver: '#C0C0C0',
    gold: '#FFD700',
    platinum: '#E5E4E2',
    vip: '#8B5CF6'
  }
}

export const DEFAULT_CHART_CONFIG = {
  responsive: true,
  showGrid: true,
  showTooltip: true,
  showLegend: true,
  height: 300,
  theme: 'light' as const
}

export const CHART_ANIMATIONS = {
  duration: 300,
  easing: 'ease-in-out'
}

// Responsive breakpoints for charts
export const CHART_BREAKPOINTS = {
  mobile: 480,
  tablet: 768,
  desktop: 1024
}

// Chart size configurations
export const CHART_SIZES = {
  small: { width: '100%', height: 200 },
  medium: { width: '100%', height: 300 },
  large: { width: '100%', height: 400 },
  xlarge: { width: '100%', height: 500 }
}

// Common chart margins
export const CHART_MARGINS = {
  default: { top: 20, right: 30, left: 20, bottom: 5 },
  withLegend: { top: 20, right: 30, left: 20, bottom: 40 },
  compact: { top: 10, right: 15, left: 10, bottom: 5 }
}

// Format functions for different data types
export const formatters = {
  currency: (value: number, currency = 'USD') => 
    new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value),
    
  percentage: (value: number) => `${value.toFixed(1)}%`,
  
  number: (value: number) => 
    new Intl.NumberFormat('en-US').format(value),
    
  compactNumber: (value: number) => {
    if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
    if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
    return value.toString()
  },
  
  date: (date: string | Date) => 
    new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      day: 'numeric' 
    }).format(new Date(date)),
    
  monthYear: (date: string | Date) => 
    new Intl.DateTimeFormat('en-US', { 
      month: 'short', 
      year: 'numeric' 
    }).format(new Date(date))
}

// Chart-specific configurations
export const PIPELINE_CHART_CONFIG = {
  ...DEFAULT_CHART_CONFIG,
  height: 350,
  colors: Object.values(CHART_COLORS.pipeline)
}

export const REVENUE_CHART_CONFIG = {
  ...DEFAULT_CHART_CONFIG,
  height: 300,
  colors: [CHART_COLORS.primary, CHART_COLORS.secondary]
}

export const LEAD_FUNNEL_CONFIG = {
  ...DEFAULT_CHART_CONFIG,
  height: 400,
  colors: Object.values(CHART_COLORS.leadSources)
}

export const CUSTOMER_DISTRIBUTION_CONFIG = {
  ...DEFAULT_CHART_CONFIG,
  height: 300,
  colors: Object.values(CHART_COLORS.customerTiers)
}
