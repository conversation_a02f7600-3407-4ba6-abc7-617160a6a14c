"use client"

import { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { 
  Search, Filter, X, Calendar, MapPin, Building2, 
  Star, DollarSign, Users, SlidersHorizontal, ChevronDown
} from "lucide-react"
import { CustomerDatabaseSchema } from "@/app/types/customer"
import { cn } from "@/lib/utils"

type Customer = CustomerDatabaseSchema

interface SearchFilters {
  searchTerm: string
  status: string[]
  tier: string[]
  businessType: string[]
  country: string[]
  city: string[]
  dateRange: {
    from?: Date
    to?: Date
  }
  creditLimit: {
    min?: number
    max?: number
  }
  annualVolume: {
    min?: number
    max?: number
  }
}

interface AdvancedSearchProps {
  customers: Customer[]
  onFiltersChange: (filteredCustomers: Customer[]) => void
  className?: string
}

export function AdvancedSearch({ customers, onFiltersChange, className }: AdvancedSearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({
    searchTerm: "",
    status: [],
    tier: [],
    businessType: [],
    country: [],
    city: [],
    dateRange: {},
    creditLimit: {},
    annualVolume: {}
  })

  const [isAdvancedOpen, setIsAdvancedOpen] = useState(false)
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)

  // Extract unique values for filter options
  const filterOptions = useMemo(() => {
    const statuses = Array.from(new Set(customers.map(c => c.status).filter(Boolean)))
    const tiers = Array.from(new Set(customers.map(c => c.customer_tier).filter(Boolean)))
    const businessTypes = Array.from(new Set(customers.map(c => c.business_type).filter(Boolean)))
    const countries = Array.from(new Set(customers.map(c => c.country).filter(Boolean)))
    const cities = Array.from(new Set(customers.map(c => c.city).filter(Boolean)))

    return {
      statuses: statuses.sort(),
      tiers: tiers.sort(),
      businessTypes: businessTypes.sort(),
      countries: countries.sort(),
      cities: cities.sort()
    }
  }, [customers])

  // Generate search suggestions
  const generateSuggestions = (term: string) => {
    if (!term || term.length < 2) {
      setSearchSuggestions([])
      return
    }

    const suggestions = new Set<string>()
    const lowerTerm = term.toLowerCase()

    customers.forEach(customer => {
      // Add matching names
      if (customer.contact_person?.toLowerCase().includes(lowerTerm)) {
        suggestions.add(customer.contact_person)
      }
      // Add matching companies
      if (customer.company?.toLowerCase().includes(lowerTerm)) {
        suggestions.add(customer.company)
      }
      // Add matching emails
      if (customer.email?.toLowerCase().includes(lowerTerm)) {
        suggestions.add(customer.email)
      }
      // Add matching cities
      if (customer.city?.toLowerCase().includes(lowerTerm)) {
        suggestions.add(`${customer.city}, ${customer.country}`)
      }
    })

    setSearchSuggestions(Array.from(suggestions).slice(0, 5))
  }

  // Filter customers based on current filters
  const filteredCustomers = useMemo(() => {
    return customers.filter(customer => {
      // Text search
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase()
        const matchesText = 
          customer.contact_person?.toLowerCase().includes(searchLower) ||
          customer.company?.toLowerCase().includes(searchLower) ||
          customer.email?.toLowerCase().includes(searchLower) ||
          customer.phone?.toLowerCase().includes(searchLower) ||
          customer.city?.toLowerCase().includes(searchLower) ||
          customer.industry?.toLowerCase().includes(searchLower)
        
        if (!matchesText) return false
      }

      // Status filter
      if (filters.status.length > 0 && !filters.status.includes(customer.status || "")) {
        return false
      }

      // Tier filter
      if (filters.tier.length > 0 && !filters.tier.includes(customer.customer_tier || "")) {
        return false
      }

      // Business type filter
      if (filters.businessType.length > 0 && !filters.businessType.includes(customer.business_type || "")) {
        return false
      }

      // Country filter
      if (filters.country.length > 0 && !filters.country.includes(customer.country || "")) {
        return false
      }

      // City filter
      if (filters.city.length > 0 && !filters.city.includes(customer.city || "")) {
        return false
      }

      // Date range filter
      if (filters.dateRange.from || filters.dateRange.to) {
        const customerDate = new Date(customer.created_at)
        if (filters.dateRange.from && customerDate < filters.dateRange.from) return false
        if (filters.dateRange.to && customerDate > filters.dateRange.to) return false
      }

      // Credit limit filter
      if (filters.creditLimit.min !== undefined || filters.creditLimit.max !== undefined) {
        const creditLimit = customer.credit_limit || 0
        if (filters.creditLimit.min !== undefined && creditLimit < filters.creditLimit.min) return false
        if (filters.creditLimit.max !== undefined && creditLimit > filters.creditLimit.max) return false
      }

      // Annual volume filter
      if (filters.annualVolume.min !== undefined || filters.annualVolume.max !== undefined) {
        const annualVolume = customer.annual_volume || 0
        if (filters.annualVolume.min !== undefined && annualVolume < filters.annualVolume.min) return false
        if (filters.annualVolume.max !== undefined && annualVolume > filters.annualVolume.max) return false
      }

      return true
    })
  }, [customers, filters])

  // Update parent component when filters change
  useEffect(() => {
    onFiltersChange(filteredCustomers)
  }, [filteredCustomers, onFiltersChange])

  // Update search suggestions when search term changes
  useEffect(() => {
    generateSuggestions(filters.searchTerm)
  }, [filters.searchTerm, customers])

  const updateFilter = <K extends keyof SearchFilters>(key: K, value: SearchFilters[K]) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const addArrayFilter = (key: keyof Pick<SearchFilters, 'status' | 'tier' | 'businessType' | 'country' | 'city'>, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: [...(prev[key] as string[]), value]
    }))
  }

  const removeArrayFilter = (key: keyof Pick<SearchFilters, 'status' | 'tier' | 'businessType' | 'country' | 'city'>, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: (prev[key] as string[]).filter(item => item !== value)
    }))
  }

  const clearAllFilters = () => {
    setFilters({
      searchTerm: "",
      status: [],
      tier: [],
      businessType: [],
      country: [],
      city: [],
      dateRange: {},
      creditLimit: {},
      annualVolume: {}
    })
  }

  const getActiveFilterCount = () => {
    let count = 0
    if (filters.searchTerm) count++
    if (filters.status.length > 0) count++
    if (filters.tier.length > 0) count++
    if (filters.businessType.length > 0) count++
    if (filters.country.length > 0) count++
    if (filters.city.length > 0) count++
    if (filters.dateRange.from || filters.dateRange.to) count++
    if (filters.creditLimit.min !== undefined || filters.creditLimit.max !== undefined) count++
    if (filters.annualVolume.min !== undefined || filters.annualVolume.max !== undefined) count++
    return count
  }

  const activeFilterCount = getActiveFilterCount()

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Customer Search & Filters
            {activeFilterCount > 0 && (
              <Badge variant="default" className="ml-2">
                {activeFilterCount} active
              </Badge>
            )}
          </div>
          {activeFilterCount > 0 && (
            <Button variant="outline" size="sm" onClick={clearAllFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Main Search */}
        <div className="relative">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search customers, companies, emails, cities..."
              value={filters.searchTerm}
              onChange={(e) => {
                updateFilter("searchTerm", e.target.value)
                setShowSuggestions(true)
              }}
              onFocus={() => setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="pl-10"
            />
          </div>
          
          {/* Search Suggestions */}
          {showSuggestions && searchSuggestions.length > 0 && (
            <Card className="absolute top-full left-0 right-0 z-50 mt-1 shadow-lg">
              <CardContent className="p-2">
                {searchSuggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    className="w-full text-left px-3 py-2 hover:bg-accent rounded-md transition-colors text-sm"
                    onClick={() => {
                      updateFilter("searchTerm", suggestion)
                      setShowSuggestions(false)
                    }}
                  >
                    {suggestion}
                  </button>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          {filters.status.map(status => (
            <Badge key={status} variant="secondary" className="cursor-pointer">
              Status: {status}
              <X 
                className="h-3 w-3 ml-1" 
                onClick={() => removeArrayFilter("status", status)}
              />
            </Badge>
          ))}
          {filters.tier.map(tier => (
            <Badge key={tier} variant="secondary" className="cursor-pointer">
              <Star className="h-3 w-3 mr-1" />
              {tier}
              <X 
                className="h-3 w-3 ml-1" 
                onClick={() => removeArrayFilter("tier", tier)}
              />
            </Badge>
          ))}
          {filters.businessType.map(type => (
            <Badge key={type} variant="secondary" className="cursor-pointer">
              <Building2 className="h-3 w-3 mr-1" />
              {type}
              <X 
                className="h-3 w-3 ml-1" 
                onClick={() => removeArrayFilter("businessType", type)}
              />
            </Badge>
          ))}
          {filters.country.map(country => (
            <Badge key={country} variant="secondary" className="cursor-pointer">
              <MapPin className="h-3 w-3 mr-1" />
              {country}
              <X 
                className="h-3 w-3 ml-1" 
                onClick={() => removeArrayFilter("country", country)}
              />
            </Badge>
          ))}
        </div>

        {/* Advanced Filters Toggle */}
        <div className="flex items-center justify-between pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAdvancedOpen(!isAdvancedOpen)}
            className="flex items-center gap-2"
          >
            <SlidersHorizontal className="h-4 w-4" />
            Advanced Filters
            <ChevronDown className={cn("h-4 w-4 transition-transform", isAdvancedOpen && "rotate-180")} />
          </Button>
          
          <div className="text-sm text-muted-foreground">
            {filteredCustomers.length} of {customers.length} customers
          </div>
        </div>

        {/* Advanced Filters Panel */}
        {isAdvancedOpen && (
          <div className="space-y-4 pt-4 border-t animate-fade-in">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Status Filter */}
              <div className="space-y-2">
                <Label>Status</Label>
                <Select onValueChange={(value) => addArrayFilter("status", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add status filter" />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.statuses.map(status => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Tier Filter */}
              <div className="space-y-2">
                <Label>Customer Tier</Label>
                <Select onValueChange={(value) => addArrayFilter("tier", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add tier filter" />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.tiers.map(tier => tier && (
                      <SelectItem key={tier} value={tier}>
                        <div className="flex items-center gap-2">
                          <Star className="h-3 w-3" />
                          {tier}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Business Type Filter */}
              <div className="space-y-2">
                <Label>Business Type</Label>
                <Select onValueChange={(value) => addArrayFilter("businessType", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.businessTypes.map(type => type && (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Country Filter */}
              <div className="space-y-2">
                <Label>Country</Label>
                <Select onValueChange={(value) => addArrayFilter("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Add country filter" />
                  </SelectTrigger>
                  <SelectContent>
                    {filterOptions.countries.map(country => (
                      <SelectItem key={country} value={country}>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          {country}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Credit Limit Range */}
              <div className="space-y-2">
                <Label>Credit Limit (USD)</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.creditLimit.min || ""}
                    onChange={(e) => updateFilter("creditLimit", {
                      ...filters.creditLimit,
                      min: e.target.value ? Number(e.target.value) : undefined
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.creditLimit.max || ""}
                    onChange={(e) => updateFilter("creditLimit", {
                      ...filters.creditLimit,
                      max: e.target.value ? Number(e.target.value) : undefined
                    })}
                  />
                </div>
              </div>

              {/* Annual Volume Range */}
              <div className="space-y-2">
                <Label>Annual Volume (USD)</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.annualVolume.min || ""}
                    onChange={(e) => updateFilter("annualVolume", {
                      ...filters.annualVolume,
                      min: e.target.value ? Number(e.target.value) : undefined
                    })}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.annualVolume.max || ""}
                    onChange={(e) => updateFilter("annualVolume", {
                      ...filters.annualVolume,
                      max: e.target.value ? Number(e.target.value) : undefined
                    })}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
