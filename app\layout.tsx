import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import { Providers } from "./providers"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
  display: "swap",
})

export const metadata: Metadata = {
  title: "Nawras CRM",
  description: "Customer Relationship Management System for Nawras",
  // Force cache invalidation timestamp
  other: {
    'build-timestamp': Date.now().toString(),
    'cache-control': 'no-cache, no-store, must-revalidate'
  }
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  )
}