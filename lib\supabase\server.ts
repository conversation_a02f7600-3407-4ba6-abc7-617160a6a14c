import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '../../types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// ✅ CRITICAL FIX: Server-side client with conflict prevention
export const createClient = async () => {
  const cookieStore = await cookies()

  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return cookieStore.getAll()
      },
      setAll(cookiesToSet) {
        try {
          cookiesToSet.forEach(({ name, value, options }) =>
            cookieStore.set(name, value, options)
          )
        } catch {
          // The `setAll` method was called from a Server Component.
          // This can be ignored if you have middleware refreshing
          // user sessions.
        }
      },
    },
    auth: {
      persistSession: false, // ✅ CRITICAL: Disable session persistence on server
      autoRefreshToken: false, // ✅ CRITICAL: Disable auto-refresh on server
      detectSessionInUrl: false, // Server-side should not detect URL sessions
    },
    global: {
      headers: {
        'X-Client-Info': 'nawras-crm-server@2.0.0',
        'X-Server-Client': 'true', // ✅ DIAGNOSTIC: Mark as server client
      },
    },
  })
}
