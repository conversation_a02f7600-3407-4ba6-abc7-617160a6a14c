// Database Query Optimizations
export class DatabaseOptimizer {
  // Query caching with TTL
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  static async cachedQuery<T>(
    key: string,
    queryFn: () => Promise<T>,
    ttl: number = 300000 // 5 minutes default
  ): Promise<T> {
    const cached = this.cache.get(key)
    const now = Date.now()

    if (cached && (now - cached.timestamp) < cached.ttl) {
      return cached.data
    }

    const data = await queryFn()
    this.cache.set(key, { data, timestamp: now, ttl })

    // Clean up expired entries
    this.cleanupCache()

    return data
  }

  private static cleanupCache() {
    const now = Date.now()
    for (const [key, value] of Array.from(this.cache.entries())) {
      if ((now - value.timestamp) > value.ttl) {
        this.cache.delete(key)
      }
    }
  }

  // Optimized pagination
  static buildPaginatedQuery(
    baseQuery: string,
    page: number = 1,
    limit: number = 25,
    orderBy: string = 'created_at',
    orderDirection: 'asc' | 'desc' = 'desc'
  ) {
    const offset = (page - 1) * limit
    return `
      ${baseQuery}
      ORDER BY ${orderBy} ${orderDirection.toUpperCase()}
      LIMIT ${limit} OFFSET ${offset}
    `
  }

  // Batch operations for better performance
  static async batchInsert(
    tableName: string,
    records: any[],
    batchSize: number = 100
  ) {
    const batches = []
    for (let i = 0; i < records.length; i += batchSize) {
      batches.push(records.slice(i, i + batchSize))
    }

    const results: any[] = []
    for (const batch of batches) {
      const columns = Object.keys(batch[0])
      const values = batch.map(record => 
        columns.map(col => record[col])
      )

      const placeholders = values.map((_, index) => 
        `(${columns.map((_, colIndex) => `$${index * columns.length + colIndex + 1}`).join(', ')})`
      ).join(', ')

      const query = `
        INSERT INTO ${tableName} (${columns.join(', ')})
        VALUES ${placeholders}
        RETURNING *
      `

      const flatValues = values.flat()
      // Execute query with your database client
      // results.push(await db.query(query, flatValues))
    }

    return results
  }

  // Index suggestions for common queries
  static getIndexSuggestions() {
    return [
      // Customer table indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_email ON customers(email)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_company ON customers(company)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_status ON customers(status)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_created_at ON customers(created_at)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_search ON customers USING gin(to_tsvector(\'english\', contact_person || \' \' || company))',

      // Deals table indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_stage ON deals(stage)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_customer_id ON deals(customer_id)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_value ON deals(value)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_created_at ON deals(created_at)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_expected_close ON deals(expected_close_date)',

      // Composite indexes for common queries
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_customers_status_created ON customers(status, created_at)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_deals_stage_value ON deals(stage, value)',
    ]
  }
}

// API Response Optimization
export class APIOptimizer {
  // Response compression
  static compressResponse(data: any): string {
    return JSON.stringify(data, (key, value) => {
      // Remove null values to reduce payload size
      if (value === null) return undefined
      
      // Truncate long strings in development
      if (typeof value === 'string' && value.length > 1000) {
        return value.substring(0, 1000) + '...'
      }
      
      return value
    })
  }

  // Field selection for GraphQL-like behavior
  static selectFields<T extends object>(data: T[], fields?: string[]): Partial<T>[] {
    if (!fields || fields.length === 0) return data

    return data.map(item => {
      const selected: Partial<T> = {}
      fields.forEach(field => {
        if (field in item) {
          (selected as any)[field] = (item as any)[field]
        }
      })
      return selected
    })
  }

  // Response pagination metadata
  static buildPaginationMeta(
    total: number,
    page: number,
    limit: number
  ) {
    const totalPages = Math.ceil(total / limit)
    return {
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        nextPage: page < totalPages ? page + 1 : null,
        prevPage: page > 1 ? page - 1 : null
      }
    }
  }

  // Rate limiting helper
  private static rateLimitMap = new Map<string, { count: number; resetTime: number }>()

  static checkRateLimit(
    identifier: string,
    maxRequests: number = 100,
    windowMs: number = 60000 // 1 minute
  ): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now()
    const windowStart = now - windowMs
    
    let record = this.rateLimitMap.get(identifier)
    
    if (!record || record.resetTime < windowStart) {
      record = { count: 0, resetTime: now + windowMs }
      this.rateLimitMap.set(identifier, record)
    }

    if (record.count >= maxRequests) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: record.resetTime
      }
    }

    record.count++
    return {
      allowed: true,
      remaining: maxRequests - record.count,
      resetTime: record.resetTime
    }
  }
}

// Image Optimization Utilities
export class ImageOptimizer {
  // Generate responsive image URLs
  static generateResponsiveUrls(
    baseUrl: string,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
  ) {
    return sizes.map(size => ({
      size,
      url: `${baseUrl}?w=${size}&q=75&f=webp`,
      media: `(max-width: ${size}px)`
    }))
  }

  // Image lazy loading configuration
  static getLazyLoadingConfig() {
    return {
      loading: 'lazy' as const,
      placeholder: 'blur',
      blurDataURL: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
    }
  }
}

// Bundle Optimization Utilities
export class BundleOptimizer {
  // Dynamic import helper with error handling
  static async loadComponent<T>(
    importFn: () => Promise<{ default: T }>,
    fallback?: T
  ): Promise<T> {
    try {
      const module = await importFn()
      return module.default
    } catch (error) {
      console.error('Failed to load component:', error)
      if (fallback) return fallback
      throw error
    }
  }

  // Preload critical resources
  static preloadResource(href: string, as: string, type?: string) {
    if (typeof window === 'undefined') return

    const link = document.createElement('link')
    link.rel = 'preload'
    link.href = href
    link.as = as
    if (type) link.type = type
    
    document.head.appendChild(link)
  }

  // Prefetch next page resources
  static prefetchPage(href: string) {
    if (typeof window === 'undefined') return

    const link = document.createElement('link')
    link.rel = 'prefetch'
    link.href = href
    
    document.head.appendChild(link)
  }

  // Critical CSS inlining helper
  static inlineCriticalCSS(css: string) {
    if (typeof window === 'undefined') return

    const style = document.createElement('style')
    style.textContent = css
    document.head.appendChild(style)
  }
}

// Memory Management Utilities
export class MemoryOptimizer {
  // WeakMap for component cleanup
  private static componentCleanup = new WeakMap<object, () => void>()

  static registerCleanup(component: object, cleanup: () => void) {
    this.componentCleanup.set(component, cleanup)
  }

  static cleanup(component: object) {
    const cleanupFn = this.componentCleanup.get(component)
    if (cleanupFn) {
      cleanupFn()
      this.componentCleanup.delete(component)
    }
  }

  // Object pool for frequently created objects
  private static objectPools = new Map<string, any[]>()

  static getFromPool<T>(poolName: string, factory: () => T): T {
    let pool = this.objectPools.get(poolName)
    if (!pool) {
      pool = []
      this.objectPools.set(poolName, pool)
    }

    return pool.pop() || factory()
  }

  static returnToPool(poolName: string, object: any) {
    const pool = this.objectPools.get(poolName)
    if (pool && pool.length < 100) { // Limit pool size
      // Reset object properties if needed
      pool.push(object)
    }
  }

  // Memory usage monitoring
  static getMemoryUsage() {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return null
    }

    const memory = (performance as any).memory
    return {
      used: Math.round(memory.usedJSHeapSize / 1048576), // MB
      total: Math.round(memory.totalJSHeapSize / 1048576), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
      percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
    }
  }
}

// Performance Monitoring
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>()

  static startTiming(label: string): () => number {
    const start = performance.now()
    
    return () => {
      const duration = performance.now() - start
      this.recordMetric(label, duration)
      return duration
    }
  }

  static recordMetric(label: string, value: number) {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, [])
    }
    
    const values = this.metrics.get(label)!
    values.push(value)
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift()
    }
  }

  static getMetrics(label: string) {
    const values = this.metrics.get(label) || []
    if (values.length === 0) return null

    const sum = values.reduce((a, b) => a + b, 0)
    const avg = sum / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    const p95 = values.sort((a, b) => a - b)[Math.floor(values.length * 0.95)]

    return { avg, min, max, p95, count: values.length }
  }

  static getAllMetrics() {
    const result: Record<string, any> = {}
    for (const [label, _] of Array.from(this.metrics)) {
      result[label] = this.getMetrics(label)
    }
    return result
  }

  // Web Vitals reporting
  static reportWebVital(metric: { name: string; value: number; id: string }) {
    // Send to analytics service
    if (process.env.NODE_ENV === 'production') {
      // Example: send to Google Analytics, DataDog, etc.
      console.log('Web Vital:', metric)
    }
  }
}
