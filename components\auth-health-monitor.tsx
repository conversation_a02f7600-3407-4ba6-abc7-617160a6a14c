"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { supabase } from "@/lib/supabase"
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  RefreshCw,
  Activity,
  Clock
} from "lucide-react"

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'down'
  responseTime: number
  lastChecked: Date
  error?: string
}

export function AuthHealthMonitor() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus>({
    status: 'healthy',
    responseTime: 0,
    lastChecked: new Date()
  })
  const [isChecking, setIsChecking] = useState(false)
  const [autoCheck, setAutoCheck] = useState(true)

  const checkAuthHealth = async (): Promise<HealthStatus> => {
    const startTime = Date.now()
    
    try {
      // Test basic connectivity
      const { data, error } = await supabase.from('users').select('count').limit(1)
      const responseTime = Date.now() - startTime
      
      if (error) {
        return {
          status: 'degraded',
          responseTime,
          lastChecked: new Date(),
          error: error.message
        }
      }

      // Test authentication service specifically
      try {
        const { data: session, error: authError } = await Promise.race([
          supabase.auth.getSession(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Auth timeout')), 5000)
          )
        ]) as any

        if (authError && !authError.message.includes('No session')) {
          return {
            status: 'degraded',
            responseTime,
            lastChecked: new Date(),
            error: `Auth service: ${authError.message}`
          }
        }

        // Determine status based on response time
        const status = responseTime > 10000 ? 'degraded' : 'healthy'
        
        return {
          status,
          responseTime,
          lastChecked: new Date()
        }
      } catch (authError) {
        return {
          status: 'degraded',
          responseTime,
          lastChecked: new Date(),
          error: authError instanceof Error ? authError.message : 'Auth service timeout'
        }
      }
    } catch (error) {
      return {
        status: 'down',
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error instanceof Error ? error.message : 'Service unavailable'
      }
    }
  }

  const performHealthCheck = async () => {
    setIsChecking(true)
    try {
      const status = await checkAuthHealth()
      setHealthStatus(status)
    } catch (error) {
      setHealthStatus({
        status: 'down',
        responseTime: 0,
        lastChecked: new Date(),
        error: 'Health check failed'
      })
    } finally {
      setIsChecking(false)
    }
  }

  useEffect(() => {
    // Initial health check
    performHealthCheck()

    // Set up periodic health checks
    if (autoCheck) {
      const interval = setInterval(performHealthCheck, 30000) // Check every 30 seconds
      return () => clearInterval(interval)
    }
  }, [autoCheck])

  const getStatusIcon = () => {
    switch (healthStatus.status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'down':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusColor = () => {
    switch (healthStatus.status) {
      case 'healthy':
        return 'bg-green-500'
      case 'degraded':
        return 'bg-yellow-500'
      case 'down':
        return 'bg-red-500'
    }
  }

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Activity className="h-4 w-4" />
          Authentication Service Health
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Status Badge */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <Badge 
              variant={healthStatus.status === 'healthy' ? 'default' : 'destructive'}
              className={healthStatus.status === 'healthy' ? 'bg-green-500' : ''}
            >
              {healthStatus.status.toUpperCase()}
            </Badge>
          </div>
          
          <Button
            onClick={performHealthCheck}
            disabled={isChecking}
            size="sm"
            variant="outline"
          >
            {isChecking ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
          </Button>
        </div>

        {/* Metrics */}
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <div className="text-muted-foreground">Response Time</div>
            <div className="font-medium">
              {formatResponseTime(healthStatus.responseTime)}
            </div>
          </div>
          <div>
            <div className="text-muted-foreground">Last Checked</div>
            <div className="font-medium flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {healthStatus.lastChecked.toLocaleTimeString()}
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {healthStatus.error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {healthStatus.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Auto-check Toggle */}
        <div className="flex items-center justify-between text-xs">
          <span className="text-muted-foreground">Auto-monitoring</span>
          <Button
            onClick={() => setAutoCheck(!autoCheck)}
            size="sm"
            variant={autoCheck ? "default" : "outline"}
            className="h-6 px-2"
          >
            {autoCheck ? "ON" : "OFF"}
          </Button>
        </div>

        {/* Recommendations */}
        {healthStatus.status !== 'healthy' && (
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>Recommendations:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              {healthStatus.status === 'degraded' && (
                <>
                  <li>Service is slow but functional</li>
                  <li>Consider using bypass mode for testing</li>
                </>
              )}
              {healthStatus.status === 'down' && (
                <>
                  <li>Service is currently unavailable</li>
                  <li>Use emergency bypass for immediate access</li>
                  <li>Check Supabase status page</li>
                </>
              )}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
