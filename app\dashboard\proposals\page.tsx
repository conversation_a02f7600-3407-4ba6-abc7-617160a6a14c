"use client"

import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { Plus } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Search, Filter, FileText, Calendar, DollarSign, Eye, Edit, Trash2 } from "lucide-react"
import { useLanguage } from "@/components/language-provider"
import { useAuth } from "@/components/auth-provider"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import { format } from "date-fns"
import { useOptimizedData } from "@/hooks/use-optimized-data"

interface Proposal {
  id: string
  title: string
  client_name: string
  value: number
  currency: string
  status: string
  created_at: string
  valid_until: string
  description: string
}

export default function ProposalsPage() {
  console.log("🚀 PROPOSALS PAGE LOADED - SIMPLIFIED AUTH - " + new Date().toISOString())

  const { t } = useLanguage()
  const { user } = useAuth()
  const { toast } = useToast()

  // Get update and remove functions from optimized data hook
  const { update, remove } = useOptimizedData<Proposal>({
    table: "proposals",
    requiresAuth: true,
  })

  // ✅ SIMPLIFIED: Direct data management like User Management
  const [proposals, setProposals] = useState<Proposal[]>([])
  const [loading, setLoading] = useState(true)

  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false)
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isClient, setIsClient] = useState(false)

  const [formData, setFormData] = useState({
    title: "",
    client_name: "",
    amount: "",
    currency: "USD",
    status: "draft",
    valid_until: "",
    description: "",
  })

  // ✅ SIMPLIFIED: Direct data fetching pattern like User Management
  const fetchProposals = useCallback(async () => {
    if (!user) return

    try {
      setLoading(true)
      console.log('🔄 [PROPOSALS] Fetching proposals directly from Supabase...')

      const { data, error } = await supabase
        .from('proposals')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error

      setProposals(data || [])
      console.log('✅ [PROPOSALS] Proposals fetched successfully:', data?.length || 0)
    } catch (error: any) {
      console.error('❌ [PROPOSALS] Error fetching proposals:', error)
      toast({
        title: "Error",
        description: "Failed to load proposals. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }, [user, toast])

  // ✅ SIMPLIFIED: Fetch proposals when user is available (like User Management)
  useEffect(() => {
    if (user) {
      fetchProposals()
    }
  }, [user, fetchProposals])

  useEffect(() => {
    setIsClient(true)
  }, [])

  // ✅ SIMPLIFIED: Direct create function like User Management
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim() || !formData.client_name.trim() || !formData.amount.trim()) {
      toast({
        title: t("Error"),
        description: t("Please fill in all required fields"),
        variant: "destructive",
      })
      return
    }

    if (!user) {
      toast({
        title: t("Error"),
        description: t("You must be logged in to create proposals"),
        variant: "destructive",
      })
      return
    }

    try {
      const proposalData = {
        title: formData.title,
        client_name: formData.client_name,
        value: parseFloat(formData.amount),
        currency: formData.currency,
        status: formData.status,
        valid_until: formData.valid_until || "",
        description: formData.description,
        user_id: user.id,
      }

      const { data, error } = await supabase
        .from('proposals')
        .insert([proposalData])
        .select()
        .single()

      if (error) throw error

      console.log("✅ Proposal created successfully:", data)
      toast({
        title: t("Success"),
        description: t("Proposal created successfully"),
      })

      setIsAddDialogOpen(false)
      setFormData({
        title: "",
        client_name: "",
        amount: "",
        currency: "USD",
        status: "draft",
        valid_until: "",
        description: "",
      })
      fetchProposals() // Refresh the list
    } catch (error: any) {
      console.error("❌ Error creating proposal:", error)
      toast({
        title: t("Error"),
        description: t("Failed to create proposal"),
        variant: "destructive",
      })
    }
  }

  const handleEdit = (proposal: Proposal) => {
    setSelectedProposal(proposal)
    setFormData({
      title: proposal.title,
      client_name: proposal.client_name,
      amount: proposal.value.toString(),
      currency: proposal.currency,
      status: proposal.status,
      valid_until: proposal.valid_until,
      description: proposal.description,
    })
    setIsEditDialogOpen(true)
  }

  const handleView = (proposal: Proposal) => {
    setSelectedProposal(proposal)
    setIsViewDialogOpen(true)
  }

  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!selectedProposal) return

    const proposalData = {
      title: formData.title,
      client_name: formData.client_name,
      value: parseFloat(formData.amount),
      currency: formData.currency,
      status: formData.status,
      valid_until: formData.valid_until,
      description: formData.description,
    }

    const result = await update(selectedProposal.id, proposalData)

    if (result) {
      setIsEditDialogOpen(false)
      setSelectedProposal(null)
      setFormData({
        title: "",
        client_name: "",
        amount: "",
        currency: "USD",
        status: "draft",
        valid_until: "",
        description: "",
      })
      toast({
        title: t("Success"),
        description: t("Proposal updated successfully"),
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm(t("Are you sure you want to delete this proposal?"))) {
      return
    }

    // Fix: Pass table name as second argument to match deployed version
    const result = await remove(id, "proposals")
    if (result) {
      toast({
        title: t("Success"),
        description: t("Proposal deleted successfully"),
      })
    }
  }

  const filteredProposals = (proposals || []).filter((proposal) => {
    const matchesSearch = (proposal.title || "").toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (proposal.client_name || "").toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || proposal.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: "Draft", variant: "secondary" as const },
      sent: { label: "Sent", variant: "default" as const },
      accepted: { label: "Accepted", variant: "default" as const },
      rejected: { label: "Rejected", variant: "destructive" as const },
    }
    return statusConfig[status as keyof typeof statusConfig] || statusConfig.draft
  }

  const totalValue = filteredProposals.reduce((sum, proposal) => sum + proposal.value, 0)
  const acceptedValue = filteredProposals
    .filter(p => p.status === "accepted")
    .reduce((sum, proposal) => sum + proposal.value, 0)

  if (!isClient) {
    return <div>Loading...</div>
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <FileText className="h-8 w-8 text-blue-600" />
            {t("Proposals")}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">{t("Manage your business proposals")}</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="gap-2 bg-teal-600 hover:bg-teal-700">
              <Plus className="h-4 w-4" />
              {t("New Proposal")}
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{t("Create New Proposal")}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">{t("Title")} *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    placeholder={t("Enter proposal title")}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="client_name">{t("Client Name")} *</Label>
                  <Input
                    id="client_name"
                    value={formData.client_name}
                    onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                    placeholder={t("Enter client name")}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="amount">{t("Amount")} *</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    placeholder="0.00"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="currency">{t("Currency")}</Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData({ ...formData, currency: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="valid_until">{t("Valid Until")}</Label>
                <Input
                  id="valid_until"
                  type="date"
                  value={formData.valid_until}
                  onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">{t("Description")}</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder={t("Enter proposal description")}
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  {t("Cancel")}
                </Button>
                <Button type="submit">{t("Create Proposal")}</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Proposal Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{t("Edit Proposal")}</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleUpdate} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-title">{t("Title")}</Label>
                  <Input
                    id="edit-title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit-client">{t("Client Name")}</Label>
                  <Input
                    id="edit-client"
                    value={formData.client_name}
                    onChange={(e) => setFormData({ ...formData, client_name: e.target.value })}
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-amount">{t("Amount")}</Label>
                  <Input
                    id="edit-amount"
                    type="number"
                    value={formData.amount}
                    onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit-currency">{t("Currency")}</Label>
                  <Select value={formData.currency} onValueChange={(value) => setFormData({ ...formData, currency: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                      <SelectItem value="JOD">JOD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-status">{t("Status")}</Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({ ...formData, status: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="accepted">Accepted</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-valid-until">{t("Valid Until")}</Label>
                  <Input
                    id="edit-valid-until"
                    type="date"
                    value={formData.valid_until}
                    onChange={(e) => setFormData({ ...formData, valid_until: e.target.value })}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-description">{t("Description")}</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  {t("Cancel")}
                </Button>
                <Button type="submit" className="bg-teal-600 hover:bg-teal-700">
                  {t("Update Proposal")}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* View Proposal Dialog */}
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>{t("View Proposal")}</DialogTitle>
            </DialogHeader>
            {selectedProposal && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Title")}</Label>
                    <p className="text-sm">{selectedProposal.title}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Client Name")}</Label>
                    <p className="text-sm">{selectedProposal.client_name}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Value")}</Label>
                    <p className="text-sm">{selectedProposal.currency} {selectedProposal.value.toLocaleString()}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Status")}</Label>
                    <Badge variant={getStatusBadge(selectedProposal.status).variant}>
                      {getStatusBadge(selectedProposal.status).label}
                    </Badge>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Valid Until")}</Label>
                    <p className="text-sm">
                      {selectedProposal.valid_until ? format(new Date(selectedProposal.valid_until), "MMM dd, yyyy") : "-"}
                    </p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Created")}</Label>
                    <p className="text-sm">{format(new Date(selectedProposal.created_at), "MMM dd, yyyy")}</p>
                  </div>
                </div>
                {selectedProposal.description && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t("Description")}</Label>
                    <p className="text-sm">{selectedProposal.description}</p>
                  </div>
                )}
                <div className="flex justify-end">
                  <Button onClick={() => setIsViewDialogOpen(false)}>
                    {t("Close")}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Proposals</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredProposals.length}</div>
            <p className="text-xs text-muted-foreground">
              Active proposals
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Combined value
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Accepted Value</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${acceptedValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Won proposals
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder={t("Search proposals...")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="accepted">Accepted</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Proposals Table */}
      <Card>
        <CardHeader>
          <CardTitle>Proposals ({filteredProposals.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading proposals...</div>
          ) : filteredProposals.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No proposals found</p>
              <p className="text-sm">Create your first proposal to get started</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Valid Until</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProposals.map((proposal) => (
                  <TableRow key={proposal.id}>
                    <TableCell className="font-medium">{proposal.title}</TableCell>
                    <TableCell>{proposal.client_name}</TableCell>
                    <TableCell>
                      {proposal.currency} {proposal.value.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant={getStatusBadge(proposal.status).variant}>
                        {getStatusBadge(proposal.status).label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {proposal.valid_until ? format(new Date(proposal.valid_until), "MMM dd, yyyy") : "-"}
                    </TableCell>
                    <TableCell>
                      {format(new Date(proposal.created_at), "MMM dd, yyyy")}
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleView(proposal)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(proposal)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(proposal.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}