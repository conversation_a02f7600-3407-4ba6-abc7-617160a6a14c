"use client"

import * as React from "react"
import { useState, useMemo } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CustomerFormData } from "@/app/types/customer"
import { 
  TrendingUp, TrendingDown, AlertTriangle, CheckCircle,
  Users, DollarSign, Star, Target, Award, Zap,
  ArrowUpRight, ArrowDownRight, Lightbulb, Flag,
  Calendar, MapPin, Building2, CreditCard
} from "lucide-react"
import { cn } from "@/lib/utils"

// Insight Types
export interface CustomerInsight {
  id: string
  type: "opportunity" | "risk" | "trend" | "recommendation"
  priority: "high" | "medium" | "low"
  title: string
  description: string
  impact: "revenue" | "retention" | "efficiency" | "growth"
  value?: number
  customers?: CustomerFormData[]
  actionable: boolean
  category: string
  confidence: number
}

export interface InsightMetrics {
  totalOpportunities: number
  totalRisks: number
  potentialRevenue: number
  customersAtRisk: number
  actionableInsights: number
  confidenceScore: number
}

// Customer Insights Generator
export function generateCustomerInsights(customers: CustomerFormData[]): {
  insights: CustomerInsight[]
  metrics: InsightMetrics
} {
  const insights: CustomerInsight[] = []
  
  // Calculate baseline metrics
  const totalRevenue = customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
  const avgRevenue = customers.length > 0 ? totalRevenue / customers.length : 0
  
  // 1. Tier Upgrade Opportunities
  const bronzeCustomers = customers.filter(c => 
    c.customer_tier === "Bronze" && (c.annual_volume || 0) > avgRevenue * 0.8
  )
  
  if (bronzeCustomers.length > 0) {
    insights.push({
      id: "tier-upgrade-opportunity",
      type: "opportunity",
      priority: "high",
      title: "Tier Upgrade Opportunities",
      description: `${bronzeCustomers.length} Bronze customers show potential for tier upgrades based on their volume`,
      impact: "revenue",
      value: bronzeCustomers.reduce((sum, c) => sum + (c.annual_volume || 0), 0) * 0.2,
      customers: bronzeCustomers.slice(0, 5),
      actionable: true,
      category: "Customer Development",
      confidence: 85
    })
  }

  // 2. Geographic Expansion Opportunities
  const countryRevenue = customers.reduce((acc, customer) => {
    const country = customer.country || "Unknown"
    acc[country] = (acc[country] || 0) + (customer.annual_volume || 0)
    return acc
  }, {} as Record<string, number>)
  
  const topCountries = Object.entries(countryRevenue)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 3)
  
  if (topCountries.length > 0) {
    insights.push({
      id: "geographic-expansion",
      type: "opportunity",
      priority: "medium",
      title: "Geographic Expansion Potential",
      description: `Top performing regions: ${topCountries.map(([country]) => country).join(", ")}`,
      impact: "growth",
      value: topCountries.reduce((sum, [, revenue]) => sum + revenue, 0) * 0.15,
      actionable: true,
      category: "Market Expansion",
      confidence: 72
    })
  }

  // 3. Payment Risk Analysis
  const riskCustomers = customers.filter(c => 
    c.payment_terms === "90 Days" || c.credit_limit && (c.annual_volume || 0) > (c.credit_limit * 0.8)
  )
  
  if (riskCustomers.length > 0) {
    insights.push({
      id: "payment-risk",
      type: "risk",
      priority: "high",
      title: "Payment Risk Alert",
      description: `${riskCustomers.length} customers have extended payment terms or high credit utilization`,
      impact: "retention",
      customers: riskCustomers.slice(0, 5),
      actionable: true,
      category: "Financial Risk",
      confidence: 90
    })
  }

  // 4. Industry Concentration Risk
  const industryDistribution = customers.reduce((acc, customer) => {
    const industry = customer.industry || "Other"
    acc[industry] = (acc[industry] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const topIndustry = Object.entries(industryDistribution)
    .sort(([,a], [,b]) => b - a)[0]
  
  if (topIndustry && topIndustry[1] > customers.length * 0.4) {
    insights.push({
      id: "industry-concentration",
      type: "risk",
      priority: "medium",
      title: "Industry Concentration Risk",
      description: `${((topIndustry[1] / customers.length) * 100).toFixed(1)}% of customers are in ${topIndustry[0]} industry`,
      impact: "retention",
      actionable: true,
      category: "Portfolio Risk",
      confidence: 78
    })
  }

  // 5. Inactive Customer Reactivation
  const inactiveCustomers = customers.filter(c => c.status === "Inactive")
  
  if (inactiveCustomers.length > 0) {
    insights.push({
      id: "reactivation-opportunity",
      type: "opportunity",
      priority: "medium",
      title: "Customer Reactivation Potential",
      description: `${inactiveCustomers.length} inactive customers could be reactivated`,
      impact: "revenue",
      value: inactiveCustomers.reduce((sum, c) => sum + (c.annual_volume || 0), 0) * 0.3,
      customers: inactiveCustomers.slice(0, 5),
      actionable: true,
      category: "Customer Retention",
      confidence: 65
    })
  }

  // 6. High-Value Customer Retention
  const highValueCustomers = customers
    .filter(c => (c.annual_volume || 0) > avgRevenue * 2)
    .filter(c => !c.account_manager)
  
  if (highValueCustomers.length > 0) {
    insights.push({
      id: "high-value-retention",
      type: "recommendation",
      priority: "high",
      title: "Assign Account Managers",
      description: `${highValueCustomers.length} high-value customers lack dedicated account managers`,
      impact: "retention",
      customers: highValueCustomers.slice(0, 5),
      actionable: true,
      category: "Account Management",
      confidence: 88
    })
  }

  // 7. Currency Diversification
  const currencyDistribution = customers.reduce((acc, customer) => {
    const currency = customer.currency_preference || "USD"
    acc[currency] = (acc[currency] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const usdPercentage = (currencyDistribution.USD || 0) / customers.length
  
  if (usdPercentage > 0.8) {
    insights.push({
      id: "currency-diversification",
      type: "recommendation",
      priority: "low",
      title: "Currency Diversification",
      description: `${(usdPercentage * 100).toFixed(1)}% of customers use USD - consider multi-currency strategy`,
      impact: "growth",
      actionable: true,
      category: "Risk Management",
      confidence: 60
    })
  }

  // 8. Shipping Method Optimization
  const shippingCosts = customers.reduce((acc, customer) => {
    const method = customer.preferred_shipping_method || "Ground"
    acc[method] = (acc[method] || 0) + 1
    return acc
  }, {} as Record<string, number>)
  
  const airShippingPercentage = (shippingCosts.Air || 0) / customers.length
  
  if (airShippingPercentage > 0.6) {
    insights.push({
      id: "shipping-optimization",
      type: "recommendation",
      priority: "medium",
      title: "Shipping Cost Optimization",
      description: `${(airShippingPercentage * 100).toFixed(1)}% of customers use air shipping - potential cost savings`,
      impact: "efficiency",
      actionable: true,
      category: "Operations",
      confidence: 70
    })
  }

  // Calculate metrics
  const metrics: InsightMetrics = {
    totalOpportunities: insights.filter(i => i.type === "opportunity").length,
    totalRisks: insights.filter(i => i.type === "risk").length,
    potentialRevenue: insights
      .filter(i => i.value)
      .reduce((sum, i) => sum + (i.value || 0), 0),
    customersAtRisk: riskCustomers.length + inactiveCustomers.length,
    actionableInsights: insights.filter(i => i.actionable).length,
    confidenceScore: insights.length > 0 
      ? Math.round(insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length)
      : 0
  }

  return { insights, metrics }
}

// Customer Insights Component
interface CustomerInsightsProps {
  customers: CustomerFormData[]
  className?: string
  onInsightAction?: (insight: CustomerInsight) => void
}

export function CustomerInsights({
  customers,
  className,
  onInsightAction
}: CustomerInsightsProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedPriority, setSelectedPriority] = useState<string>("all")

  const { insights, metrics } = useMemo(() => {
    return generateCustomerInsights(customers)
  }, [customers])

  const filteredInsights = useMemo(() => {
    return insights.filter(insight => {
      const categoryMatch = selectedCategory === "all" || insight.category === selectedCategory
      const priorityMatch = selectedPriority === "all" || insight.priority === selectedPriority
      return categoryMatch && priorityMatch
    })
  }, [insights, selectedCategory, selectedPriority])

  const categories = useMemo(() => {
    const cats = Array.from(new Set(insights.map(i => i.category)))
    return ["all", ...cats]
  }, [insights])

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "opportunity": return <TrendingUp className="h-4 w-4 text-green-600" />
      case "risk": return <AlertTriangle className="h-4 w-4 text-red-600" />
      case "trend": return <TrendingDown className="h-4 w-4 text-blue-600" />
      case "recommendation": return <Lightbulb className="h-4 w-4 text-yellow-600" />
      default: return <Flag className="h-4 w-4 text-gray-600" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800"
      case "medium": return "bg-yellow-100 text-yellow-800"
      case "low": return "bg-green-100 text-green-800"
      default: return "bg-gray-100 text-gray-800"
    }
  }

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case "revenue": return <DollarSign className="h-3 w-3" />
      case "retention": return <Users className="h-3 w-3" />
      case "efficiency": return <Zap className="h-3 w-3" />
      case "growth": return <Target className="h-3 w-3" />
      default: return <Star className="h-3 w-3" />
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-6 w-6 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">{metrics.totalOpportunities}</div>
            <p className="text-xs text-muted-foreground">Opportunities</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <AlertTriangle className="h-6 w-6 mx-auto mb-2 text-red-600" />
            <div className="text-2xl font-bold">{metrics.totalRisks}</div>
            <p className="text-xs text-muted-foreground">Risks</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <DollarSign className="h-6 w-6 mx-auto mb-2 text-blue-600" />
            <div className="text-lg font-bold">${Math.round(metrics.potentialRevenue / 1000)}K</div>
            <p className="text-xs text-muted-foreground">Potential Revenue</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Users className="h-6 w-6 mx-auto mb-2 text-orange-600" />
            <div className="text-2xl font-bold">{metrics.customersAtRisk}</div>
            <p className="text-xs text-muted-foreground">At Risk</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-6 w-6 mx-auto mb-2 text-purple-600" />
            <div className="text-2xl font-bold">{metrics.actionableInsights}</div>
            <p className="text-xs text-muted-foreground">Actionable</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Award className="h-6 w-6 mx-auto mb-2 text-indigo-600" />
            <div className="text-2xl font-bold">{metrics.confidenceScore}%</div>
            <p className="text-xs text-muted-foreground">Confidence</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Category:</label>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="text-sm border rounded px-2 py-1"
          >
            {categories.map(cat => (
              <option key={cat} value={cat}>
                {cat === "all" ? "All Categories" : cat}
              </option>
            ))}
          </select>
        </div>

        <div className="flex items-center gap-2">
          <label className="text-sm font-medium">Priority:</label>
          <select
            value={selectedPriority}
            onChange={(e) => setSelectedPriority(e.target.value)}
            className="text-sm border rounded px-2 py-1"
          >
            <option value="all">All Priorities</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      {/* Insights List */}
      <div className="space-y-4">
        {filteredInsights.length === 0 ? (
          <Alert>
            <Lightbulb className="h-4 w-4" />
            <AlertDescription>
              No insights match your current filters. Try adjusting the category or priority filters.
            </AlertDescription>
          </Alert>
        ) : (
          filteredInsights.map((insight) => (
            <Card key={insight.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {getInsightIcon(insight.type)}
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-semibold">{insight.title}</h4>
                        <Badge className={getPriorityColor(insight.priority)}>
                          {insight.priority}
                        </Badge>
                        <Badge variant="outline" className="gap-1">
                          {getImpactIcon(insight.impact)}
                          {insight.impact}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {insight.description}
                      </p>
                      
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>Category: {insight.category}</span>
                        <span>Confidence: {insight.confidence}%</span>
                        {insight.value && (
                          <span>Value: ${Math.round(insight.value).toLocaleString()}</span>
                        )}
                      </div>
                      
                      {insight.customers && insight.customers.length > 0 && (
                        <div className="mt-3">
                          <p className="text-xs font-medium mb-2">Affected Customers:</p>
                          <div className="flex flex-wrap gap-1">
                            {insight.customers.slice(0, 3).map((customer) => (
                              <Badge key={customer.company} variant="secondary" className="text-xs">
                                {customer.company}
                              </Badge>
                            ))}
                            {insight.customers.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{insight.customers.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex flex-col items-end gap-2">
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">Confidence</div>
                      <Progress value={insight.confidence} className="w-16 h-1" />
                    </div>
                    
                    {insight.actionable && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onInsightAction?.(insight)}
                        className="gap-1"
                      >
                        <ArrowUpRight className="h-3 w-3" />
                        Take Action
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
