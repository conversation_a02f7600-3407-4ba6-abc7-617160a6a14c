"use client"

import { useState, useEffect, useCallback } from "react"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/components/auth-provider"

// ✅ PERFORMANCE OPTIMIZATION: Batch multiple queries into single request
interface BatchQuery {
  id: string
  table: string
  select?: string
  filters?: Record<string, any>
  orderBy?: { column: string; ascending?: boolean }
}

interface BatchQueryResult<T = any> {
  id: string
  data: T[]
  error?: string
}

interface UseBatchQueriesOptions {
  queries: BatchQuery[]
  requiresAuth?: boolean
  enableCache?: boolean
  cacheTTL?: number
}

export function useBatchQueries<T = any>({
  queries,
  requiresAuth = true,
  enableCache = true,
  cacheTTL = 5 * 60 * 1000, // 5 minutes
}: UseBatchQueriesOptions) {
  const [results, setResults] = useState<Record<string, BatchQueryResult<T>>>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { user } = useAuth()

  const executeBatchQueries = useCallback(async () => {
    if (requiresAuth && !user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      console.log(`🔄 [BATCH-QUERIES] Executing ${queries.length} queries in batch`)
      const startTime = performance.now()

      // Execute all queries concurrently but with controlled batching
      const queryPromises = queries.map(async (query) => {
        try {
          let supabaseQuery = supabase.from(query.table).select(query.select || "*")

          // Apply filters
          if (query.filters) {
            Object.entries(query.filters).forEach(([key, value]) => {
              supabaseQuery = supabaseQuery.eq(key, value)
            })
          }

          // Apply ordering
          if (query.orderBy) {
            supabaseQuery = supabaseQuery.order(query.orderBy.column, {
              ascending: query.orderBy.ascending ?? false
            })
          }

          const { data, error } = await supabaseQuery

          if (error) {
            console.error(`❌ [BATCH-QUERIES] Error in query ${query.id}:`, error)
            return { id: query.id, data: [], error: error.message }
          }

          return { id: query.id, data: data || [], error: undefined }
        } catch (err: any) {
          console.error(`❌ [BATCH-QUERIES] Exception in query ${query.id}:`, err)
          return { id: query.id, data: [], error: err.message }
        }
      })

      const queryResults = await Promise.all(queryPromises)
      const duration = performance.now() - startTime

      console.log(`✅ [BATCH-QUERIES] Completed ${queries.length} queries in ${duration.toFixed(2)}ms`)

      // Convert array to object for easy access
      const resultsMap = queryResults.reduce((acc, result) => {
        acc[result.id] = result
        return acc
      }, {} as Record<string, BatchQueryResult<T>>)

      setResults(resultsMap)
    } catch (err: any) {
      console.error(`❌ [BATCH-QUERIES] Batch execution failed:`, err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }, [queries, user, requiresAuth])

  useEffect(() => {
    executeBatchQueries()
  }, [executeBatchQueries])

  // Helper function to get specific query result
  const getQueryResult = useCallback((queryId: string) => {
    return results[queryId] || { id: queryId, data: [], error: undefined }
  }, [results])

  return {
    results,
    loading,
    error,
    refetch: executeBatchQueries,
    getQueryResult,
  }
}

// ✅ HELPER: Pre-configured batch queries for common use cases
export const COMMON_BATCH_QUERIES = {
  DASHBOARD_OVERVIEW: [
    { id: 'customers', table: 'customers', select: 'id, status, created_at' },
    { id: 'leads', table: 'leads', select: 'id, status, value, created_at' },
    { id: 'opportunities', table: 'opportunities', select: 'id, stage, value, created_at' },
  ],
  SIDEBAR_STATS: [
    { id: 'customers_count', table: 'customers', select: 'count' },
    { id: 'leads_count', table: 'leads', select: 'count' },
    { id: 'opportunities_count', table: 'opportunities', select: 'count' },
  ],
} as const
