import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { Database } from '../../types/database'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll()
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value, options }) => {
          request.cookies.set(name, value)
          supabaseResponse.cookies.set(name, value, options)
        })
      },
    },
  })

  // ✅ UNIFIED SESSION VALIDATION: Check session from cookies instead of Supabase auth
  // This avoids server/client session conflicts by using the same session storage
  const sessionValid = validateSessionFromCookies(request)

  // Protect dashboard routes with cookie-based session validation
  if (request.nextUrl.pathname.startsWith('/dashboard') && !sessionValid) {
    console.log('🔒 MIDDLEWARE: No valid session found, redirecting to login')
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  // Redirect authenticated users away from login page
  if (request.nextUrl.pathname === '/login' && sessionValid) {
    console.log('✅ MIDDLEWARE: Valid session found, redirecting to dashboard')
    const url = request.nextUrl.clone()
    url.pathname = '/dashboard'
    return NextResponse.redirect(url)
  }

  if (sessionValid) {
    console.log('✅ MIDDLEWARE: Valid session found, allowing access')
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is. If you're
  // creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}

// ✅ COOKIE-BASED SESSION VALIDATION: Validate session from cookies
function validateSessionFromCookies(request: NextRequest): boolean {
  try {
    const sessionCookie = request.cookies.get('nawras_session')
    if (!sessionCookie?.value) {
      console.log('🔍 MIDDLEWARE: No session cookie found')
      return false
    }

    // Decode and parse cookie data
    const cookieData = JSON.parse(atob(sessionCookie.value))

    // Check if session has required fields
    if (!cookieData.user_id || !cookieData.email) {
      console.log('🔍 MIDDLEWARE: Invalid session data in cookie')
      return false
    }

    // Check if session is still valid (not expired)
    const now = Math.floor(Date.now() / 1000)
    if (cookieData.expires_at && now > cookieData.expires_at) {
      console.log('🔍 MIDDLEWARE: Session cookie expired')
      return false
    }

    // Check if session is not too old (within 24 hours)
    const sessionAge = Date.now() - cookieData.timestamp
    if (sessionAge > 24 * 60 * 60 * 1000) {
      console.log('🔍 MIDDLEWARE: Session cookie too old')
      return false
    }

    console.log('✅ MIDDLEWARE: Session cookie validation successful')
    return true
  } catch (error) {
    console.log('⚠️ MIDDLEWARE: Error validating session cookie:', error)
    return false
  }
}
