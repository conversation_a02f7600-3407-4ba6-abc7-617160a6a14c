'use client'

import React from 'react'
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { useCustomerDistributionData } from '@/hooks/useChartData'
import { formatters } from '@/components/charts/chart-config'
import { ChartProps } from '@/components/charts/types'
import { Users, Crown, Award } from 'lucide-react'

interface CustomerDistributionChartProps extends Omit<ChartProps, 'data'> {
  chartType?: 'pie' | 'donut' | 'bar'
  showPercentages?: boolean
}

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{data.category} Tier</p>
        <p className="text-sm text-muted-foreground">
          Customers: <span className="font-medium text-foreground">{data.count}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Percentage: <span className="font-medium text-foreground">{formatters.percentage(data.percentage)}</span>
        </p>
      </div>
    )
  }
  return null
}

const RADIAN = Math.PI / 180
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, category }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5
  const x = cx + radius * Math.cos(-midAngle * RADIAN)
  const y = cy + radius * Math.sin(-midAngle * RADIAN)

  return percent > 0.05 ? (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={11}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  ) : null
}

const getTierIcon = (tier: string) => {
  switch (tier.toLowerCase()) {
    case 'platinum':
    case 'vip':
      return <Crown className="h-4 w-4" />
    case 'gold':
      return <Award className="h-4 w-4" />
    default:
      return <Users className="h-4 w-4" />
  }
}

export function CustomerDistributionChart({ 
  config,
  loading: externalLoading,
  error,
  className = "",
  title = "Customer Distribution",
  subtitle = "Customers by tier and category",
  chartType = 'donut',
  showPercentages = true
}: CustomerDistributionChartProps) {
  const { data, loading: dataLoading } = useCustomerDistributionData()
  const isLoading = externalLoading || dataLoading

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>Error loading customer data: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>No customer data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate summary metrics
  const totalCustomers = data.reduce((sum, item) => sum + item.count, 0)
  const premiumCustomers = data
    .filter(item => ['Gold', 'Platinum', 'VIP'].includes(item.category))
    .reduce((sum, item) => sum + item.count, 0)
  const premiumPercentage = totalCustomers > 0 ? (premiumCustomers / totalCustomers) * 100 : 0
  const largestSegment = data.reduce((largest, current) => 
    current.count > largest.count ? current : largest
  )

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={config?.height || 300}>
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="category" 
                tick={{ fontSize: 12 }}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )
      
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={config?.height || 300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={showPercentages ? renderCustomizedLabel : false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        )
      
      default: // donut
        return (
          <ResponsiveContainer width="100%" height={config?.height || 300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={showPercentages ? renderCustomizedLabel : false}
                outerRadius={100}
                innerRadius={60}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            {title}
          </div>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Crown className="h-3 w-3" />
            {formatters.percentage(premiumPercentage)} Premium
          </Badge>
        </CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="flex-1">
            {renderChart()}
          </div>
          
          {/* Customer tier breakdown */}
          <div className="lg:w-64 space-y-3">
            <h4 className="font-medium text-sm text-muted-foreground">Customer Tiers</h4>
            {data.map((item, index) => (
              <div key={item.category} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  />
                  <div className="flex items-center gap-1">
                    {getTierIcon(item.category)}
                    <span className="text-sm font-medium">{item.category}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold">{item.count}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatters.percentage(item.percentage)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">Total Customers</p>
            <p className="font-semibold text-lg">{formatters.number(totalCustomers)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Premium Customers</p>
            <p className="font-semibold text-lg text-yellow-600">{formatters.number(premiumCustomers)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Premium Rate</p>
            <p className="font-semibold text-lg">{formatters.percentage(premiumPercentage)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Largest Segment</p>
            <p className="font-semibold text-lg">{largestSegment.category}</p>
            <p className="text-xs text-muted-foreground">
              {largestSegment.count} customers
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
