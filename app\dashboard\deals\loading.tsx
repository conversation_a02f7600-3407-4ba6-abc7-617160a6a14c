export default function DealsLoading() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between">
        <div>
          <div className="h-8 w-48 bg-gray-200 rounded animate-pulse mb-2" />
          <div className="h-4 w-64 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
      </div>

      {/* Filters Skeleton */}
      <div className="flex gap-4 items-center">
        <div className="h-10 w-48 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 w-40 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 flex-1 max-w-sm bg-gray-200 rounded animate-pulse" />
      </div>

      {/* Kanban Board Skeleton */}
      <div className="flex gap-6 overflow-x-auto pb-4">
        {[1, 2, 3, 4, 5].map((i) => (
          <div key={i} className="flex-shrink-0 w-80">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse" />
                <div className="h-5 w-24 bg-gray-300 rounded animate-pulse" />
                <div className="h-5 w-8 bg-gray-300 rounded animate-pulse" />
              </div>

              <div className="space-y-3">
                {[1, 2, 3].map((j) => (
                  <div key={j} className="bg-white rounded-lg p-4 border">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <div className="flex-1">
                          <div className="h-4 w-32 bg-gray-200 rounded animate-pulse mb-2" />
                          <div className="h-3 w-24 bg-gray-200 rounded animate-pulse" />
                        </div>
                        <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
                      </div>
                      <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-gray-200 rounded-full animate-pulse" />
                        <div className="h-3 w-16 bg-gray-200 rounded animate-pulse" />
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="h-3 w-12 bg-gray-200 rounded animate-pulse" />
                          <div className="h-3 w-8 bg-gray-200 rounded animate-pulse" />
                        </div>
                        <div className="w-full h-1.5 bg-gray-200 rounded animate-pulse" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}