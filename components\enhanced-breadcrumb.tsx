"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, ChevronRight } from "lucide-react"
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useLanguage } from "@/components/language-provider"

interface BreadcrumbConfig {
  [key: string]: {
    label: string
    labelKey: string
    icon?: React.ComponentType<{ className?: string }>
  }
}

const breadcrumbConfig: BreadcrumbConfig = {
  "/dashboard": {
    label: "Dashboard",
    labelKey: "nav.dashboard",
    icon: Home,
  },
  "/dashboard/customers": {
    label: "Customers",
    labelKey: "nav.customers",
  },
  "/dashboard/companies": {
    label: "Companies",
    labelKey: "nav.companies",
  },

  "/dashboard/leads": {
    label: "Leads",
    labelKey: "nav.leads",
  },
  "/dashboard/opportunities": {
    label: "Opportunities",
    labelKey: "nav.opportunities",
  },
  "/dashboard/proposals": {
    label: "Proposals",
    labelKey: "nav.proposals",
  },
  "/dashboard/tasks": {
    label: "Tasks",
    labelKey: "nav.tasks",
  },
  "/dashboard/reports": {
    label: "Reports",
    labelKey: "nav.reports",
  },
  "/dashboard/settings": {
    label: "Settings",
    labelKey: "nav.settings",
  },
  "/dashboard/users": {
    label: "Users",
    labelKey: "nav.users",
  },
}

export function EnhancedBreadcrumb() {
  const pathname = usePathname()
  const { t } = useLanguage()

  // Generate breadcrumb items from current path
  const generateBreadcrumbs = () => {
    const pathSegments = pathname.split("/").filter(Boolean)
    const breadcrumbs = []

    // Always start with dashboard if we're in the dashboard area
    if (pathname.startsWith("/dashboard")) {
      let currentPath = ""
      
      for (let i = 0; i < pathSegments.length; i++) {
        currentPath += `/${pathSegments[i]}`
        const config = breadcrumbConfig[currentPath]
        
        if (config) {
          breadcrumbs.push({
            path: currentPath,
            label: t(config.labelKey),
            icon: config.icon,
            isLast: i === pathSegments.length - 1,
          })
        }
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  // Don't show breadcrumbs if we're on the main dashboard
  if (pathname === "/dashboard" || breadcrumbs.length <= 1) {
    return null
  }

  return (
    <div className="flex items-center space-x-1 text-sm text-muted-foreground mb-6">
      <Breadcrumb>
        <BreadcrumbList>
          {breadcrumbs.map((breadcrumb, index) => (
            <React.Fragment key={breadcrumb.path}>
              <BreadcrumbItem>
                {breadcrumb.isLast ? (
                  <BreadcrumbPage className="flex items-center gap-2 font-medium text-foreground">
                    {breadcrumb.icon && (
                      <breadcrumb.icon className="h-4 w-4" />
                    )}
                    {breadcrumb.label}
                  </BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link 
                      href={breadcrumb.path}
                      className="flex items-center gap-2 hover:text-foreground transition-colors"
                    >
                      {breadcrumb.icon && (
                        <breadcrumb.icon className="h-4 w-4" />
                      )}
                      {breadcrumb.label}
                    </Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!breadcrumb.isLast && (
                <BreadcrumbSeparator>
                  <ChevronRight className="h-4 w-4" />
                </BreadcrumbSeparator>
              )}
            </React.Fragment>
          ))}
        </BreadcrumbList>
      </Breadcrumb>
    </div>
  )
}

// Enhanced page header component that includes breadcrumbs
interface PageHeaderProps {
  title: string
  description?: string
  children?: React.ReactNode
  showBreadcrumb?: boolean
}

export function PageHeader({ 
  title, 
  description, 
  children, 
  showBreadcrumb = true 
}: PageHeaderProps) {
  return (
    <div className="space-y-4 mb-8">
      {showBreadcrumb && <EnhancedBreadcrumb />}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-1">
          <h1 className="heading-2 text-foreground">{title}</h1>
          {description && (
            <p className="body-base text-muted-foreground">{description}</p>
          )}
        </div>
        {children && (
          <div className="flex items-center gap-2">
            {children}
          </div>
        )}
      </div>
    </div>
  )
}
