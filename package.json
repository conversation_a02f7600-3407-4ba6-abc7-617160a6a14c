{"name": "nawras-crm", "version": "1.0.2-deployment-fix", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:customer": "playwright test enhanced-customer-management.test.ts", "test:validation": "playwright test customer-validation-edge-cases.test.ts", "test:report": "playwright show-report", "test:performance": "node scripts/comprehensive-performance-test.js", "deploy:performance": "bash scripts/deploy-performance-optimizations.sh", "test:performance:quick": "node scripts/performance-test.js"}, "dependencies": {"@alloc/quick-lru": "^5.2.0", "@auth/supabase-adapter": "^1.10.0", "@fontsource/inter": "^5.2.6", "@fontsource/jetbrains-mono": "^5.2.6", "@modelcontextprotocol/sdk": "^1.13.2", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@smithery/sdk": "^1.5.2", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.46.1", "@tanstack/react-query": "^5.62.7", "@types/node": "^22.10.2", "@types/pg": "^8.15.4", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/recharts": "^1.8.29", "@types/xlsx": "^0.0.35", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "critters": "^0.0.24", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.5.1", "input-otp": "^1.4.1", "lucide-react": "^0.468.0", "next": "^15.1.3", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "pg": "^8.16.3", "playwright": "^1.54.1", "react": "^18.3.1", "react-day-picker": "^9.4.4", "react-dom": "^18.3.1", "recharts": "^3.0.2", "sonner": "^1.7.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "vaul": "^1.1.1", "web-vitals": "^5.0.3", "xlsx": "^0.18.5", "zustand": "^5.0.6"}, "devDependencies": {"@next/bundle-analyzer": "^15.1.3", "@playwright/test": "^1.53.2", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "^19.1.0-rc.2", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}}