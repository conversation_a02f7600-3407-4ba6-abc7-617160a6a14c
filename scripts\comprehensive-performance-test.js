// ✅ COMPREHENSIVE PERFORMANCE TESTING SCRIPT
// Based on research from Context 7 and performance best practices

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'https://sales.nawrasinchina.com',
  credentials: {
    email: '<EMAIL>',
    password: '111333Tt'
  },
  performanceTargets: {
    pageLoad: 3000, // 3 seconds (research-based target)
    queryTime: 1000, // 1 second for individual queries
    authTime: 2000, // 2 seconds for authentication
    sidebarLoad: 500, // 500ms for sidebar stats (after optimization)
  },
  modules: [
    { name: 'Dashboard', path: '/dashboard', critical: true },
    { name: 'Customers', path: '/dashboard/customers', critical: true },
    { name: 'Leads', path: '/dashboard/leads', critical: true },
    { name: 'Opportunities', path: '/dashboard/opportunities', critical: true },
    { name: 'Companies', path: '/dashboard/companies', critical: false },
    { name: 'Tasks', path: '/dashboard/tasks', critical: false },
    { name: 'Reports', path: '/dashboard/reports', critical: false },
  ]
};

class ComprehensivePerformanceTester {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.performanceMetrics = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      averageLoadTime: 0,
      criticalIssues: [],
      optimizationRecommendations: []
    };
  }

  async initialize() {
    console.log('🚀 Initializing Comprehensive Performance Test Suite...');
    console.log('📊 Based on Context 7 research and Neon/Supabase best practices');
    
    this.browser = await chromium.launch({ 
      headless: false,
      args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
    });
    this.page = await this.browser.newPage();
    
    // Enable performance monitoring
    await this.page.addInitScript(() => {
      window.performanceMetrics = {
        queries: [],
        startTime: Date.now(),
        authTime: 0,
        pageLoadTime: 0,
        sidebarLoadTime: 0,
        pooledQueries: 0,
        standardQueries: 0
      };
      
      // Monitor console for performance indicators
      const originalLog = console.log;
      console.log = (...args) => {
        const message = args.join(' ');
        
        // Track slow queries
        if (message.includes('Slow query detected')) {
          const match = message.match(/(\w+): ([\d.]+)ms/);
          if (match) {
            window.performanceMetrics.queries.push({
              table: match[1],
              duration: parseFloat(match[2]),
              timestamp: Date.now(),
              type: 'slow'
            });
          }
        }
        
        // Track pooled vs standard queries
        if (message.includes('(pooled)')) {
          window.performanceMetrics.pooledQueries++;
        } else if (message.includes('(standard)')) {
          window.performanceMetrics.standardQueries++;
        }
        
        // Track sidebar performance
        if (message.includes('SIDEBAR') && message.includes('loaded')) {
          window.performanceMetrics.sidebarLoadTime = Date.now() - window.performanceMetrics.startTime;
        }
        
        originalLog.apply(console, args);
      };
    });
  }

  async testAuthentication() {
    console.log('🔐 Testing Enhanced Authentication Flow...');
    const startTime = Date.now();
    
    try {
      await this.page.goto(`${TEST_CONFIG.baseUrl}/login`);
      
      // Fill credentials
      await this.page.fill('input[type="email"]', TEST_CONFIG.credentials.email);
      await this.page.fill('input[type="password"]', TEST_CONFIG.credentials.password);
      
      // Click login and measure time
      await this.page.click('button[type="submit"]');
      
      // Wait for dashboard with timeout
      await this.page.waitForURL('**/dashboard', { timeout: 30000 });
      
      const authTime = Date.now() - startTime;
      console.log(`✅ Authentication completed in ${authTime}ms`);
      
      return {
        success: true,
        duration: authTime,
        withinTarget: authTime <= TEST_CONFIG.performanceTargets.authTime,
        optimized: authTime <= 1000 // Excellent if under 1 second
      };
    } catch (error) {
      console.error(`❌ Authentication failed: ${error.message}`);
      return {
        success: false,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  async testModule(module) {
    console.log(`🧪 Testing ${module.name} module (${module.critical ? 'CRITICAL' : 'NON-CRITICAL'})...`);
    const startTime = Date.now();
    
    try {
      // Navigate to module
      await this.page.goto(`${TEST_CONFIG.baseUrl}${module.path}`);
      
      // Wait for content to load
      await this.page.waitForSelector('[data-testid="module-content"], .loading, table, .grid, main', { 
        timeout: 30000 
      });
      
      // Wait for sidebar stats to load (testing our optimization)
      await this.page.waitForTimeout(2000);
      
      const pageLoadTime = Date.now() - startTime;
      
      // Get performance metrics from page
      const metrics = await this.page.evaluate(() => {
        return window.performanceMetrics || { 
          queries: [], 
          pooledQueries: 0, 
          standardQueries: 0,
          sidebarLoadTime: 0 
        };
      });
      
      // Check for layout visibility
      const layoutVisible = await this.page.evaluate(() => {
        const sidebar = document.querySelector('[data-testid="sidebar"], .sidebar, nav');
        const mainContent = document.querySelector('main, [role="main"], .main-content');
        return {
          sidebarVisible: sidebar && sidebar.offsetHeight > 0,
          mainContentVisible: mainContent && mainContent.offsetHeight > 0,
          hasData: document.querySelectorAll('table tr, .card, .grid-item').length > 0
        };
      });
      
      // Analyze query performance
      const recentQueries = metrics.queries.filter(q => 
        q.timestamp > startTime - 1000
      );
      
      const slowQueries = recentQueries.filter(q => q.duration > 1000);
      const avgQueryTime = recentQueries.length > 0 
        ? recentQueries.reduce((sum, q) => sum + q.duration, 0) / recentQueries.length 
        : 0;
      
      const result = {
        module: module.name,
        path: module.path,
        critical: module.critical,
        pageLoadTime,
        sidebarLoadTime: metrics.sidebarLoadTime,
        queryCount: recentQueries.length,
        pooledQueries: metrics.pooledQueries,
        standardQueries: metrics.standardQueries,
        slowQueryCount: slowQueries.length,
        averageQueryTime: Math.round(avgQueryTime),
        maxQueryTime: recentQueries.length > 0 ? Math.max(...recentQueries.map(q => q.duration)) : 0,
        layoutVisible,
        success: pageLoadTime <= TEST_CONFIG.performanceTargets.pageLoad,
        optimized: pageLoadTime <= 1500, // Excellent performance
        timestamp: new Date().toISOString(),
        
        // Performance analysis
        performanceGrade: this.calculatePerformanceGrade(pageLoadTime, slowQueries.length, metrics),
        recommendations: this.generateRecommendations(pageLoadTime, slowQueries, metrics)
      };
      
      console.log(`📊 ${module.name} Results:`, {
        'Page Load': `${pageLoadTime}ms ${result.success ? '✅' : '❌'}`,
        'Queries': `${recentQueries.length} total (${metrics.pooledQueries} pooled, ${slowQueries.length} slow)`,
        'Avg Query Time': `${Math.round(avgQueryTime)}ms`,
        'Layout': `${layoutVisible.sidebarVisible && layoutVisible.mainContentVisible ? '✅ Visible' : '❌ Issues'}`,
        'Grade': result.performanceGrade
      });
      
      return result;
      
    } catch (error) {
      console.error(`❌ ${module.name} test failed:`, error.message);
      return {
        module: module.name,
        path: module.path,
        critical: module.critical,
        error: error.message,
        success: false,
        timestamp: new Date().toISOString()
      };
    }
  }

  calculatePerformanceGrade(loadTime, slowQueryCount, metrics) {
    let score = 100;
    
    // Deduct points for slow loading
    if (loadTime > 3000) score -= 30;
    else if (loadTime > 2000) score -= 20;
    else if (loadTime > 1000) score -= 10;
    
    // Deduct points for slow queries
    score -= slowQueryCount * 15;
    
    // Bonus for using pooled connections
    if (metrics.pooledQueries > metrics.standardQueries) score += 10;
    
    // Determine grade
    if (score >= 90) return 'A+ (Excellent)';
    if (score >= 80) return 'A (Very Good)';
    if (score >= 70) return 'B (Good)';
    if (score >= 60) return 'C (Fair)';
    return 'D (Needs Improvement)';
  }

  generateRecommendations(loadTime, slowQueries, metrics) {
    const recommendations = [];
    
    if (loadTime > 3000) {
      recommendations.push('🚨 CRITICAL: Page load time exceeds 3 seconds - investigate database queries');
    }
    
    if (slowQueries.length > 0) {
      recommendations.push(`⚠️ ${slowQueries.length} slow queries detected - consider query optimization`);
    }
    
    if (metrics.standardQueries > metrics.pooledQueries) {
      recommendations.push('💡 Consider using more pooled connections for better performance');
    }
    
    if (loadTime < 1000 && slowQueries.length === 0) {
      recommendations.push('🎉 Excellent performance - no optimization needed');
    }
    
    return recommendations;
  }

  async runFullSuite() {
    console.log('🎯 Starting Comprehensive Performance Test Suite');
    console.log('📋 Testing Connection Pooling, Query Optimization, and Layout Visibility');
    console.log('=' .repeat(80));
    
    try {
      // Initialize browser
      await this.initialize();
      
      // Test authentication
      const authResult = await this.testAuthentication();
      this.results.push({ type: 'auth', ...authResult });
      this.performanceMetrics.totalTests++;
      
      if (!authResult.success) {
        throw new Error('Authentication failed - aborting test suite');
      }
      
      if (authResult.success) this.performanceMetrics.passedTests++;
      
      // Test each module
      for (const module of TEST_CONFIG.modules) {
        const moduleResult = await this.testModule(module);
        this.results.push({ type: 'module', ...moduleResult });
        this.performanceMetrics.totalTests++;
        
        if (moduleResult.success) {
          this.performanceMetrics.passedTests++;
        } else {
          this.performanceMetrics.failedTests++;
          if (module.critical) {
            this.performanceMetrics.criticalIssues.push(`${module.name}: ${moduleResult.error || 'Performance target not met'}`);
          }
        }
        
        // Brief pause between tests
        await this.page.waitForTimeout(1000);
      }
      
      // Generate comprehensive report
      this.generateComprehensiveReport();
      
    } catch (error) {
      console.error('💥 Test suite failed:', error.message);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  generateComprehensiveReport() {
    console.log('\n📋 COMPREHENSIVE PERFORMANCE TEST REPORT');
    console.log('=' .repeat(80));
    
    const moduleResults = this.results.filter(r => r.type === 'module');
    const successfulModules = moduleResults.filter(r => r.success);
    const failedModules = moduleResults.filter(r => !r.success);
    const criticalModules = moduleResults.filter(r => r.critical);
    const criticalSuccessful = criticalModules.filter(r => r.success);
    
    // Calculate metrics
    const avgLoadTime = successfulModules.length > 0 
      ? Math.round(successfulModules.reduce((sum, r) => sum + r.pageLoadTime, 0) / successfulModules.length)
      : 0;
    
    const avgQueryTime = successfulModules.length > 0
      ? Math.round(successfulModules.reduce((sum, r) => sum + (r.averageQueryTime || 0), 0) / successfulModules.length)
      : 0;
    
    const totalPooledQueries = successfulModules.reduce((sum, r) => sum + (r.pooledQueries || 0), 0);
    const totalStandardQueries = successfulModules.reduce((sum, r) => sum + (r.standardQueries || 0), 0);
    
    console.log(`\n🎯 SUMMARY:`);
    console.log(`✅ Successful Modules: ${successfulModules.length}/${moduleResults.length}`);
    console.log(`❌ Failed Modules: ${failedModules.length}/${moduleResults.length}`);
    console.log(`🔥 Critical Modules Success: ${criticalSuccessful.length}/${criticalModules.length}`);
    console.log(`📊 Overall Success Rate: ${Math.round((successfulModules.length / moduleResults.length) * 100)}%`);
    
    console.log(`\n⚡ PERFORMANCE METRICS:`);
    console.log(`📈 Average Page Load: ${avgLoadTime}ms (Target: <3000ms)`);
    console.log(`🔍 Average Query Time: ${avgQueryTime}ms (Target: <1000ms)`);
    console.log(`🏊 Pooled Queries: ${totalPooledQueries} | Standard Queries: ${totalStandardQueries}`);
    console.log(`💡 Connection Pool Usage: ${Math.round((totalPooledQueries / (totalPooledQueries + totalStandardQueries)) * 100)}%`);
    
    console.log(`\n📈 DETAILED RESULTS:`);
    moduleResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      const critical = result.critical ? '🔥' : '  ';
      const loadTime = result.pageLoadTime ? `${result.pageLoadTime}ms` : 'N/A';
      const grade = result.performanceGrade || 'N/A';
      const layout = result.layoutVisible?.sidebarVisible && result.layoutVisible?.mainContentVisible ? '👁️' : '🚫';
      
      console.log(`${status}${critical} ${result.module.padEnd(15)} | Load: ${loadTime.padEnd(8)} | Grade: ${grade.padEnd(15)} | Layout: ${layout}`);
    });
    
    if (this.performanceMetrics.criticalIssues.length > 0) {
      console.log(`\n🚨 CRITICAL ISSUES:`);
      this.performanceMetrics.criticalIssues.forEach(issue => {
        console.log(`   ${issue}`);
      });
    }
    
    // Overall assessment
    const overallGrade = this.calculateOverallGrade();
    console.log(`\n🏆 OVERALL ASSESSMENT: ${overallGrade}`);
    
    if (overallGrade.includes('A')) {
      console.log('🎉 Excellent! Your CRM performance optimizations are working effectively.');
    } else if (overallGrade.includes('B')) {
      console.log('👍 Good performance, but there\'s room for improvement.');
    } else {
      console.log('⚠️ Performance issues detected. Review the recommendations above.');
    }
    
    console.log('\n' + '=' .repeat(80));
  }

  calculateOverallGrade() {
    const moduleResults = this.results.filter(r => r.type === 'module');
    const successRate = (this.performanceMetrics.passedTests / this.performanceMetrics.totalTests) * 100;
    const criticalSuccess = moduleResults.filter(r => r.critical && r.success).length;
    const totalCritical = moduleResults.filter(r => r.critical).length;
    const criticalRate = totalCritical > 0 ? (criticalSuccess / totalCritical) * 100 : 100;
    
    const avgScore = (successRate + criticalRate) / 2;
    
    if (avgScore >= 90) return 'A+ (Excellent Performance)';
    if (avgScore >= 80) return 'A (Very Good Performance)';
    if (avgScore >= 70) return 'B (Good Performance)';
    if (avgScore >= 60) return 'C (Fair Performance)';
    return 'D (Needs Improvement)';
  }
}

// Run the test suite
if (require.main === module) {
  const tester = new ComprehensivePerformanceTester();
  tester.runFullSuite().catch(console.error);
}

module.exports = ComprehensivePerformanceTester;
