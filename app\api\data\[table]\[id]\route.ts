import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'
import { authOptions } from '@/lib/auth-config'

// Create server-side Supabase client with service role
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Define allowed tables for security
const ALLOWED_TABLES = [
  'customers',
  'companies', 
  'leads',
  'deals',
  'opportunities', // This is a view, but we'll handle it
  'tasks',
  'proposals',
  'users'
]

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ table: string; id: string }> }
) {
  try {
    // Get NextAuth session
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = (session.user as any)?.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table
    const id = resolvedParams.id

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    const body = await request.json()
    
    // Add updated_at timestamp
    const dataToUpdate = {
      ...body,
      updated_at: new Date().toISOString()
    }

    console.log(`🔍 [DATA-API] Updating record ${id} in ${table} for user:`, userId)

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    const { data, error } = await supabaseAdmin
      .from(actualTable)
      .update(dataToUpdate)
      .eq('id', id)
      .eq('user_id', userId) // Ensure user can only update their own records
      .select()
      .single()

    if (error) {
      console.error(`❌ [DATA-API] Error updating ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully updated record in ${table}`)

    return NextResponse.json({
      data: data,
      message: 'Record updated successfully'
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error updating record:`, error)
    
    return NextResponse.json(
      { 
        error: 'Failed to update record',
        details: error.message 
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ table: string; id: string }> }
) {
  try {
    // Get NextAuth session
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = (session.user as any)?.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    const resolvedParams = await params
    const table = resolvedParams.table
    const id = resolvedParams.id

    // Security check - only allow specific tables
    if (!ALLOWED_TABLES.includes(table)) {
      return NextResponse.json(
        { error: 'Table not allowed' },
        { status: 403 }
      )
    }

    console.log(`🔍 [DATA-API] Deleting record ${id} from ${table} for user:`, userId)

    // Handle special case for opportunities (which is a view of deals)
    const actualTable = table === 'opportunities' ? 'deals' : table

    const { error } = await supabaseAdmin
      .from(actualTable)
      .delete()
      .eq('id', id)
      .eq('user_id', userId) // Ensure user can only delete their own records

    if (error) {
      console.error(`❌ [DATA-API] Error deleting ${table}:`, error)
      throw error
    }

    console.log(`✅ [DATA-API] Successfully deleted record from ${table}`)

    return NextResponse.json({
      message: 'Record deleted successfully'
    })

  } catch (error: any) {
    console.error(`❌ [DATA-API] Error deleting record:`, error)
    
    return NextResponse.json(
      { 
        error: 'Failed to delete record',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
