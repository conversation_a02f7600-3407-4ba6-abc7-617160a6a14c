import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'
import { authOptions } from '@/lib/auth-config'
import { cache, CacheKeys, CacheTTL } from '@/lib/cache'

// Create server-side Supabase client with service role for dashboard stats
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Get NextAuth session
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = (session.user as any)?.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    console.log('🔍 [DASHBOARD-STATS-API] Fetching stats for user:', userId)

    // Check if user is admin to determine scope of stats
    const userRole = (session.user as any)?.role
    const isAdmin = userRole === 'admin'

    console.log(`🔍 [DASHBOARD-STATS-API] User role: ${userRole}, isAdmin: ${isAdmin}`)

    // Check cache first
    const cacheKey = CacheKeys.dashboardStats(userId, isAdmin)
    const cachedStats = cache.get(cacheKey)

    if (cachedStats) {
      console.log('⚡ [DASHBOARD-STATS-API] Returning cached stats')
      return NextResponse.json(cachedStats)
    }

    // For admin users, show global stats; for regular users, show their own stats
    const [customersRes, leadsRes, opportunitiesRes, companiesRes, tasksRes] = await Promise.all([
      isAdmin
        ? supabaseAdmin.from('customers').select('id', { count: 'exact', head: true })
        : supabaseAdmin.from('customers').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabaseAdmin.from('leads').select('id', { count: 'exact', head: true })
        : supabaseAdmin.from('leads').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabaseAdmin.from('deals').select('id', { count: 'exact', head: true })
        : supabaseAdmin.from('deals').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabaseAdmin.from('companies').select('id', { count: 'exact', head: true })
        : supabaseAdmin.from('companies').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabaseAdmin.from('tasks').select('id', { count: 'exact', head: true })
        : supabaseAdmin.from('tasks').select('id', { count: 'exact', head: true }).eq('user_id', userId)
    ])

    // Check for errors
    if (customersRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Customers query error:', customersRes.error)
      throw customersRes.error
    }
    if (leadsRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Leads query error:', leadsRes.error)
      throw leadsRes.error
    }
    if (opportunitiesRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Opportunities query error:', opportunitiesRes.error)
      throw opportunitiesRes.error
    }
    if (companiesRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Companies query error:', companiesRes.error)
      throw companiesRes.error
    }
    if (tasksRes.error) {
      console.error('❌ [DASHBOARD-STATS-API] Tasks query error:', tasksRes.error)
      throw tasksRes.error
    }

    const stats = {
      customers: customersRes.count || 0,
      leads: leadsRes.count || 0,
      opportunities: opportunitiesRes.count || 0,
      companies: companiesRes.count || 0,
      tasks: tasksRes.count || 0
    }

    console.log('✅ [DASHBOARD-STATS-API] Stats fetched successfully:', stats)

    // Cache the results for future requests
    cache.set(cacheKey, stats, CacheTTL.DASHBOARD_STATS)

    return NextResponse.json(stats)

  } catch (error: any) {
    console.error('❌ [DASHBOARD-STATS-API] Error fetching dashboard stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch dashboard stats',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
