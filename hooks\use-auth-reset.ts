"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"

interface UseAuthResetOptions {
  timeoutMs?: number
  enableAutoDetection?: boolean
  onResetComplete?: () => void
}

interface UseAuthResetReturn {
  isAuthStuck: boolean
  isResetting: boolean
  resetStatus: 'idle' | 'success' | 'error'
  resetMessage: string
  resetAuthentication: () => Promise<void>
  clearResetStatus: () => void
}

/**
 * Hook for detecting and handling authentication issues
 * Provides automatic detection of stuck authentication states and reset functionality
 */
export function useAuthReset(options: UseAuthResetOptions = {}): UseAuthResetReturn {
  const {
    timeoutMs = 15000, // 15 seconds default
    enableAutoDetection = true,
    onResetComplete
  } = options

  const { user, loading: authLoading, error: authError, resetAuthentication: authReset } = useAuth()
  
  const [isAuthStuck, setIsAuthStuck] = useState(false)
  const [isResetting, setIsResetting] = useState(false)
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [resetMessage, setResetMessage] = useState('')

  // Auto-detection of stuck authentication
  useEffect(() => {
    if (!enableAutoDetection) return

    const timeout = setTimeout(() => {
      // Consider authentication stuck if:
      // 1. Still loading after timeout
      // 2. No user present
      // 3. No error present (errors are handled differently)
      if (authLoading && !user && !authError) {
        console.warn(`🚨 Authentication appears stuck after ${timeoutMs}ms`)
        setIsAuthStuck(true)
      }
    }, timeoutMs)

    // Clear stuck state if authentication resolves
    if (!authLoading || user || authError) {
      setIsAuthStuck(false)
    }

    return () => clearTimeout(timeout)
  }, [authLoading, user, authError, timeoutMs, enableAutoDetection])

  // Reset authentication function
  const resetAuthentication = async (): Promise<void> => {
    try {
      setIsResetting(true)
      setResetStatus('idle')
      setResetMessage('')
      setIsAuthStuck(false)
      
      console.log('🔄 Initiating authentication reset via useAuthReset hook')
      
      await authReset()
      
      setResetStatus('success')
      setResetMessage('Authentication reset completed successfully!')
      
      // Call completion callback if provided
      if (onResetComplete) {
        setTimeout(() => {
          onResetComplete()
        }, 2000)
      }
      
    } catch (error) {
      console.error('❌ Authentication reset failed:', error)
      setResetStatus('error')
      setResetMessage('Authentication reset failed. Please refresh the page manually.')
    } finally {
      setIsResetting(false)
    }
  }

  // Clear reset status
  const clearResetStatus = () => {
    setResetStatus('idle')
    setResetMessage('')
  }

  return {
    isAuthStuck,
    isResetting,
    resetStatus,
    resetMessage,
    resetAuthentication,
    clearResetStatus
  }
}

/**
 * Utility function to check if authentication appears to be in a problematic state
 */
export function isAuthenticationProblematic(
  loading: boolean, 
  user: any, 
  error: string | null,
  timeElapsed: number = 0
): boolean {
  // Authentication is problematic if:
  // 1. Loading for more than 10 seconds with no user or error
  // 2. Has an error that suggests a stuck state
  // 3. Loading state persists abnormally long
  
  const stuckInLoading: boolean = loading && !user && !error && timeElapsed > 10000
  const hasStuckError: boolean = Boolean(error && typeof error === 'string' && (
    error.includes('timeout') ||
    error.includes('stuck') ||
    error.includes('infinite')
  ))

  return stuckInLoading || hasStuckError
}

/**
 * Utility function to get authentication diagnostic information
 */
export function getAuthDiagnostics(
  loading: boolean,
  user: any,
  error: string | null
): Record<string, any> {
  return {
    loading,
    hasUser: !!user,
    userEmail: user?.email || null,
    hasError: !!error,
    error: error || null,
    timestamp: new Date().toISOString(),
    userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : null,
    url: typeof window !== 'undefined' ? window.location.href : null,
    localStorage: typeof window !== 'undefined' ? {
      hasAuthBypass: !!localStorage.getItem('auth_bypass'),
      hasEmergencyBypass: !!localStorage.getItem('emergency_bypass'),
      hasSessionBackup: !!localStorage.getItem('crm_session_backup'),
      supabaseKeys: Object.keys(localStorage).filter(key => 
        key.startsWith('sb-') || key.includes('supabase')
      )
    } : null
  }
}
