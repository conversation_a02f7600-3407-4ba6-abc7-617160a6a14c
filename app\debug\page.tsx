"use client"

import { useSession } from "next-auth/react"
import { useEffect, useState } from "react"

export default function DebugPage() {
  const { data: session, status } = useSession()
  const [windowInfo, setWindowInfo] = useState<any>({})
  const [cssInfo, setCssInfo] = useState<any>({})

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWindowInfo({
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        screen: {
          width: window.screen.width,
          height: window.screen.height
        },
        devicePixelRatio: window.devicePixelRatio,
        location: window.location.href
      })

      // Check CSS loading
      const stylesheets = Array.from(document.styleSheets).map(sheet => {
        try {
          return {
            href: sheet.href,
            rules: sheet.cssRules?.length || 0,
            disabled: sheet.disabled
          }
        } catch (e) {
          return {
            href: sheet.href,
            error: 'Cannot access rules (CORS)',
            disabled: sheet.disabled
          }
        }
      })

      setCssInfo({
        stylesheets,
        computedStyles: {
          body: window.getComputedStyle(document.body),
          html: window.getComputedStyle(document.documentElement)
        }
      })
    }
  }, [])

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">🔍 Debug Information</h1>
      
      <div className="space-y-6">
        {/* Authentication Status */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">🔐 Authentication Status</h2>
          <div className="space-y-2">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>User:</strong> {session?.user?.email || 'Not authenticated'}</p>
            <p><strong>Role:</strong> {(session?.user as any)?.role || 'N/A'}</p>
            <p><strong>Session:</strong> {session ? 'Active' : 'None'}</p>
          </div>
        </div>

        {/* Browser Information */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">🌐 Browser Information</h2>
          <div className="space-y-2 text-sm">
            <p><strong>User Agent:</strong> {windowInfo.userAgent}</p>
            <p><strong>Viewport:</strong> {windowInfo.viewport?.width} x {windowInfo.viewport?.height}</p>
            <p><strong>Screen:</strong> {windowInfo.screen?.width} x {windowInfo.screen?.height}</p>
            <p><strong>Device Pixel Ratio:</strong> {windowInfo.devicePixelRatio}</p>
            <p><strong>URL:</strong> {windowInfo.location}</p>
          </div>
        </div>

        {/* CSS Information */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">🎨 CSS Information</h2>
          <div className="space-y-2">
            <p><strong>Stylesheets Loaded:</strong> {cssInfo.stylesheets?.length || 0}</p>
            <div className="max-h-40 overflow-y-auto">
              {cssInfo.stylesheets?.map((sheet: any, index: number) => (
                <div key={index} className="text-xs p-2 bg-gray-50 rounded mb-1">
                  <p><strong>URL:</strong> {sheet.href || 'Inline'}</p>
                  <p><strong>Rules:</strong> {sheet.rules || sheet.error}</p>
                  <p><strong>Disabled:</strong> {sheet.disabled ? 'Yes' : 'No'}</p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Test Elements */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">🧪 Test Elements</h2>
          <div className="space-y-4">
            <div className="p-4 bg-blue-100 border border-blue-300 rounded">
              <p className="text-blue-800">This should be a blue box with blue text</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-red-100 border border-red-300 rounded">
                <p className="text-red-800">Red Card</p>
              </div>
              <div className="p-4 bg-green-100 border border-green-300 rounded">
                <p className="text-green-800">Green Card</p>
              </div>
              <div className="p-4 bg-yellow-100 border border-yellow-300 rounded">
                <p className="text-yellow-800">Yellow Card</p>
              </div>
            </div>
            <div className="flex items-center justify-between p-4 bg-purple-100 border border-purple-300 rounded">
              <span className="text-purple-800">Flex Layout Test</span>
              <button className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                Button
              </button>
            </div>
          </div>
        </div>

        {/* Console Test */}
        <div className="bg-white p-4 rounded-lg border">
          <h2 className="text-xl font-semibold mb-3">📝 Console Test</h2>
          <button 
            onClick={() => {
              console.log('🔍 Debug button clicked!')
              console.log('Session:', session)
              console.log('Window info:', windowInfo)
              console.log('CSS info:', cssInfo)
              alert('Check the browser console for debug information!')
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Log Debug Info to Console
          </button>
        </div>
      </div>
    </div>
  )
}
