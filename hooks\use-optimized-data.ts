"use client"

import { useState, useEffect, useCallback, useMemo, useRef } from "react"
import { supabase, supabasePooled } from "@/lib/supabase"
import { useToast } from "@/hooks/use-toast"
import { useSession } from "next-auth/react"
import { RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import { realtimeManager } from "@/lib/realtime-manager"

// ✅ ENHANCED PERFORMANCE: Intelligent query caching with longer TTL for slow queries
const queryCache = new Map<string, { data: any[], timestamp: number, ttl: number }>()
const CACHE_TTL = 10 * 60 * 1000 // 10 minutes default TTL (extended for performance)
const SLOW_QUERY_CACHE_TTL = 30 * 60 * 1000 // 30 minutes for slow queries

// Performance monitoring
const performanceMetrics = new Map<string, { queryCount: number, totalTime: number, avgTime: number }>()

function trackPerformance(table: string, startTime: number) {
  const endTime = performance.now()
  const duration = endTime - startTime

  const existing = performanceMetrics.get(table) || { queryCount: 0, totalTime: 0, avgTime: 0 }
  const newMetrics = {
    queryCount: existing.queryCount + 1,
    totalTime: existing.totalTime + duration,
    avgTime: (existing.totalTime + duration) / (existing.queryCount + 1)
  }

  performanceMetrics.set(table, newMetrics)

  // ✅ ENHANCED: Log slow queries and suggest longer caching
  if (duration > 1000) {
    console.warn(`🐌 Slow query detected for ${table}: ${duration.toFixed(2)}ms`)

    // For very slow queries (>10s), suggest using longer cache TTL
    if (duration > 10000) {
      console.warn(`⚡ Performance tip: Consider using longer cache TTL for ${table} (current query: ${duration.toFixed(2)}ms)`)
    }
  }

  return duration
}

// Performance optimization: Debounce utility
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout
  return ((...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }) as T
}

// Check if demo mode is enabled (only in development)
const isDemoModeEnabled = () => {
  if (typeof window === 'undefined') return false
  const isProduction = process.env.NODE_ENV === 'production'
  const demoModeDisabled = process.env.ENABLE_DEMO_MODE === 'false'
  
  // Demo mode only available in development and when not explicitly disabled
  return !isProduction && !demoModeDisabled
}

// Demo data generator for development/testing only
function getDemoData(table: string) {
  // Only return demo data if demo mode is enabled
  if (!isDemoModeEnabled()) {
    return []
  }

  const currentDate = new Date().toISOString()
  
  switch (table) {
    case "customers":
      return [
        {
          id: "1",
          contact_person: "John Smith",
          email: "<EMAIL>",
          phone: "+962-6-1234567",
          company: "Acme Corporation",
          address: "123 Business Street",
          country: "Jordan",
          city: "Amman",
          status: "Active",
          created_at: currentDate,
          user_id: "00000000-0000-4000-8000-000000000001",
        },
        {
          id: "2", 
          contact_person: "Sarah Johnson",
          email: "<EMAIL>",
          phone: "+962-6-7654321",
          company: "Tech Solutions Inc",
          address: "456 Innovation Ave",
          country: "Jordan",
          city: "Irbid",
          status: "Active",
          created_at: currentDate,
          user_id: "00000000-0000-4000-8000-000000000001",
        },
      ]
    default:
      return []
  }
}

interface UseOptimizedDataOptions {
  table: string
  select?: string
  orderBy?: { column: string; ascending?: boolean }
  filters?: Record<string, any>
  realtime?: boolean
  requiresAuth?: boolean
  // Performance optimization options
  enableCache?: boolean
  cacheTTL?: number // Time to live in milliseconds
  debounceMs?: number // Debounce queries
  enablePagination?: boolean
  pageSize?: number
  enableLazyLoading?: boolean
  usePooledClient?: boolean // ✅ NEW: Option to use pooled client for better performance
}

export function useOptimizedData<T = any>({
  table,
  select = "*",
  orderBy,
  filters = {},
  realtime = false,
  requiresAuth = true,
  enableCache = true,
  cacheTTL = CACHE_TTL,
  debounceMs = 300,
  enablePagination = false,
  pageSize = 50,
  enableLazyLoading = false,
  usePooledClient = false, // ✅ NEW: Use pooled client for better performance
}: UseOptimizedDataOptions) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const { data: session } = useSession()
  const user = session?.user

  // Use the singleton Supabase instance directly to prevent multiple GoTrueClient instances
  // No need to memoize since supabase is already a singleton

  // CRITICAL FIX: Memoize stable user values to prevent infinite re-renders
  const stableUserId = useMemo(() => (user as any)?.id, [(user as any)?.id])
  const stableUserRole = useMemo(() => (user as any)?.role, [(user as any)?.role])
  const hasUser = useMemo(() => !!user, [!!user])

  // Add state debugging
  console.log(`🔍 [OPTIMIZED-DATA] Hook state for ${table}:`, {
    dataLength: data.length,
    loading,
    hasError: !!error,
    hasUser,
    stableUserId,
    stableUserRole
  })

  // Memoize the query to prevent unnecessary re-renders
  const queryKey = useMemo(() => {
    return JSON.stringify({ table, select, orderBy, filters })
  }, [table, select, orderBy, filters])

  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      console.log(`🔍 [OPTIMIZED-DATA] Starting fetch for ${table}`, {
        stableUserId,
        stableUserRole,
        requiresAuth,
        hasUser
      })

      // PRODUCTION: Only use demo data in development mode when explicitly enabled
      if (isDemoModeEnabled()) {
        // Check for demo session only in development
        const demoSession = typeof window !== 'undefined' ? localStorage.getItem("demo-session") : null
        if (demoSession || (user && user.email === "<EMAIL>")) {
          console.log(`🎯 [OPTIMIZED-DATA] Using demo data for ${table} (development mode)`)
          const mockData = getDemoData(table)
          setData(mockData as T[])
          setLoading(false)
          return
        }
      }

      // For production: require authentication and use real database
      if (requiresAuth && !hasUser) {
        console.log(`❌ [OPTIMIZED-DATA] No user found for ${table}, requiresAuth=${requiresAuth}`)
        setData([])
        setLoading(false)
        return
      }

      // Performance optimization: Check cache first
      const cacheKey = `${table}-${queryKey}-${stableUserId}`
      if (enableCache) {
        const cached = queryCache.get(cacheKey)
        if (cached && Date.now() - cached.timestamp < cached.ttl) {
          console.log(`⚡ [OPTIMIZED-DATA] Cache hit for ${table}`)
          setData(cached.data as T[])
          setLoading(false)
          return
        }
      }

      console.log(`🔍 [OPTIMIZED-DATA] Querying Supabase for ${table} ${usePooledClient ? '(pooled)' : '(standard)'}`)
      const queryStartTime = performance.now()

      // ✅ PERFORMANCE: Use pooled client for better performance when requested
      const client = usePooledClient ? supabasePooled : supabase
      let query = client.from(table).select(select)

      // CRITICAL FIX: Add user filtering for authenticated requests (but not for admin users)
      if (hasUser && requiresAuth && stableUserId) {
        // Check if user is admin - admins should see all data
        const isAdmin = stableUserRole === 'admin'
        if (!isAdmin) {
          console.log(`🔒 [OPTIMIZED-DATA] Adding user filter: user_id = ${stableUserId}`)
          query = query.eq("user_id", stableUserId)
        } else {
          console.log(`👑 [OPTIMIZED-DATA] Admin user detected - showing all data for ${table}`)
        }
      }

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value)
        }
      })

      // Apply ordering
      if (orderBy) {
        query = query.order(orderBy.column, {
          ascending: orderBy.ascending ?? true,
        })
      }

      const { data: result, error: queryError } = await query

      if (queryError) {
        throw queryError
      }

      const queryDuration = trackPerformance(table, queryStartTime)
      console.log(`✅ [OPTIMIZED-DATA] Successfully fetched ${result?.length || 0} records from ${table} in ${queryDuration.toFixed(2)}ms`)

      // ✅ ENHANCED PERFORMANCE: Intelligent caching with adaptive TTL
      if (enableCache && result) {
        // Use longer cache TTL for slow queries to reduce database load
        const adaptiveTTL = queryDuration > 10000 ? SLOW_QUERY_CACHE_TTL : cacheTTL

        queryCache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
          ttl: adaptiveTTL
        })

        const ttlMinutes = Math.round(adaptiveTTL / 60000)
        console.log(`💾 [OPTIMIZED-DATA] Cached ${result.length} records for ${table} (TTL: ${ttlMinutes}min, Query: ${queryDuration.toFixed(0)}ms)`)
      }

      setData(result as T[])
      setError(null)
    } catch (err: any) {
      console.error(`❌ [OPTIMIZED-DATA] Query error:`, err)
      setError(err.message)
      toast({
        title: `Error fetching ${table}`,
        description: err.message,
        variant: "destructive",
      })
    } finally {
      console.log(`🏁 [OPTIMIZED-DATA] Setting loading to false for ${table}`)
      setLoading(false)
    }
  }, [table, select, orderBy, filters, stableUserId, stableUserRole, hasUser, requiresAuth, supabase, toast, enableCache, cacheTTL, queryKey])

  // Performance optimization: Debounced fetch function
  const debouncedFetchData = useMemo(
    () => debounce(fetchData, debounceMs),
    [fetchData, debounceMs]
  )

  const create = async (data: Partial<T>, table: string) => {
    try {
      if (requiresAuth && !user) {
        throw new Error("Authentication required")
      }

      // CRITICAL FIX: Handle field mapping for customers table
      let recordData = { ...data, user_id: (user as any)?.id }

      if (table === 'customers') {
        console.log("🔍 [HOOK DEBUG] Original customer data:", data)

        // Map contact_person to name field for database compatibility
        if ((data as any).contact_person && !(data as any).name) {
          recordData = {
            ...recordData,
            name: (data as any).contact_person,  // Required field for database
            contact_person: (data as any).contact_person  // Keep for compatibility
          }
        }

        console.log("🔍 [HOOK DEBUG] Mapped customer data:", recordData)
      }

      const { data: result, error } = await supabase
        .from(table)
        .insert([recordData])
        .select()
        .single()

      if (error) throw error

      // Optimistically update the local data
      setData(prev => [...prev, result as T])

      return result
    } catch (err: any) {
      console.error(`Error creating record in ${table}:`, err)
      toast({
        title: `Error creating record`,
        description: err.message,
        variant: "destructive",
      })
      return null
    }
  }

  const update = async (id: string, data: Partial<T>, table: string) => {
    try {
      if (requiresAuth && !user) {
        throw new Error("Authentication required")
      }

      const { data: result, error } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error

      // Optimistically update the local data
      setData(prev => prev.map(item => {
        if ((item as any).id === id) {
          return { ...item, ...result }
        }
        return item
      }))

      return result
    } catch (err: any) {
      console.error(`Error updating record in ${table}:`, err)
      toast({
        title: `Error updating record`,
        description: err.message,
        variant: "destructive",
      })
      return null
    }
  }

  const remove = async (id: string, table: string) => {
    try {
      if (requiresAuth && !user) {
        throw new Error("Authentication required")
      }

      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id)

      if (error) throw error

      // Optimistically update the local data
      setData(prev => (prev || []).filter(item => (item as any).id !== id))

      return true
    } catch (err: any) {
      console.error(`Error deleting record from ${table}:`, err)
      toast({
        title: `Error deleting record`,
        description: err.message,
        variant: "destructive",
      })
      return false
    }
  }

  // CRITICAL FIX: Use refs to track stable values and prevent infinite loops
  const lastStableUserIdRef = useRef<string | undefined>(undefined)
  const lastRequiresAuthRef = useRef<boolean>(requiresAuth)
  const lastTableRef = useRef<string>(table)
  const hasInitiallyFetchedRef = useRef<boolean>(false)

  // Effect 1: Initial data fetch - runs only when meaningful dependencies change
  useEffect(() => {
    // Check if any critical dependencies have actually changed
    const userIdChanged = lastStableUserIdRef.current !== stableUserId
    const requiresAuthChanged = lastRequiresAuthRef.current !== requiresAuth
    const tableChanged = lastTableRef.current !== table

    console.log(`🚀 [OPTIMIZED-DATA] Initial fetch triggered for ${table}`, {
      hasUser,
      requiresAuth,
      hasInitiallyFetched: hasInitiallyFetchedRef.current,
      userIdChanged,
      requiresAuthChanged,
      tableChanged,
      stableUserId
    })

    // Only proceed if there's a meaningful change or initial load
    if (!userIdChanged && !requiresAuthChanged && !tableChanged && hasInitiallyFetchedRef.current) {
      console.log(`⏭️ [OPTIMIZED-DATA] Skipping fetch - no meaningful changes for ${table}`)
      return
    }

    // Update refs to track current values
    lastStableUserIdRef.current = stableUserId
    lastRequiresAuthRef.current = requiresAuth
    lastTableRef.current = table
    hasInitiallyFetchedRef.current = true

    // ✅ PERFORMANCE OPTIMIZED: Reduced auth timeout for faster queries
    const authCheckTimeout = setTimeout(() => {
      if (requiresAuth && !hasUser) {
        console.log(`⚠️ [OPTIMIZED-DATA] Auth timeout for ${table} - stopping infinite loop`)
        setLoading(false)
        setError("Authentication required")
        return
      }

      // Only fetch when auth requirements are met
      if (!requiresAuth || hasUser) {
        console.log(`🔄 [OPTIMIZED-DATA] Starting data fetch for ${table}`)
        // Use debounced fetch for better performance
        if (debounceMs > 0) {
          debouncedFetchData()
        } else {
          fetchData()
        }
      }
    }, 100) // ✅ REDUCED: From 500ms to 100ms for faster query execution

    return () => clearTimeout(authCheckTimeout)
  }, [table, stableUserId, requiresAuth, hasUser]) // Remove fetchData dependency to prevent loops

  // Effect 2: Realtime subscription setup using singleton manager to prevent multiple GoTrueClient instances
  useEffect(() => {
    if (!realtime || !hasUser) return

    console.log(`🔄 [OPTIMIZED-DATA] Setting up realtime subscription for ${table} via singleton manager`)

    // Use the singleton realtime manager to prevent duplicate subscriptions
    const unsubscribe = realtimeManager.subscribe(
      table,
      (payload: RealtimePostgresChangesPayload<any>) => {
        console.log(`🔄 [OPTIMIZED-DATA] Realtime change received for ${table}:`, payload.eventType)
        // Call fetchData directly without dependency issues
        fetchData()
      },
      {
        userId: stableUserId,
        userRole: stableUserRole,
        requiresAuth
      }
    )

    return unsubscribe
  }, [realtime, table, stableUserId, stableUserRole, requiresAuth, hasUser, fetchData]) // Use stable memoized values

  return {
    data,
    loading,
    error,
    create,
    update,
    remove,
    refresh: fetchData,
  }
}