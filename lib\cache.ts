// Simple in-memory cache for dashboard stats
// In production, consider using Redis or similar

interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>()

  set<T>(key: string, data: T, ttlMinutes: number = 5): void {
    const ttl = ttlMinutes * 60 * 1000 // Convert minutes to milliseconds
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
    
    console.log(`📦 [CACHE] Set key: ${key}, TTL: ${ttlMinutes}min`)
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      console.log(`❌ [CACHE] Miss: ${key}`)
      return null
    }

    const now = Date.now()
    const isExpired = (now - entry.timestamp) > entry.ttl

    if (isExpired) {
      console.log(`⏰ [CACHE] Expired: ${key}`)
      this.cache.delete(key)
      return null
    }

    console.log(`✅ [CACHE] Hit: ${key}`)
    return entry.data as T
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key)
    if (deleted) {
      console.log(`🗑️ [CACHE] Deleted: ${key}`)
    }
    return deleted
  }

  clear(): void {
    const size = this.cache.size
    this.cache.clear()
    console.log(`🧹 [CACHE] Cleared ${size} entries`)
  }

  // Get cache statistics
  getStats() {
    const entries = Array.from(this.cache.entries())
    const now = Date.now()
    
    const stats = {
      totalEntries: entries.length,
      validEntries: 0,
      expiredEntries: 0,
      keys: [] as string[]
    }

    entries.forEach(([key, entry]) => {
      const isExpired = (now - entry.timestamp) > entry.ttl
      if (isExpired) {
        stats.expiredEntries++
      } else {
        stats.validEntries++
        stats.keys.push(key)
      }
    })

    return stats
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, entry] of this.cache.entries()) {
      const isExpired = (now - entry.timestamp) > entry.ttl
      if (isExpired) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 [CACHE] Cleaned up ${cleanedCount} expired entries`)
    }
  }
}

// Singleton cache instance
export const cache = new MemoryCache()

// Cache key generators
export const CacheKeys = {
  dashboardStats: (userId: string, isAdmin: boolean) => 
    `dashboard-stats:${isAdmin ? 'admin' : userId}`,
  sidebarStats: (userId: string, isAdmin: boolean) => 
    `sidebar-stats:${isAdmin ? 'admin' : userId}`,
  userList: () => 'admin-users:list',
  userCount: () => 'admin-users:count'
}

// Cache TTL constants (in minutes)
export const CacheTTL = {
  DASHBOARD_STATS: 5,    // 5 minutes for dashboard stats
  USER_DATA: 10,         // 10 minutes for user data
  QUICK_STATS: 2,        // 2 minutes for sidebar quick stats
  ADMIN_DATA: 15         // 15 minutes for admin data
}

// Utility function to run cleanup periodically
let cleanupInterval: NodeJS.Timeout | null = null

export function startCacheCleanup(intervalMinutes: number = 10): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
  }

  cleanupInterval = setInterval(() => {
    cache.cleanup()
  }, intervalMinutes * 60 * 1000)

  console.log(`🔄 [CACHE] Started cleanup interval: ${intervalMinutes}min`)
}

export function stopCacheCleanup(): void {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
    cleanupInterval = null
    console.log(`⏹️ [CACHE] Stopped cleanup interval`)
  }
}
