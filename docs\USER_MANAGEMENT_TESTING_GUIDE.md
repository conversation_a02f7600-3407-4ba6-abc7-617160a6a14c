# User Management Module - Comprehensive Testing Guide

## 🎯 Overview

This document provides a comprehensive guide for testing the User Management Module's role-based access control (RBAC) and user switching functionality. The test suite ensures that the system properly enforces security policies and provides appropriate user experiences based on roles.

## 📋 Test Coverage

### 1. Role-Based Access Control Tests
- ✅ Admin users can access User Management page
- ✅ Admin users can see all users in the system  
- ❌ Regular users are restricted from accessing User Management
- ❌ Non-admin users cannot view other users' data

### 2. User Role Switching Tests
- 🔄 Authentication with different user accounts
- 🔄 Role-specific UI element verification
- 🔄 Cross-role functionality testing
- 🔄 Session persistence across role switches

### 3. Authority and Permission Tests
- 🛡️ CRUD operations permissions for admin vs non-admin users
- 🛡️ Search and filter functionality access control
- 🛡️ Data modification restrictions
- 🛡️ Unauthorized action blocking

### 4. UI/UX Role-Based Tests
- 🎨 Sidebar navigation visibility based on role
- 🎨 Role-specific UI elements (crown icons, badges)
- 🎨 Status indicators and user counts
- 🎨 Responsive design across different roles

## 🧪 Test Users

The test suite uses the following user accounts from the existing database:

| User Type | Email | Role | Expected Name | Access Level |
|-----------|-------|------|---------------|--------------|
| Admin | <EMAIL> | admin | Admin User | Full Access |
| Manager | <EMAIL> | admin | Taha Zoony | Full Access |
| Sales Personnel | <EMAIL> | admin | Ali Zoony | Full Access |
| Regular User | <EMAIL> | user | Sara Al-Mansouri | Restricted |

## 🚀 Running Tests

### Prerequisites
1. Development server running on `http://localhost:3005`
2. Playwright installed and configured
3. Test database with sample users populated

### Quick Start
```bash
# Run all User Management tests
npm run test:user-management

# Or use PowerShell script
.\scripts\run-user-management-tests.ps1

# Run specific test suites
npx playwright test __tests__/user-management-comprehensive.test.ts --grep "Role-Based Access Control"
npx playwright test __tests__/user-management-comprehensive.test.ts --grep "User Role Switching"
```

### Test Execution Options
```bash
# Run tests in headed mode (visible browser)
npx playwright test __tests__/user-management-comprehensive.test.ts --headed

# Run tests in specific browser
npx playwright test __tests__/user-management-comprehensive.test.ts --project=chromium

# Run tests with debug mode
npx playwright test __tests__/user-management-comprehensive.test.ts --debug

# Generate HTML report
npx playwright test __tests__/user-management-comprehensive.test.ts --reporter=html
```

## 📊 Test Scenarios

### Scenario 1: Admin User Journey
```typescript
1. Login as admin user (<EMAIL>)
2. Verify User Management link is visible in sidebar
3. Navigate to User Management page
4. Verify all users are visible (6+ users expected)
5. Test search functionality with "Admin" term
6. Test role filtering with "Admin" role
7. Test status filtering with "Active" status
8. Verify crown icons appear for admin users
9. Verify role and status badges display correctly
10. Logout successfully
```

### Scenario 2: Regular User Restriction
```typescript
1. Login as regular user (<EMAIL>)
2. Verify User Management link is NOT visible in sidebar
3. Attempt direct navigation to /dashboard/users
4. Verify access is blocked (redirect or error)
5. Confirm no user data is accessible
6. Logout successfully
```

### Scenario 3: Cross-Role Verification
```typescript
1. Login as first admin (<EMAIL>)
2. Record visible user count and UI elements
3. Logout and login as second admin (<EMAIL>)
4. Verify same user count and UI elements
5. Test that both admins see identical data
6. Verify role-specific elements are consistent
```

## 🔍 Test Assertions

### Access Control Assertions
- `expect(hasAccess).toBe(true)` - Admin users can access User Management
- `expect(hasAccess).toBe(false)` - Regular users cannot access User Management
- `expect(usersData.headerCount).toBeGreaterThan(0)` - Admin users see user data
- `expect(isBlocked).toBe(true)` - Unauthorized access is blocked

### UI Element Assertions
- `expect(adminElements.crownIcons).toBeGreaterThan(0)` - Crown icons visible for admins
- `expect(adminElements.roleFilter).toBe(true)` - Role filter available to admins
- `expect(roleBadges.admin).toBeGreaterThan(0)` - Admin role badges displayed
- `expect(statusBadges.active + statusBadges.inactive).toBeGreaterThan(0)` - Status badges shown

### Data Consistency Assertions
- `expect(afterFilterData.headerCount).toBe(initialData.headerCount)` - Data consistency after filtering
- `expect(managerData.headerCount).toBe(adminData.headerCount)` - Same data across admin users
- `expect(usersData.activeCount + usersData.inactiveCount).toBe(usersData.headerCount)` - Count accuracy

## 🛠️ Test Helpers

### AuthHelper
Handles user authentication and session management:
- `loginUser(userType)` - Login with specified user credentials
- `logout()` - Logout current user
- `isAuthenticated()` - Check authentication status
- `verifyUserRole(role)` - Verify user has expected role

### UserManagementHelper  
Handles User Management page interactions:
- `navigateToUserManagement()` - Navigate to User Management page
- `checkUserManagementAccess()` - Check sidebar link visibility
- `getUsersTableData()` - Extract user table data and counts
- `checkAdminUIElements()` - Verify admin-specific UI elements
- `testSearchFunctionality(term)` - Test search with given term
- `testRoleFilter(role)` - Test role filtering
- `testStatusFilter(status)` - Test status filtering

## 📈 Success Criteria

### ✅ Passing Tests Should Show:
- Admin users can access User Management page
- Admin users see all 6+ users in the system
- Regular users are blocked from accessing User Management
- Search functionality works correctly
- Role and status filtering work correctly
- UI elements display appropriately for each role
- Data consistency is maintained across operations
- Cross-browser compatibility is verified

### ❌ Failing Tests Indicate:
- Security vulnerabilities in access control
- UI elements not displaying correctly
- Data inconsistencies or corruption
- Authentication/authorization failures
- Cross-browser compatibility issues

## 🔧 Troubleshooting

### Common Issues:

1. **Tests fail with "Development server not running"**
   - Ensure `npm run dev` is running on port 3005
   - Check that the server responds to `http://localhost:3005`

2. **Authentication failures**
   - Verify test user credentials are correct
   - Check that users exist in the database
   - Ensure authentication system is working

3. **UI element not found errors**
   - Check that the User Management page loads correctly
   - Verify UI elements have expected selectors
   - Update selectors if UI has changed

4. **Data inconsistency errors**
   - Verify database has expected test data
   - Check that RLS policies are configured correctly
   - Ensure data is not being filtered unexpectedly

### Debug Commands:
```bash
# Run tests with verbose output
npx playwright test --reporter=line --verbose

# Run single test with debug
npx playwright test --debug --grep "Admin users can access"

# Generate trace for failed tests
npx playwright test --trace=on-first-retry
```

## 📊 Test Reports

After running tests, review the following reports:

1. **HTML Report**: `playwright-report/index.html`
   - Visual test results with screenshots
   - Test execution timeline
   - Error details and stack traces

2. **JSON Report**: `test-results/results.json`
   - Machine-readable test results
   - Detailed test metrics
   - Performance data

3. **Console Output**: Real-time test execution logs
   - Step-by-step test progress
   - Assertion results
   - Error messages and warnings

## 🔒 Security Considerations

The test suite verifies the following security aspects:

1. **Authentication**: Users must be logged in to access the system
2. **Authorization**: Only admin users can access User Management
3. **Data Access**: Users can only see data they're authorized to view
4. **Session Management**: Proper login/logout functionality
5. **Direct URL Access**: Unauthorized direct navigation is blocked
6. **Role Persistence**: User roles are consistently enforced

## 📝 Maintenance

### Updating Tests:
1. When adding new user roles, update `TEST_USERS` configuration
2. When UI changes, update selectors in helper functions
3. When adding new features, create corresponding test scenarios
4. Regularly review and update test assertions

### Best Practices:
- Run tests before deploying changes
- Keep test data consistent across environments
- Document any test failures and resolutions
- Regularly update test credentials and user data
