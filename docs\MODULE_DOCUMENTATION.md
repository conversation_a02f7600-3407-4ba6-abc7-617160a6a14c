# Nawras CRM - Module Documentation

## Overview

This document provides comprehensive documentation for all 11 CRM modules in the Nawras CRM application. Each module is designed to handle specific business functions while maintaining consistency in user experience and data flow.

## Module Architecture

### Common Module Structure
Each module follows a consistent structure:
```
module-name/
├── page.tsx                 # Main module page
├── components/              # Module-specific components
│   ├── module-form.tsx     # Create/edit forms
│   ├── module-table.tsx    # Data listing table
│   ├── module-detail.tsx   # Detail view
│   └── module-analytics.tsx # Analytics dashboard
├── hooks/                   # Module-specific hooks
└── types/                   # Module type definitions
```

### Shared Features
All modules include:
- **CRUD Operations**: Create, Read, Update, Delete
- **Search and Filtering**: Advanced search capabilities
- **Export/Import**: Data export and import functionality
- **Real-time Updates**: Live data synchronization
- **Role-based Access**: Permission-based feature access
- **Mobile Responsiveness**: Optimized for all devices

## 1. Dashboard Module

**Location**: `app/dashboard/page.tsx`
**Purpose**: Central hub providing overview of all CRM activities and key metrics.

### Features
- **Real-time Metrics**: Live KPIs and performance indicators
- **Quick Actions**: Fast access to common tasks
- **Recent Activities**: Timeline of recent CRM activities
- **Analytics Widgets**: Customizable dashboard widgets
- **Navigation Hub**: Quick access to all modules

### Key Components
```typescript
// Dashboard metrics widget
<MetricsWidget
  title="Total Customers"
  value={totalCustomers}
  change={customerGrowth}
  icon={Users}
  trend="up"
/>

// Quick actions panel
<QuickActions
  actions={[
    { label: "Add Customer", href: "/dashboard/customers/new" },
    { label: "Create Deal", href: "/dashboard/deals/new" },
    { label: "New Task", href: "/dashboard/tasks/new" }
  ]}
/>
```

### API Endpoints
- `GET /api/dashboard/metrics` - Dashboard KPIs
- `GET /api/dashboard/activities` - Recent activities
- `GET /api/dashboard/widgets` - Widget configurations

### Permissions
- **All Users**: View personal dashboard
- **Managers**: View team metrics
- **Admins**: View organization-wide metrics

## 2. Customers Module

**Location**: `app/dashboard/customers/`
**Purpose**: Comprehensive customer relationship management.

### Features
- **Customer Profiles**: Detailed customer information management
- **Contact Management**: Multiple contacts per customer
- **Address Management**: Multiple addresses with types
- **Document Storage**: File attachments and document management
- **Communication History**: Track all customer interactions
- **Customer Analytics**: Insights and performance metrics

### Key Components
```typescript
// Enhanced customer form
<EnhancedCustomerForm
  initialData={customer}
  onSubmit={handleSaveCustomer}
  sections={[
    'contact_information',
    'business_details',
    'financial_information',
    'shipping_preferences',
    'compliance_requirements'
  ]}
/>

// Customer analytics dashboard
<CustomerAnalytics
  customerId={customerId}
  metrics={['revenue', 'orders', 'interactions', 'satisfaction']}
  dateRange={dateRange}
/>
```

### Data Structure
```typescript
interface Customer {
  id: string
  contact_person: string
  title_position?: string
  email: string
  phone?: string
  mobile?: string
  company: string
  website?: string
  industry: string
  business_type: BusinessType
  company_size: CompanySize
  annual_volume?: number
  country: string
  city: string
  address?: string
  source: ContactSource
  status: CustomerStatus
  customer_tier: CustomerTier
  notes?: string
  tags: string[]
  created_at: string
  updated_at: string
}
```

### API Endpoints
- `GET /api/customers` - List customers
- `POST /api/customers` - Create customer
- `PUT /api/customers/[id]` - Update customer
- `DELETE /api/customers/[id]` - Delete customer
- `GET /api/customers/[id]/analytics` - Customer analytics

## 3. Companies Module

**Location**: `app/dashboard/companies/`
**Purpose**: Organization and company management.

### Features
- **Company Profiles**: Comprehensive company information
- **Relationship Mapping**: Company hierarchies and relationships
- **Industry Classification**: Industry and sector categorization
- **Financial Information**: Revenue, size, and financial data
- **Contact Directory**: Multiple contacts within companies

### Key Components
```typescript
// Company profile card
<CompanyProfile
  company={company}
  showFinancials={hasPermission('view_financials')}
  showContacts={true}
  onEdit={handleEdit}
/>

// Company relationship map
<CompanyRelationshipMap
  companyId={companyId}
  relationships={relationships}
  onAddRelationship={handleAddRelationship}
/>
```

### Data Structure
```typescript
interface Company {
  id: string
  name: string
  legal_name?: string
  industry: string
  size: CompanySize
  website?: string
  description?: string
  headquarters_country: string
  headquarters_city: string
  founded_year?: number
  annual_revenue?: number
  employee_count?: number
  main_phone?: string
  main_email?: string
}
```

## 4. Deals Module

**Location**: `app/dashboard/deals/`
**Purpose**: Sales pipeline and deal management.

### Features
- **Kanban Pipeline**: Visual deal progression through stages
- **Deal Analytics**: Revenue forecasting and pipeline analysis
- **Stage Management**: Customizable deal stages and workflows
- **Probability Tracking**: Deal probability and likelihood scoring
- **Activity Timeline**: Complete deal activity history

### Key Components
```typescript
// Kanban board for deal pipeline
<EnhancedKanbanBoard
  deals={deals}
  stages={dealStages}
  onDealMove={handleDealMove}
  onStageUpdate={handleStageUpdate}
  groupBy="stage"
  sortBy="value"
/>

// Deal form wizard
<DealFormWizard
  steps={[
    'basic_information',
    'financial_details',
    'timeline_milestones',
    'documents_notes'
  ]}
  onSubmit={handleCreateDeal}
/>
```

### Deal Stages
1. **Prospecting**: Initial contact and qualification
2. **Qualification**: Needs assessment and budget confirmation
3. **Proposal**: Formal proposal and presentation
4. **Negotiation**: Terms and pricing discussions
5. **Closed Won**: Successfully closed deal
6. **Closed Lost**: Lost opportunity with reason tracking

### Data Structure
```typescript
interface Deal {
  id: string
  title: string
  description?: string
  value: number
  currency: string
  stage: DealStage
  probability: number
  customer_id?: string
  assigned_to?: string
  expected_close_date: string
  actual_close_date?: string
  source?: string
  competitors: string[]
  next_steps?: string
  loss_reason?: string
}
```

## 5. Leads Module

**Location**: `app/dashboard/leads/`
**Purpose**: Lead generation, qualification, and conversion.

### Features
- **Lead Capture**: Multiple lead sources and capture methods
- **Lead Scoring**: Automated and manual lead scoring
- **Qualification Process**: BANT (Budget, Authority, Need, Timeline) qualification
- **Lead Nurturing**: Automated follow-up and nurturing campaigns
- **Conversion Tracking**: Lead to customer conversion analytics

### Key Components
```typescript
// Lead qualification form
<LeadQualificationForm
  lead={lead}
  qualificationCriteria={['budget', 'authority', 'need', 'timeline']}
  onQualify={handleQualification}
  onConvert={handleConversion}
/>

// Lead scoring widget
<LeadScoringWidget
  lead={lead}
  scoringFactors={scoringFactors}
  onScoreUpdate={handleScoreUpdate}
/>
```

### Lead Sources
- Website forms
- Social media campaigns
- Email marketing
- Cold outreach
- Referrals
- Trade shows
- Advertisements

### Lead Statuses
- **New**: Recently captured lead
- **Contacted**: Initial contact made
- **Qualified**: Meets qualification criteria
- **Unqualified**: Does not meet criteria
- **Converted**: Successfully converted to customer
- **Lost**: Lost opportunity

## 6. Opportunities Module

**Location**: `app/dashboard/opportunities/`
**Purpose**: Sales opportunity tracking and revenue forecasting.

### Features
- **Opportunity Pipeline**: Visual opportunity progression
- **Revenue Forecasting**: Predictive revenue analysis
- **Competitive Analysis**: Competitor tracking and analysis
- **Decision Criteria**: Customer decision factors tracking
- **Proposal Management**: Linked proposal and quote management

### Key Components
```typescript
// Opportunity pipeline view
<OpportunityPipeline
  opportunities={opportunities}
  stages={opportunityStages}
  forecastPeriod={forecastPeriod}
  onStageChange={handleStageChange}
/>

// Revenue forecast chart
<RevenueForecastChart
  opportunities={opportunities}
  period="quarterly"
  confidence="weighted"
/>
```

### Opportunity Stages
1. **Identification**: Opportunity identified
2. **Qualification**: Opportunity qualified
3. **Analysis**: Detailed analysis and planning
4. **Proposal**: Proposal development and submission
5. **Negotiation**: Contract negotiation
6. **Closed Won**: Successfully won
7. **Closed Lost**: Lost with analysis

## 7. Tasks Module

**Location**: `app/dashboard/tasks/`
**Purpose**: Task management and follow-up activities.

### Features
- **Task Creation**: Create tasks linked to CRM entities
- **Priority Management**: Task prioritization and urgency tracking
- **Assignment**: Task assignment to team members
- **Due Date Tracking**: Deadline management and reminders
- **Activity Logging**: Complete task activity history

### Key Components
```typescript
// Task management board
<TaskBoard
  tasks={tasks}
  groupBy="status"
  filterBy={taskFilters}
  onTaskUpdate={handleTaskUpdate}
  onTaskAssign={handleTaskAssignment}
/>

// Task creation form
<TaskForm
  relatedTo={{ type: 'customer', id: customerId }}
  assignees={teamMembers}
  onSubmit={handleTaskCreation}
/>
```

### Task Priorities
- **Low**: Non-urgent tasks
- **Medium**: Standard priority
- **High**: Important tasks
- **Urgent**: Critical, immediate attention required

### Task Statuses
- **Open**: New, unstarted task
- **In Progress**: Currently being worked on
- **Completed**: Successfully completed
- **Cancelled**: Cancelled or no longer needed

## 8. Reports Module

**Location**: `app/dashboard/reports/`
**Purpose**: Analytics, reporting, and business intelligence.

### Features
- **Sales Reports**: Revenue, pipeline, and performance analytics
- **Customer Reports**: Customer analytics and segmentation
- **Activity Reports**: Team and individual activity tracking
- **Custom Reports**: User-defined report creation
- **Export Capabilities**: Multiple export formats (PDF, Excel, CSV)

### Key Components
```typescript
// Report builder
<ReportBuilder
  dataSources={['customers', 'deals', 'leads', 'opportunities']}
  visualizations={['chart', 'table', 'metric']}
  onReportGenerate={handleReportGeneration}
/>

// Analytics dashboard
<AnalyticsDashboard
  widgets={reportWidgets}
  dateRange={dateRange}
  filters={reportFilters}
/>
```

### Report Types
- **Sales Performance**: Revenue, conversion rates, pipeline health
- **Customer Analytics**: Customer lifetime value, churn analysis
- **Team Performance**: Individual and team productivity metrics
- **Forecast Reports**: Revenue and sales forecasting
- **Activity Reports**: Communication and task completion tracking

## 9. Proposals Module

**Location**: `app/dashboard/proposals/`
**Purpose**: Proposal and quote management.

### Features
- **Proposal Creation**: Template-based proposal generation
- **Quote Management**: Pricing and quote calculations
- **Document Generation**: PDF proposal generation
- **Tracking**: Proposal view and response tracking
- **Approval Workflow**: Multi-stage approval process

### Key Components
```typescript
// Proposal editor
<ProposalEditor
  template={proposalTemplate}
  customer={customer}
  deal={deal}
  onSave={handleProposalSave}
  onSend={handleProposalSend}
/>

// Proposal tracking
<ProposalTracker
  proposal={proposal}
  activities={proposalActivities}
  onStatusUpdate={handleStatusUpdate}
/>
```

### Proposal Statuses
- **Draft**: Being created or edited
- **Sent**: Sent to customer
- **Viewed**: Customer has viewed proposal
- **Accepted**: Customer accepted proposal
- **Rejected**: Customer rejected proposal
- **Expired**: Proposal validity expired

## 10. Settings Module

**Location**: `app/dashboard/settings/`
**Purpose**: System configuration and user preferences.

### Features
- **User Preferences**: Personal settings and preferences
- **System Configuration**: Application-wide settings
- **Integration Settings**: Third-party integrations
- **Security Settings**: Password and security preferences
- **Notification Settings**: Email and push notification preferences

### Key Components
```typescript
// Settings panel
<SettingsPanel
  sections={[
    'profile',
    'preferences',
    'notifications',
    'security',
    'integrations'
  ]}
  onSettingsUpdate={handleSettingsUpdate}
/>

// Integration configuration
<IntegrationSettings
  integrations={availableIntegrations}
  onIntegrationToggle={handleIntegrationToggle}
/>
```

### Settings Categories
- **Profile**: User profile information
- **Preferences**: UI preferences and defaults
- **Notifications**: Communication preferences
- **Security**: Password and authentication settings
- **Integrations**: Third-party service connections

## 11. User Management Module (Admin Only)

**Location**: `app/dashboard/users/`
**Purpose**: User administration and role management.

### Features
- **User CRUD**: Create, read, update, delete users
- **Role Management**: Assign and manage user roles
- **Permission Control**: Granular permission management
- **Activity Monitoring**: User activity tracking
- **Bulk Operations**: Bulk user management operations

### Key Components
```typescript
// User management table
<UserManagementTable
  users={users}
  roles={availableRoles}
  onUserUpdate={handleUserUpdate}
  onRoleAssign={handleRoleAssignment}
  onUserDeactivate={handleUserDeactivation}
/>

// Role editor
<RoleEditor
  role={role}
  permissions={availablePermissions}
  onRoleUpdate={handleRoleUpdate}
/>
```

### User Roles
- **Admin**: Full system access and user management
- **Manager**: Team management and advanced features
- **User**: Standard CRM functionality
- **Viewer**: Read-only access to assigned data

### Permissions
- **View Data**: Access to view CRM data
- **Edit Data**: Ability to modify CRM records
- **Delete Data**: Permission to delete records
- **Manage Users**: User administration capabilities
- **View Reports**: Access to analytics and reports
- **Export Data**: Data export capabilities

This module documentation provides a comprehensive overview of all 11 CRM modules, their features, components, and implementation details. Each module is designed to work seamlessly with others while maintaining clear boundaries and responsibilities.
