"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  MobileHeader, 
  MobileLayoutWrapper, 
  MobileTable,
  TouchButton,
  PullToRefresh,
  type MobileTableColumn,
  type MobileTableAction
} from "@/components/mobile"
import { MetricCard } from "@/components/tables"
import { CustomerFormData, CustomerDatabaseSchema } from "@/app/types/customer"
import { 
  Plus, Search, Filter, Users, TrendingUp, DollarSign, 
  Star, Eye, Edit, Trash2, Phone, Mail, MapPin, Building2
} from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"
import { cn } from "@/lib/utils"

interface MobileCustomersPageProps {
  customers: CustomerDatabaseSchema[]
  loading?: boolean
  onAddCustomer?: () => void
  onViewCustomer?: (customer: CustomerDatabaseSchema) => void
  onEditCustomer?: (customer: CustomerDatabaseSchema) => void
  onDeleteCustomer?: (customerId: string) => void
  onRefresh?: () => Promise<void>
  className?: string
}

export function MobileCustomersPage({
  customers,
  loading = false,
  onAddCustomer,
  onViewCustomer,
  onEditCustomer,
  onDeleteCustomer,
  onRefresh,
  className
}: MobileCustomersPageProps) {
  const isMobile = useIsMobile()
  const [searchQuery, setSearchQuery] = useState("")
  const [showFilters, setShowFilters] = useState(false)

  // Calculate metrics
  const totalCustomers = customers.length
  const activeCustomers = customers.filter(c => c.status === "Active" || !c.status).length
  const totalVolume = customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0)
  const avgVolume = totalCustomers > 0 ? totalVolume / totalCustomers : 0

  // Mobile table columns configuration
  const mobileColumns: MobileTableColumn<CustomerDatabaseSchema>[] = [
    {
      id: "contact_person",
      label: "Contact Person",
      accessor: "contact_person",
      priority: "high",
      render: (customer) => (
        <div>
          <p className="font-medium">{customer.contact_person}</p>
          {customer.title_position && (
            <p className="text-xs text-muted-foreground">{customer.title_position}</p>
          )}
        </div>
      )
    },
    {
      id: "company",
      label: "Company",
      accessor: "company",
      priority: "high",
      render: (customer) => (
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4 text-muted-foreground" />
          <span>{customer.company}</span>
        </div>
      )
    },
    {
      id: "email",
      label: "Email",
      accessor: "email",
      priority: "medium",
      render: (customer) => customer.email ? (
        <div className="flex items-center gap-2">
          <Mail className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{customer.email}</span>
        </div>
      ) : null
    },
    {
      id: "phone",
      label: "Phone",
      accessor: "phone",
      priority: "medium",
      render: (customer) => customer.phone ? (
        <div className="flex items-center gap-2">
          <Phone className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{customer.phone}</span>
        </div>
      ) : null
    },
    {
      id: "location",
      label: "Location",
      priority: "medium",
      render: (customer) => (
        <div className="flex items-center gap-2">
          <MapPin className="h-3 w-3 text-muted-foreground" />
          <span className="text-sm">{customer.city}, {customer.country}</span>
        </div>
      )
    },
    {
      id: "customer_tier",
      label: "Tier",
      accessor: "customer_tier",
      priority: "low",
      render: (customer) => {
        const tierColors = {
          "VIP": "bg-purple-100 text-purple-800",
          "Platinum": "bg-gray-100 text-gray-800",
          "Gold": "bg-yellow-100 text-yellow-800",
          "Silver": "bg-gray-100 text-gray-600",
          "Bronze": "bg-orange-100 text-orange-800"
        }
        
        return (
          <Badge 
            variant="secondary" 
            className={cn(tierColors[customer.customer_tier as keyof typeof tierColors])}
          >
            <Star className="h-3 w-3 mr-1" />
            {customer.customer_tier || "Bronze"}
          </Badge>
        )
      }
    },
    {
      id: "annual_volume",
      label: "Annual Volume",
      accessor: "annual_volume",
      priority: "low",
      render: (customer) => customer.annual_volume ? (
        <span className="text-sm font-medium">
          ${customer.annual_volume.toLocaleString()}
        </span>
      ) : null
    },
    {
      id: "status",
      label: "Status",
      accessor: "status",
      priority: "low",
      render: (customer) => {
        const statusColors = {
          "Active": "bg-green-100 text-green-800",
          "Inactive": "bg-gray-100 text-gray-800",
          "Pending": "bg-yellow-100 text-yellow-800",
          "Suspended": "bg-red-100 text-red-800"
        }
        
        return (
          <Badge 
            variant="secondary"
            className={cn(statusColors[customer.status as keyof typeof statusColors] || "bg-gray-100 text-gray-800")}
          >
            {customer.status || "Active"}
          </Badge>
        )
      }
    }
  ]

  // Mobile table actions
  const mobileActions: MobileTableAction<CustomerDatabaseSchema>[] = [
    {
      label: "View",
      icon: Eye,
      onClick: (customer) => onViewCustomer?.(customer)
    },
    {
      label: "Edit",
      icon: Edit,
      onClick: (customer) => onEditCustomer?.(customer)
    },
    {
      label: "Delete",
      icon: Trash2,
      onClick: (customer) => onDeleteCustomer?.(customer.id!),
      variant: "destructive"
    }
  ]

  // Filter customers based on search
  const filteredCustomers = customers.filter(customer => {
    if (!searchQuery) return true
    
    const searchLower = searchQuery.toLowerCase()
    return (
      customer.contact_person?.toLowerCase().includes(searchLower) ||
      customer.company?.toLowerCase().includes(searchLower) ||
      customer.email?.toLowerCase().includes(searchLower) ||
      customer.phone?.toLowerCase().includes(searchLower)
    )
  })

  const handleRefresh = async () => {
    if (onRefresh) {
      await onRefresh()
    }
  }

  if (!isMobile) {
    return null // Use regular desktop layout
  }

  return (
    <MobileLayoutWrapper hasBottomNav={true} className={className}>
      {/* Mobile Header */}
      <MobileHeader
        title="Customers"
        subtitle={`${totalCustomers} customers`}
        actions={
          <div className="flex items-center gap-2">
            <TouchButton
              variant="ghost"
              size="sm"
              onPress={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
            </TouchButton>
            <TouchButton
              variant="default"
              size="sm"
              onPress={onAddCustomer}
            >
              <Plus className="h-4 w-4" />
            </TouchButton>
          </div>
        }
      />

      {/* Content with Pull to Refresh */}
      <PullToRefresh onRefresh={handleRefresh}>
        <div className="p-4 space-y-4">
          {/* Metrics Cards */}
          <div className="grid grid-cols-2 gap-3">
            <MetricCard
              metric={{
                label: "Total Customers",
                value: totalCustomers,
                icon: Users,
                color: "primary"
              }}
              size="sm"
            />
            <MetricCard
              metric={{
                label: "Active",
                value: activeCustomers,
                icon: TrendingUp,
                color: "success"
              }}
              size="sm"
            />
            <MetricCard
              metric={{
                label: "Total Volume",
                value: totalVolume,
                format: "currency",
                icon: DollarSign,
                color: "info"
              }}
              size="sm"
            />
            <MetricCard
              metric={{
                label: "Avg Volume",
                value: avgVolume,
                format: "currency",
                icon: Star,
                color: "warning"
              }}
              size="sm"
            />
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search customers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border rounded-lg text-sm bg-background"
            />
          </div>

          {/* Filters (if shown) */}
          {showFilters && (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Filters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Status</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <Badge variant="outline" className="cursor-pointer">All</Badge>
                    <Badge variant="outline" className="cursor-pointer">Active</Badge>
                    <Badge variant="outline" className="cursor-pointer">Inactive</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-xs font-medium text-muted-foreground">Tier</label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <Badge variant="outline" className="cursor-pointer">All</Badge>
                    <Badge variant="outline" className="cursor-pointer">VIP</Badge>
                    <Badge variant="outline" className="cursor-pointer">Gold</Badge>
                    <Badge variant="outline" className="cursor-pointer">Silver</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Mobile Table */}
          <MobileTable
            data={filteredCustomers}
            columns={mobileColumns}
            keyExtractor={(customer) => customer.id || customer.contact_person || ""}
            actions={mobileActions}
            onItemClick={onViewCustomer}
            loading={loading}
            emptyMessage="No customers found"
            variant="card"
            searchable={false} // We handle search above
            sortable={true}
            filterable={false} // We handle filters above
          />

          {/* Quick Actions */}
          <div className="grid grid-cols-2 gap-3 pt-4">
            <TouchButton
              variant="outline"
              className="h-12 flex-col gap-1"
              onPress={onAddCustomer}
            >
              <Plus className="h-5 w-5" />
              <span className="text-xs">Add Customer</span>
            </TouchButton>
            <TouchButton
              variant="outline"
              className="h-12 flex-col gap-1"
              onPress={() => {/* Handle import */}}
            >
              <Users className="h-5 w-5" />
              <span className="text-xs">Import</span>
            </TouchButton>
          </div>

          {/* Bottom spacing for navigation */}
          <div className="h-4" />
        </div>
      </PullToRefresh>
    </MobileLayoutWrapper>
  )
}
