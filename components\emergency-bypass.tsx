'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

interface EmergencyBypassProps {
  children: React.ReactNode
  loading: boolean
  user: any
  error: string | null
}

export default function EmergencyBypass({ children, loading, user, error }: EmergencyBypassProps) {
  const [bypassActive, setBypassActive] = useState(false)
  const [showBypassOption, setShowBypassOption] = useState(false)
  const router = useRouter()

  // Show bypass option after 8 seconds of loading
  useEffect(() => {
    if (loading && !user && !error) {
      const timer = setTimeout(() => {
        setShowBypassOption(true)
        console.log('🚧 Emergency bypass option available')
      }, 8000)

      return () => clearTimeout(timer)
    } else {
      setShowBypassOption(false)
    }
  }, [loading, user, error])

  // Auto-activate bypass after 15 seconds
  useEffect(() => {
    if (loading && !user && !error) {
      const timer = setTimeout(() => {
        console.log('🚨 Auto-activating emergency bypass after 15 seconds')
        setBypassActive(true)
      }, 15000)

      return () => clearTimeout(timer)
    }
  }, [loading, user, error])

  const activateBypass = () => {
    console.log('🚧 Emergency bypass activated manually')
    setBypassActive(true)
  }

  // If bypass is active, render children with mock user
  if (bypassActive) {
    return (
      <div>
        <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm">
                <strong>Emergency Bypass Active:</strong> Authentication is temporarily bypassed. 
                Some features may be limited. Please refresh the page to retry normal authentication.
              </p>
            </div>
          </div>
        </div>
        {children}
      </div>
    )
  }

  // If authentication is working normally, render children
  if (!loading && user) {
    return <>{children}</>
  }

  // If there's an error, show error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Authentication Error
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              {error}
            </p>
            <button
              onClick={() => router.push('/login')}
              className="mt-4 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Return to Login
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Show loading state with optional bypass
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Authenticating...
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Please wait while we verify your credentials
          </p>
          
          {showBypassOption && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800 mb-3">
                Authentication is taking longer than expected.
              </p>
              <button
                onClick={activateBypass}
                className="w-full flex justify-center py-2 px-4 border border-yellow-300 rounded-md shadow-sm text-sm font-medium text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                🚧 Continue with Emergency Bypass
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
