# Nawras CRM - API Documentation

## Overview

This document provides comprehensive documentation for all API endpoints in the Nawras CRM application. The API follows RESTful conventions and uses Supabase for data persistence and authentication.

## Base Configuration

### Base URL
- **Development**: `http://localhost:3000/api`
- **Production**: `https://sales.nawrasinchina.com/api`

### Authentication
All API endpoints require authentication via Supabase session cookies. The authentication is handled automatically by the Supabase client.

### Response Format
All API responses follow a consistent format:

```typescript
// Success Response
{
  data: T, // Response data
  success: true,
  message?: string
}

// Error Response
{
  error: string,
  success: false,
  status: number
}
```

## Customers API

### Base Path: `/api/customers`

#### GET /api/customers
Retrieve all customers for the authenticated user.

**Query Parameters:**
- `page` (optional): Page number for pagination (default: 1)
- `limit` (optional): Number of items per page (default: 50)
- `search` (optional): Search term for filtering customers
- `status` (optional): Filter by customer status
- `country` (optional): Filter by country

**Response:**
```typescript
{
  data: Customer[],
  pagination: {
    page: number,
    limit: number,
    total: number,
    totalPages: number
  }
}
```

**Example:**
```bash
GET /api/customers?page=1&limit=20&search=acme&status=Active
```

#### POST /api/customers
Create a new customer.

**Request Body:**
```typescript
{
  contact_person: string,
  email: string,
  phone?: string,
  company: string,
  country: string,
  city: string,
  source: ContactSource,
  status: CustomerStatus,
  notes?: string,
  // Additional fields...
}
```

**Response:**
```typescript
{
  data: Customer,
  success: true,
  message: "Customer created successfully"
}
```

#### PUT /api/customers/[id]
Update an existing customer.

**Path Parameters:**
- `id`: Customer ID (UUID)

**Request Body:** Same as POST, all fields optional

**Response:**
```typescript
{
  data: Customer,
  success: true,
  message: "Customer updated successfully"
}
```

#### DELETE /api/customers/[id]
Delete a customer.

**Path Parameters:**
- `id`: Customer ID (UUID)

**Response:**
```typescript
{
  success: true,
  message: "Customer deleted successfully"
}
```

#### GET /api/customers/analytics
Get customer analytics data.

**Query Parameters:**
- `type`: Analysis type (`overview`, `trends`, `segments`, `performance`)
- `from`: Start date (ISO string)
- `to`: End date (ISO string)
- `segment`: Customer segment filter

**Response:**
```typescript
{
  data: {
    overview: {
      totalCustomers: number,
      activeCustomers: number,
      newCustomers: number,
      churnRate: number
    },
    trends: CustomerTrend[],
    segments: CustomerSegment[],
    performance: PerformanceMetric[]
  }
}
```

## Deals API

### Base Path: `/api/deals`

#### GET /api/deals
Retrieve all deals for the authenticated user.

**Query Parameters:**
- `stage` (optional): Filter by deal stage
- `status` (optional): Filter by deal status
- `customer_id` (optional): Filter by customer
- `date_from` (optional): Filter deals created after date
- `date_to` (optional): Filter deals created before date

**Response:**
```typescript
{
  data: Deal[],
  pagination: PaginationInfo
}
```

#### POST /api/deals
Create a new deal.

**Request Body:**
```typescript
{
  title: string,
  description?: string,
  value: number,
  currency: string,
  stage: DealStage,
  probability: number,
  expected_close_date: string,
  customer_id: string,
  assigned_to?: string
}
```

#### PUT /api/deals/[id]
Update an existing deal.

#### DELETE /api/deals/[id]
Delete a deal.

#### POST /api/deals/[id]/stage
Update deal stage.

**Request Body:**
```typescript
{
  stage: DealStage,
  notes?: string
}
```

## Companies API

### Base Path: `/api/companies`

#### GET /api/companies
Retrieve all companies.

#### POST /api/companies
Create a new company.

**Request Body:**
```typescript
{
  name: string,
  industry: string,
  size: CompanySize,
  website?: string,
  description?: string,
  headquarters: {
    country: string,
    city: string,
    address?: string
  }
}
```

## Leads API

### Base Path: `/api/leads`

#### GET /api/leads
Retrieve all leads.

**Query Parameters:**
- `status`: Filter by lead status
- `source`: Filter by lead source
- `score_min`: Minimum lead score
- `score_max`: Maximum lead score

#### POST /api/leads
Create a new lead.

**Request Body:**
```typescript
{
  name: string,
  email: string,
  phone?: string,
  company?: string,
  source: LeadSource,
  status: LeadStatus,
  score?: number,
  notes?: string
}
```

#### POST /api/leads/[id]/convert
Convert lead to customer.

**Request Body:**
```typescript
{
  customer_data: CustomerFormData
}
```

## Opportunities API

### Base Path: `/api/opportunities`

#### GET /api/opportunities
Retrieve all opportunities.

#### POST /api/opportunities
Create a new opportunity.

**Request Body:**
```typescript
{
  title: string,
  description?: string,
  value: number,
  probability: number,
  stage: OpportunityStage,
  expected_close_date: string,
  customer_id?: string,
  lead_id?: string
}
```

## Tasks API

### Base Path: `/api/tasks`

#### GET /api/tasks
Retrieve all tasks.

**Query Parameters:**
- `status`: Filter by task status
- `priority`: Filter by priority
- `assigned_to`: Filter by assignee
- `due_date`: Filter by due date

#### POST /api/tasks
Create a new task.

**Request Body:**
```typescript
{
  title: string,
  description?: string,
  priority: TaskPriority,
  status: TaskStatus,
  due_date?: string,
  assigned_to?: string,
  related_to?: {
    type: 'customer' | 'deal' | 'lead' | 'opportunity',
    id: string
  }
}
```

## Reports API

### Base Path: `/api/reports`

#### GET /api/reports/sales
Get sales reports.

**Query Parameters:**
- `period`: Time period (`week`, `month`, `quarter`, `year`)
- `from`: Start date
- `to`: End date

#### GET /api/reports/customers
Get customer reports.

#### GET /api/reports/performance
Get performance reports.

## Users API (Admin Only)

### Base Path: `/api/users`

#### GET /api/users
Retrieve all users (Admin only).

#### POST /api/users
Create a new user (Admin only).

**Request Body:**
```typescript
{
  email: string,
  full_name: string,
  role: UserRole,
  permissions: Permission[]
}
```

#### PUT /api/users/[id]
Update user (Admin only).

#### DELETE /api/users/[id]
Delete user (Admin only).

## Health Check API

### Base Path: `/api/health`

#### GET /api/health
Check API health status.

**Response:**
```typescript
{
  status: "healthy" | "unhealthy",
  timestamp: string,
  version: string,
  database: {
    status: "connected" | "disconnected",
    latency: number
  },
  auth: {
    status: "operational" | "degraded"
  }
}
```

## Error Handling

### Common Error Codes
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `409`: Conflict - Resource already exists
- `422`: Unprocessable Entity - Validation errors
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server error

### Error Response Format
```typescript
{
  error: string,
  details?: {
    field?: string,
    code?: string,
    message?: string
  }[],
  timestamp: string,
  path: string
}
```

## Rate Limiting

### Limits
- **General API**: 100 requests per minute per user
- **Authentication**: 10 requests per minute per IP
- **File Upload**: 5 requests per minute per user

### Headers
Rate limit information is included in response headers:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Data Types

### Customer Types
```typescript
interface Customer {
  id: string
  user_id: string
  contact_person: string
  email: string
  phone?: string
  company: string
  country: string
  city: string
  source: ContactSource
  status: CustomerStatus
  notes?: string
  created_at: string
  updated_at: string
}

type ContactSource = 'Website' | 'Email' | 'Phone' | 'Social Media' | 'Referral' | 'Trade Show' | 'Cold Call' | 'Other'
type CustomerStatus = 'Active' | 'Inactive' | 'Prospect' | 'Churned'
```

### Deal Types
```typescript
interface Deal {
  id: string
  user_id: string
  title: string
  description?: string
  value: number
  currency: string
  stage: DealStage
  probability: number
  expected_close_date: string
  customer_id?: string
  assigned_to?: string
  created_at: string
  updated_at: string
}

type DealStage = 'Prospecting' | 'Qualification' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost'
```

## Authentication Examples

### Using Fetch API
```javascript
// API calls are automatically authenticated via Supabase session cookies
const response = await fetch('/api/customers', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
})

const data = await response.json()
```

### Error Handling Example
```javascript
try {
  const response = await fetch('/api/customers', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(customerData)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'API request failed')
  }

  const result = await response.json()
  return result.data
} catch (error) {
  console.error('API Error:', error)
  throw error
}
```

## Testing API Endpoints

### Using curl
```bash
# Get customers
curl -X GET "https://sales.nawrasinchina.com/api/customers" \
  -H "Content-Type: application/json"

# Create customer
curl -X POST "https://sales.nawrasinchina.com/api/customers" \
  -H "Content-Type: application/json" \
  -d '{
    "contact_person": "John Doe",
    "email": "<EMAIL>",
    "company": "Example Corp",
    "country": "Jordan",
    "city": "Amman",
    "source": "Website",
    "status": "Active"
  }'
```

## Settings API

### Base Path: `/api/settings`

#### GET /api/settings
Get user settings and preferences.

#### PUT /api/settings
Update user settings.

**Request Body:**
```typescript
{
  theme: 'light' | 'dark' | 'system',
  language: 'en' | 'ar',
  notifications: {
    email: boolean,
    push: boolean,
    sms: boolean
  },
  dashboard: {
    defaultView: string,
    widgets: string[]
  }
}
```

## Proposals API

### Base Path: `/api/proposals`

#### GET /api/proposals
Retrieve all proposals.

#### POST /api/proposals
Create a new proposal.

**Request Body:**
```typescript
{
  title: string,
  description?: string,
  customer_id: string,
  deal_id?: string,
  total_amount: number,
  currency: string,
  valid_until: string,
  items: ProposalItem[]
}
```

#### PUT /api/proposals/[id]
Update proposal.

#### POST /api/proposals/[id]/send
Send proposal to customer.

#### GET /api/proposals/[id]/pdf
Generate PDF version of proposal.

This API documentation provides a comprehensive reference for all available endpoints in the Nawras CRM application. For additional support or questions, please refer to the development team.
