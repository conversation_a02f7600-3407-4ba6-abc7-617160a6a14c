"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useSession, signOut } from "next-auth/react"
import { 
  AlertTriangle, 
  RefreshCw, 
  Settings, 
  Zap,
  CheckCircle,
  ExternalLink
} from "lucide-react"

interface AuthErrorHandlerProps {
  error: string
  onRetry: () => void
}

export function AuthErrorHandler({ error, onRetry }: AuthErrorHandlerProps) {
  const { data: session } = useSession()
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [bypassActive, setBypassActive] = useState(false)

  // Bypass functions for NextAuth.js compatibility
  const activateBypass = () => setBypassActive(true)
  const activateEmergencyBypass = () => setBypassActive(true)
  
  const isTimeoutError = error.includes('timeout') || error.includes('Session request timeout')
  const isConnectionError = error.includes('connection') || error.includes('network')
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 p-3 bg-destructive/10 rounded-full w-fit">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">Authentication Issue</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {error}
            </AlertDescription>
          </Alert>

          {/* Quick Actions */}
          <div className="space-y-3">
            <Button 
              onClick={onRetry} 
              className="w-full"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>

            {isTimeoutError && (
              <Button 
                onClick={activateBypass} 
                className="w-full"
                variant="outline"
              >
                <Zap className="h-4 w-4 mr-2" />
                Activate Testing Mode
              </Button>
            )}
          </div>

          {/* Advanced Options */}
          <div className="pt-4 border-t">
            <Button
              onClick={() => setShowAdvanced(!showAdvanced)}
              variant="ghost"
              size="sm"
              className="w-full text-muted-foreground"
            >
              <Settings className="h-4 w-4 mr-2" />
              Advanced Options
            </Button>

            {showAdvanced && (
              <div className="mt-3 space-y-2">
                <Button
                  onClick={activateEmergencyBypass}
                  variant="outline"
                  size="sm"
                  className="w-full text-orange-600 border-orange-200 hover:bg-orange-50"
                >
                  <Zap className="h-4 w-4 mr-2" />
                  Emergency Bypass
                </Button>
                
                <Button
                  onClick={() => window.location.href = 'https://sales.nawrasinchina.com/dashboard/deals?bypass=true'}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Direct Access with Bypass
                </Button>
              </div>
            )}
          </div>

          {/* Status Information */}
          {bypassActive && (
            <Alert className="bg-green-50 border-green-200">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                Bypass mode is currently active. You can access the CRM functionality.
              </AlertDescription>
            </Alert>
          )}

          {/* Troubleshooting Tips */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p><strong>Common solutions:</strong></p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>Check your internet connection</li>
              <li>Try refreshing the page</li>
              <li>Clear browser cache and cookies</li>
              {isTimeoutError && <li>Use testing mode for immediate access</li>}
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
