"use client"

import * as React from "react"
import { useFormValidation, FieldValidation, ValidationErrors } from "./form-validation"

// Form configuration types
export interface FormConfig<T = any> {
  initialData?: Partial<T>
  validation?: FieldValidation
  onSubmit?: (data: T) => Promise<void> | void
  onCancel?: () => void
  onReset?: () => void
  autoSave?: boolean
  autoSaveDelay?: number
  persistKey?: string
}

export interface FormState<T = any> {
  data: T
  originalData: T
  isDirty: boolean
  isSubmitting: boolean
  isValid: boolean
  errors: ValidationErrors
  touched: Record<string, boolean>
}

export interface FormActions<T = any> {
  setField: (field: keyof T, value: any) => void
  setFields: (fields: Partial<T>) => void
  setData: (data: T) => void
  resetForm: () => void
  submitForm: () => Promise<void>
  validateField: (field: keyof T) => void
  validateForm: () => boolean
  clearErrors: () => void
  markFieldTouched: (field: keyof T) => void
  isDirtyField: (field: keyof T) => boolean
}

// Auto-save hook
function useAutoSave<T>(
  data: T,
  onSave: (data: T) => void,
  delay: number = 2000,
  enabled: boolean = false
) {
  const timeoutRef = React.useRef<NodeJS.Timeout>()
  const lastSavedRef = React.useRef<T>(data)

  React.useEffect(() => {
    if (!enabled) return

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Check if data has changed
    const hasChanged = JSON.stringify(data) !== JSON.stringify(lastSavedRef.current)
    
    if (hasChanged) {
      timeoutRef.current = setTimeout(() => {
        onSave(data)
        lastSavedRef.current = data
      }, delay)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, onSave, delay, enabled])
}

// Form persistence hook
function useFormPersistence<T>(
  key: string,
  initialData: T,
  enabled: boolean = false
): [T, (data: T) => void, () => void] {
  const [data, setData] = React.useState<T>(() => {
    if (!enabled || typeof window === "undefined") {
      return initialData
    }

    try {
      const saved = localStorage.getItem(`form_${key}`)
      return saved ? JSON.parse(saved) : initialData
    } catch {
      return initialData
    }
  })

  const saveData = React.useCallback((newData: T) => {
    setData(newData)
    if (enabled && typeof window !== "undefined") {
      try {
        localStorage.setItem(`form_${key}`, JSON.stringify(newData))
      } catch (error) {
        console.warn("Failed to save form data to localStorage:", error)
      }
    }
  }, [key, enabled])

  const clearData = React.useCallback(() => {
    if (enabled && typeof window !== "undefined") {
      try {
        localStorage.removeItem(`form_${key}`)
      } catch (error) {
        console.warn("Failed to clear form data from localStorage:", error)
      }
    }
  }, [key, enabled])

  return [data, saveData, clearData]
}

// Main form hook
export function useForm<T extends Record<string, any>>(config: FormConfig<T>) {
  const {
    initialData = {} as T,
    validation = {},
    onSubmit,
    onCancel,
    onReset,
    autoSave = false,
    autoSaveDelay = 2000,
    persistKey
  } = config

  // Form persistence
  const [persistedData, setPersistedData, clearPersistedData] = useFormPersistence(
    persistKey || "default",
    initialData,
    !!persistKey
  )

  // Form state
  const [data, setData] = React.useState<T>(persistKey ? persistedData as T : initialData as T)
  const [originalData] = React.useState<T>(initialData as T)
  const [isSubmitting, setIsSubmitting] = React.useState(false)

  // Validation
  const {
    errors,
    touched,
    isValid,
    validateSingleField,
    validateAllFields,
    touchField,
    clearAllErrors,
    getFieldError,
    hasFieldError
  } = useFormValidation({ fields: validation })

  // Calculate if form is dirty
  const isDirty = React.useMemo(() => {
    return JSON.stringify(data) !== JSON.stringify(originalData)
  }, [data, originalData])

  // Auto-save functionality
  useAutoSave(
    data,
    (dataToSave) => {
      if (persistKey) {
        setPersistedData(dataToSave)
      }
    },
    autoSaveDelay,
    autoSave
  )

  // Form actions
  const setField = React.useCallback((field: keyof T, value: any) => {
    setData(prev => {
      const newData = { ...prev, [field]: value }
      
      // Validate field if it has been touched
      if (touched[field as string]) {
        validateSingleField(field as string, value)
      }
      
      return newData
    })
  }, [touched, validateSingleField])

  const setFields = React.useCallback((fields: Partial<T>) => {
    setData(prev => ({ ...prev, ...fields }))
  }, [])

  const setFormData = React.useCallback((newData: T) => {
    setData(newData)
  }, [])

  const resetForm = React.useCallback(() => {
    setData(initialData as T)
    clearAllErrors()
    setIsSubmitting(false)
    
    if (persistKey) {
      clearPersistedData()
    }
    
    if (onReset) {
      onReset()
    }
  }, [initialData, clearAllErrors, persistKey, clearPersistedData, onReset])

  const validateField = React.useCallback((field: keyof T) => {
    touchField(field as string)
    validateSingleField(field as string, data[field])
  }, [touchField, validateSingleField, data])

  const validateForm = React.useCallback(() => {
    const formErrors = validateAllFields(data, true)
    return Object.keys(formErrors).length === 0
  }, [validateAllFields, data])

  const submitForm = React.useCallback(async () => {
    if (!onSubmit) return

    // Validate form
    const isFormValid = validateForm()
    if (!isFormValid) {
      return
    }

    setIsSubmitting(true)
    
    try {
      await onSubmit(data)
      
      // Clear persisted data on successful submit
      if (persistKey) {
        clearPersistedData()
      }
    } catch (error) {
      console.error("Form submission error:", error)
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }, [onSubmit, validateForm, data, persistKey, clearPersistedData])

  const markFieldTouched = React.useCallback((field: keyof T) => {
    touchField(field as string)
  }, [touchField])

  const isDirtyField = React.useCallback((field: keyof T) => {
    return data[field] !== originalData[field]
  }, [data, originalData])

  // Form state object
  const formState: FormState<T> = {
    data,
    originalData,
    isDirty,
    isSubmitting,
    isValid,
    errors,
    touched
  }

  // Form actions object
  const formActions: FormActions<T> = {
    setField,
    setFields,
    setData: setFormData,
    resetForm,
    submitForm,
    validateField,
    validateForm,
    clearErrors: clearAllErrors,
    markFieldTouched,
    isDirtyField
  }

  // Helper functions for field props
  const getFieldProps = React.useCallback((field: keyof T) => {
    return {
      value: data[field],
      onChange: (value: any) => setField(field, value),
      onBlur: () => validateField(field),
      error: getFieldError(field as string),
      required: validation[field as string]?.required || false
    }
  }, [data, setField, validateField, getFieldError, validation])

  const getError = React.useCallback((field: keyof T) => {
    return getFieldError(field as string)
  }, [getFieldError])

  const hasError = React.useCallback((field: keyof T) => {
    return hasFieldError(field as string)
  }, [hasFieldError])

  return {
    // State
    ...formState,
    
    // Actions
    ...formActions,
    
    // Helpers
    getFieldProps,
    getFieldError: getError,
    hasError,
    
    // Handlers for common events
    handleSubmit: (e: React.FormEvent) => {
      e.preventDefault()
      submitForm()
    },
    
    handleCancel: () => {
      if (onCancel) {
        onCancel()
      }
    },
    
    handleReset: () => {
      resetForm()
    }
  }
}

// Form context for sharing form state across components
export const FormContext = React.createContext<{
  formState?: FormState<any>
  formActions?: FormActions<any>
  getFieldProps?: (field: any) => any
} | null>(null)

export function FormProvider<T extends Record<string, any>>({ 
  children, 
  form 
}: { 
  children: React.ReactNode
  form: ReturnType<typeof useForm<T>>
}) {
  const contextValue = React.useMemo(() => ({
    formState: {
      data: form.data,
      originalData: form.originalData,
      isDirty: form.isDirty,
      isSubmitting: form.isSubmitting,
      isValid: form.isValid,
      errors: form.errors,
      touched: form.touched
    },
    formActions: {
      setField: form.setField,
      setFields: form.setFields,
      setData: form.setData,
      resetForm: form.resetForm,
      submitForm: form.submitForm,
      validateField: form.validateField,
      validateForm: form.validateForm,
      clearErrors: form.clearErrors,
      markFieldTouched: form.markFieldTouched,
      isDirtyField: form.isDirtyField
    },
    getFieldProps: form.getFieldProps
  }), [form])

  return (
    <FormContext.Provider value={contextValue as any}>
      {children}
    </FormContext.Provider>
  )
}

export function useFormContext() {
  const context = React.useContext(FormContext)
  if (!context) {
    throw new Error("useFormContext must be used within a FormProvider")
  }
  return context
}
