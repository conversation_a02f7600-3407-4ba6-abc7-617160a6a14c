"use client"

import { useState } from "react"
import { FormLayout, FormSection, FormFieldGroup } from "@/components/forms/form-layout"
import { FormField } from "@/components/forms/form-field"
import { useForm } from "@/components/forms/use-form"
import { COMMON_VALIDATIONS } from "@/components/forms/form-validation"
import { CustomerFormData } from "@/app/types/customer"
import { 
  User, Building2, CreditCard, Truck, Star, 
  Mail, Phone, MapPin, Globe, Calendar
} from "lucide-react"

interface StandardizedCustomerFormProps {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export function StandardizedCustomerForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false
}: StandardizedCustomerFormProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(["contact"]))

  console.log("🚀 [DEBUG] StandardizedCustomerForm component rendering")

  // Form configuration with our standardized hook
  const form = useForm<CustomerFormData>({
    initialData: {
      contact_person: "",
      title_position: "",
      email: "",
      phone: "",
      mobile: "",
      company: "",
      website: "",
      city: "",
      country: "Jordan",
      business_type: undefined,
      industry: "",
      annual_volume: undefined,
      company_size: undefined,
      tax_id: "",
      credit_limit: undefined,
      payment_terms: undefined,
      currency_preference: "USD",
      preferred_shipping_method: undefined,
      preferred_incoterms: undefined,
      shipping_instructions: "",
      account_manager: "",
      customer_since: "",
      customer_tier: "Bronze",
      tags: [],
      required_certificates: [],
      compliance_requirements: [],
      source: "Website",
      status: "Active",
      notes: "",
      ...initialData
    },
    validation: COMMON_VALIDATIONS.customer,
    onSubmit,
    onCancel,
    autoSave: true,
    persistKey: "customer-form"
  })

  console.log("🔍 [DEBUG] StandardizedCustomerForm form state:", {
    isValid: form.isValid,
    data: form.data,
    isSubmitting: form.isSubmitting
  })

  // Form sections configuration
  const formSections = [
    {
      id: "contact",
      title: "Contact Information",
      description: "Primary contact details for this customer",
      icon: User,
      required: true,
      fields: ["contact_person", "title_position", "email", "phone", "mobile"],
      validation: (data: CustomerFormData) => {
        return !!(data.contact_person && data.email && data.company)
      }
    },
    {
      id: "company",
      title: "Company Details",
      description: "Company information and location",
      icon: Building2,
      required: true,
      fields: ["company", "website", "city", "country", "industry"],
      validation: (data: CustomerFormData) => {
        return !!(data.company && data.city && data.country)
      }
    },
    {
      id: "business",
      title: "Business Information",
      description: "Business classification and metrics",
      icon: Star,
      required: false,
      fields: ["business_type", "annual_volume", "company_size", "customer_tier"],
      validation: () => true
    },
    {
      id: "financial",
      title: "Financial Details",
      description: "Credit limits and payment preferences",
      icon: CreditCard,
      required: false,
      fields: ["tax_id", "credit_limit", "payment_terms", "currency_preference"],
      validation: () => true
    },
    {
      id: "shipping",
      title: "Shipping Preferences",
      description: "Delivery and logistics preferences",
      icon: Truck,
      required: false,
      fields: ["preferred_shipping_method", "preferred_incoterms", "shipping_instructions"],
      validation: () => true
    }
  ]

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const getSectionCompletion = (section: typeof formSections[0]) => {
    return section.validation(form.data)
  }

  const getFormProgress = () => {
    const requiredSections = formSections.filter(s => s.required)
    const completedRequired = requiredSections.filter(getSectionCompletion).length
    const totalRequired = requiredSections.length

    const optionalSections = formSections.filter(s => !s.required)
    const completedOptional = optionalSections.filter(getSectionCompletion).length
    const totalOptional = optionalSections.length

    const requiredWeight = 0.7
    const optionalWeight = 0.3

    const requiredProgress = totalRequired > 0 ? (completedRequired / totalRequired) * requiredWeight : 0
    const optionalProgress = totalOptional > 0 ? (completedOptional / totalOptional) * optionalWeight : 0

    const progress = Math.round((requiredProgress + optionalProgress) * 100)

    // Debug logging
    console.log("🔍 [FORM-VALIDATION] StandardizedCustomerForm state:", {
      isValid: form.isValid,
      progress,
      completedRequired,
      totalRequired,
      formData: form.data
    })

    return progress
  }

  return (
    <FormLayout
      title="Customer Information"
      description="Complete the required sections to create a customer record"
      onSubmit={form.handleSubmit}
      onCancel={form.handleCancel}
      onReset={form.handleReset}
      submitLabel="Save Customer"
      isSubmitting={form.isSubmitting || isLoading}

      showProgress={true}
      progress={getFormProgress()}
      variant="modal"
      size="lg"
    >
      {/* Contact Information Section */}
      <FormSection
        section={formSections[0]}
        isExpanded={expandedSections.has("contact")}
        isCompleted={getSectionCompletion(formSections[0])}
        hasErrors={form.hasError("contact_person") || form.hasError("email")}
        onToggle={() => toggleSection("contact")}
      >
        <FormFieldGroup columns={2}>
          <FormField
            id="contact_person"
            label="Contact Person"
            type="text"
            icon={User}
            placeholder="John Doe"
            {...form.getFieldProps("contact_person")}
          />
          
          <FormField
            id="title_position"
            label="Title/Position"
            type="text"
            placeholder="Sales Manager"
            {...form.getFieldProps("title_position")}
          />
          
          <FormField
            id="email"
            label="Email Address"
            type="email"
            icon={Mail}
            placeholder="<EMAIL>"
            {...form.getFieldProps("email")}
          />
          
          <FormField
            id="phone"
            label="Phone Number"
            type="tel"
            icon={Phone}
            placeholder="+962 6 123 4567"
            {...form.getFieldProps("phone")}
          />
          
          <FormField
            id="mobile"
            label="Mobile Number"
            type="tel"
            icon={Phone}
            placeholder="+962 79 123 4567"
            {...form.getFieldProps("mobile")}
            className="md:col-span-2"
          />
        </FormFieldGroup>
      </FormSection>

      {/* Company Details Section */}
      <FormSection
        section={formSections[1]}
        isExpanded={expandedSections.has("company")}
        isCompleted={getSectionCompletion(formSections[1])}
        hasErrors={form.hasError("company") || form.hasError("city") || form.hasError("country")}
        onToggle={() => toggleSection("company")}
      >
        <FormFieldGroup columns={2}>
          <FormField
            id="company"
            label="Company Name"
            type="text"
            icon={Building2}
            placeholder="Acme Corporation"
            {...form.getFieldProps("company")}
          />
          
          <FormField
            id="website"
            label="Website"
            type="url"
            icon={Globe}
            placeholder="https://company.com"
            {...form.getFieldProps("website")}
          />
          
          <FormField
            id="city"
            label="City"
            type="text"
            icon={MapPin}
            placeholder="Amman"
            {...form.getFieldProps("city")}
          />
          
          <FormField
            id="country"
            label="Country"
            type="select"
            options={[
              { value: "Jordan", label: "Jordan" },
              { value: "UAE", label: "United Arab Emirates" },
              { value: "Saudi Arabia", label: "Saudi Arabia" },
              { value: "Kuwait", label: "Kuwait" },
              { value: "Qatar", label: "Qatar" },
              { value: "Bahrain", label: "Bahrain" },
              { value: "Oman", label: "Oman" },
              { value: "Lebanon", label: "Lebanon" },
              { value: "Syria", label: "Syria" },
              { value: "Iraq", label: "Iraq" },
              { value: "Egypt", label: "Egypt" },
              { value: "Other", label: "Other" }
            ]}
            {...form.getFieldProps("country")}
          />
          
          <FormField
            id="industry"
            label="Industry"
            type="text"
            placeholder="Technology, Manufacturing, Healthcare, etc."
            {...form.getFieldProps("industry")}
            className="md:col-span-2"
          />
        </FormFieldGroup>
      </FormSection>

      {/* Business Information Section */}
      <FormSection
        section={formSections[2]}
        isExpanded={expandedSections.has("business")}
        isCompleted={getSectionCompletion(formSections[2])}
        onToggle={() => toggleSection("business")}
      >
        <FormFieldGroup columns={2}>
          <FormField
            id="business_type"
            label="Business Type"
            type="select"
            options={[
              { value: "Manufacturer", label: "Manufacturer" },
              { value: "Distributor", label: "Distributor" },
              { value: "Retailer", label: "Retailer" },
              { value: "Wholesaler", label: "Wholesaler" },
              { value: "Service Provider", label: "Service Provider" },
              { value: "Consultant", label: "Consultant" },
              { value: "Government", label: "Government" },
              { value: "NGO", label: "NGO" },
              { value: "Other", label: "Other" }
            ]}
            {...form.getFieldProps("business_type")}
          />
          
          <FormField
            id="company_size"
            label="Company Size"
            type="select"
            options={[
              { value: "1-10", label: "1-10 employees" },
              { value: "11-50", label: "11-50 employees" },
              { value: "51-200", label: "51-200 employees" },
              { value: "201-500", label: "201-500 employees" },
              { value: "501-1000", label: "501-1000 employees" },
              { value: "1000+", label: "1000+ employees" }
            ]}
            {...form.getFieldProps("company_size")}
          />
          
          <FormField
            id="annual_volume"
            label="Annual Volume"
            type="currency"
            prefix="$"
            placeholder="1000000"
            {...form.getFieldProps("annual_volume")}
          />
          
          <FormField
            id="customer_tier"
            label="Customer Tier"
            type="select"
            options={[
              { value: "Bronze", label: "Bronze" },
              { value: "Silver", label: "Silver" },
              { value: "Gold", label: "Gold" },
              { value: "Platinum", label: "Platinum" },
              { value: "VIP", label: "VIP" }
            ]}
            {...form.getFieldProps("customer_tier")}
          />
        </FormFieldGroup>
      </FormSection>

      {/* Financial Details Section */}
      <FormSection
        section={formSections[3]}
        isExpanded={expandedSections.has("financial")}
        isCompleted={getSectionCompletion(formSections[3])}
        onToggle={() => toggleSection("financial")}
      >
        <FormFieldGroup columns={2}>
          <FormField
            id="tax_id"
            label="Tax ID"
            type="text"
            placeholder="*********"
            {...form.getFieldProps("tax_id")}
          />
          
          <FormField
            id="credit_limit"
            label="Credit Limit"
            type="currency"
            prefix="$"
            placeholder="50000"
            {...form.getFieldProps("credit_limit")}
          />
          
          <FormField
            id="payment_terms"
            label="Payment Terms"
            type="select"
            options={[
              { value: "Net 15", label: "Net 15" },
              { value: "Net 30", label: "Net 30" },
              { value: "Net 45", label: "Net 45" },
              { value: "Net 60", label: "Net 60" },
              { value: "Net 90", label: "Net 90" },
              { value: "COD", label: "Cash on Delivery" },
              { value: "Prepaid", label: "Prepaid" },
              { value: "Letter of Credit", label: "Letter of Credit" }
            ]}
            {...form.getFieldProps("payment_terms")}
          />
          
          <FormField
            id="currency_preference"
            label="Currency Preference"
            type="select"
            options={[
              { value: "USD", label: "USD - US Dollar" },
              { value: "EUR", label: "EUR - Euro" },
              { value: "JOD", label: "JOD - Jordanian Dinar" },
              { value: "AED", label: "AED - UAE Dirham" },
              { value: "SAR", label: "SAR - Saudi Riyal" },
              { value: "KWD", label: "KWD - Kuwaiti Dinar" },
              { value: "QAR", label: "QAR - Qatari Riyal" }
            ]}
            {...form.getFieldProps("currency_preference")}
          />
        </FormFieldGroup>
      </FormSection>

      {/* Shipping Preferences Section */}
      <FormSection
        section={formSections[4]}
        isExpanded={expandedSections.has("shipping")}
        isCompleted={getSectionCompletion(formSections[4])}
        onToggle={() => toggleSection("shipping")}
      >
        <FormFieldGroup columns={2}>
          <FormField
            id="preferred_shipping_method"
            label="Preferred Shipping Method"
            type="select"
            options={[
              { value: "Air Freight", label: "Air Freight" },
              { value: "Sea Freight", label: "Sea Freight" },
              { value: "Land Transport", label: "Land Transport" },
              { value: "Express Courier", label: "Express Courier" },
              { value: "Standard Delivery", label: "Standard Delivery" },
              { value: "Customer Pickup", label: "Customer Pickup" }
            ]}
            {...form.getFieldProps("preferred_shipping_method")}
          />
          
          <FormField
            id="preferred_incoterms"
            label="Preferred Incoterms"
            type="select"
            options={[
              { value: "EXW", label: "EXW - Ex Works" },
              { value: "FCA", label: "FCA - Free Carrier" },
              { value: "CPT", label: "CPT - Carriage Paid To" },
              { value: "CIP", label: "CIP - Carriage and Insurance Paid To" },
              { value: "DAP", label: "DAP - Delivered at Place" },
              { value: "DPU", label: "DPU - Delivered at Place Unloaded" },
              { value: "DDP", label: "DDP - Delivered Duty Paid" },
              { value: "FAS", label: "FAS - Free Alongside Ship" },
              { value: "FOB", label: "FOB - Free on Board" },
              { value: "CFR", label: "CFR - Cost and Freight" },
              { value: "CIF", label: "CIF - Cost, Insurance and Freight" }
            ]}
            {...form.getFieldProps("preferred_incoterms")}
          />
          
          <FormField
            id="shipping_instructions"
            label="Special Shipping Instructions"
            type="textarea"
            placeholder="Any special handling requirements, delivery instructions, or notes..."
            {...form.getFieldProps("shipping_instructions")}
            className="md:col-span-2"
          />
        </FormFieldGroup>
      </FormSection>

      {/* Notes Section */}
      <FormFieldGroup title="Additional Notes">
        <FormField
          id="notes"
          label="Notes"
          type="textarea"
          placeholder="Additional information about this customer..."
          {...form.getFieldProps("notes")}
        />
      </FormFieldGroup>
    </FormLayout>
  )
}
