"use client"

import * as React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  CheckCircle, AlertCircle, ChevronRight, ChevronDown, 
  Save, X, RotateCcw, Eye, EyeOff
} from "lucide-react"
import { cn } from "@/lib/utils"

// Form Layout Types
export interface FormSection {
  id: string
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  required?: boolean
  fields: string[]
  validation?: (data: any) => boolean
}

export interface FormLayoutProps {
  title: string
  description?: string
  sections?: FormSection[]
  children: React.ReactNode
  onSubmit?: (e: React.FormEvent) => void
  onCancel?: () => void
  onReset?: () => void
  submitLabel?: string
  cancelLabel?: string
  reset<PERSON>abel?: string
  isSubmitting?: boolean
  showProgress?: boolean
  progress?: number
  className?: string
  variant?: "default" | "modal" | "page" | "inline"
  size?: "sm" | "md" | "lg" | "xl"
}

export interface FormSectionProps {
  section: FormSection
  isExpanded?: boolean
  isCompleted?: boolean
  hasErrors?: boolean
  onToggle?: () => void
  children: React.ReactNode
  className?: string
}

export interface FormFieldGroupProps {
  title?: string
  description?: string
  children: React.ReactNode
  columns?: 1 | 2 | 3 | 4
  className?: string
}

export interface FormActionsProps {
  onSubmit?: () => void
  onCancel?: () => void
  onReset?: () => void
  submitLabel?: string
  cancelLabel?: string
  resetLabel?: string
  isSubmitting?: boolean
  submitDisabled?: boolean
  showReset?: boolean
  alignment?: "left" | "center" | "right" | "between"
  className?: string
}

// Main Form Layout Component
export function FormLayout({
  title,
  description,
  sections,
  children,
  onSubmit,
  onCancel,
  onReset,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  resetLabel = "Reset",
  isSubmitting = false,
  showProgress = false,
  progress = 0,
  className,
  variant = "default",
  size = "md"
}: FormLayoutProps) {
  const sizeClasses = {
    sm: "max-w-md",
    md: "max-w-2xl",
    lg: "max-w-4xl",
    xl: "max-w-6xl"
  }

  const variantClasses = {
    default: "space-y-6",
    modal: "space-y-4",
    page: "space-y-8",
    inline: "space-y-4"
  }

  return (
    <div className={cn(
      "w-full mx-auto",
      sizeClasses[size],
      variantClasses[variant],
      className
    )}>
      {/* Form Header */}
      {(title || description || showProgress) && (
        <div className="space-y-4">
          {title && (
            <div>
              <h2 className="heading-2 text-foreground">{title}</h2>
              {description && (
                <p className="body-small text-muted-foreground mt-1">{description}</p>
              )}
            </div>
          )}
          
          {showProgress && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Progress</span>
                <span className="font-medium">{Math.round(progress)}% Complete</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          )}
        </div>
      )}

      {/* Form Content */}
      <form onSubmit={onSubmit} className="space-y-6">
        {children}
        
        {/* Form Actions */}
        {(onSubmit || onCancel || onReset) && (
          <FormActions
            onSubmit={onSubmit ? () => {} : undefined}
            onCancel={onCancel}
            onReset={onReset}
            submitLabel={submitLabel}
            cancelLabel={cancelLabel}
            resetLabel={resetLabel}
            isSubmitting={isSubmitting}
            showReset={!!onReset}
            alignment="right"
          />
        )}
      </form>
    </div>
  )
}

// Form Section Component with Progressive Disclosure
export function FormSection({
  section,
  isExpanded = true,
  isCompleted = false,
  hasErrors = false,
  onToggle,
  children,
  className
}: FormSectionProps) {
  const Icon = section.icon

  return (
    <Card 
      className={cn(
        "transition-all duration-normal",
        hasErrors && "border-destructive bg-destructive/5",
        isCompleted && "border-success/20 bg-success/5",
        className
      )}
    >
      <CardHeader 
        className={cn(
          "pb-3",
          onToggle && "cursor-pointer hover:bg-accent/50 transition-colors"
        )}
        onClick={onToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className={cn(
                "p-2 rounded-lg",
                isCompleted ? "bg-success/10" : hasErrors ? "bg-destructive/10" : "bg-muted"
              )}>
                <Icon className={cn(
                  "h-4 w-4",
                  isCompleted ? "text-success" : hasErrors ? "text-destructive" : "text-muted-foreground"
                )} />
              </div>
            )}
            <div>
              <CardTitle className="flex items-center gap-2">
                {section.title}
                {section.required && (
                  <Badge variant="outline" className="text-xs">Required</Badge>
                )}
              </CardTitle>
              {section.description && (
                <p className="text-sm text-muted-foreground mt-1">{section.description}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {isCompleted && (
              <CheckCircle className="h-4 w-4 text-success" />
            )}
            {hasErrors && (
              <AlertCircle className="h-4 w-4 text-destructive" />
            )}
            {onToggle && (
              isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )
            )}
          </div>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="space-y-4 animate-fade-in">
          {children}
        </CardContent>
      )}
    </Card>
  )
}

// Form Field Group Component
export function FormFieldGroup({
  title,
  description,
  children,
  columns = 1,
  className
}: FormFieldGroupProps) {
  const gridClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
  }

  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div>
          {title && (
            <h3 className="heading-4 text-foreground">{title}</h3>
          )}
          {description && (
            <p className="body-small text-muted-foreground mt-1">{description}</p>
          )}
        </div>
      )}
      
      <div className={cn("grid gap-4", gridClasses[columns])}>
        {children}
      </div>
    </div>
  )
}

// Form Actions Component
export function FormActions({
  onSubmit,
  onCancel,
  onReset,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  resetLabel = "Reset",
  isSubmitting = false,
  submitDisabled = false,
  showReset = false,
  alignment = "right",
  className
}: FormActionsProps) {
  const alignmentClasses = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end",
    between: "justify-between"
  }

  return (
    <div className={cn("pt-6 border-t")}>
      <div className={cn(
        "flex items-center gap-3",
        alignmentClasses[alignment],
        className
      )}>
        {alignment === "between" && showReset && onReset && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onReset}
            disabled={isSubmitting}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            {resetLabel}
          </Button>
        )}
        
        <div className="flex items-center gap-3">
          {alignment !== "between" && showReset && onReset && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onReset}
              disabled={isSubmitting}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              {resetLabel}
            </Button>
          )}
          
          {onCancel && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={isSubmitting}
            >
              <X className="h-4 w-4 mr-2" />
              {cancelLabel}
            </Button>
          )}
          
          {onSubmit && (
            <Button 
              type="submit" 
              disabled={isSubmitting || submitDisabled}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {submitLabel}
                </>
              )}
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

// Form Container Component for different contexts
export function FormContainer({
  children,
  variant = "card",
  className
}: {
  children: React.ReactNode
  variant?: "card" | "plain" | "bordered"
  className?: string
}) {
  if (variant === "card") {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          {children}
        </CardContent>
      </Card>
    )
  }

  if (variant === "bordered") {
    return (
      <div className={cn("border rounded-lg p-6", className)}>
        {children}
      </div>
    )
  }

  return (
    <div className={className}>
      {children}
    </div>
  )
}
