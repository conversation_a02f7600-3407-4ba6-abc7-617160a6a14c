import { cn } from "@/lib/utils"
import { Loader2, Refresh<PERSON>w, RotateCw } from "lucide-react"

interface LoadingSpinnerProps {
  size?: "xs" | "sm" | "md" | "lg" | "xl"
  variant?: "default" | "dots" | "pulse" | "bounce" | "icon"
  color?: "primary" | "secondary" | "muted" | "white"
  text?: string
  className?: string
  fullScreen?: boolean
}

export function LoadingSpinner({
  size = "md",
  variant = "default",
  color = "primary",
  text,
  className,
  fullScreen = false
}: LoadingSpinnerProps) {
  const sizeClasses = {
    xs: "h-3 w-3",
    sm: "h-4 w-4", 
    md: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  }

  const colorClasses = {
    primary: "border-primary",
    secondary: "border-secondary",
    muted: "border-muted-foreground",
    white: "border-white"
  }

  const textSizeClasses = {
    xs: "text-xs",
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg", 
    xl: "text-xl"
  }

  const renderSpinner = () => {
    switch (variant) {
      case "dots":
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "rounded-full bg-current animate-pulse",
                  size === "xs" ? "h-1 w-1" :
                  size === "sm" ? "h-1.5 w-1.5" :
                  size === "md" ? "h-2 w-2" :
                  size === "lg" ? "h-2.5 w-2.5" : "h-3 w-3"
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: "1.4s"
                }}
              />
            ))}
          </div>
        )

      case "pulse":
        return (
          <div className={cn(
            "rounded-full bg-current animate-pulse",
            sizeClasses[size]
          )} />
        )

      case "bounce":
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "rounded-full bg-current animate-bounce",
                  size === "xs" ? "h-1 w-1" :
                  size === "sm" ? "h-1.5 w-1.5" :
                  size === "md" ? "h-2 w-2" :
                  size === "lg" ? "h-2.5 w-2.5" : "h-3 w-3"
                )}
                style={{
                  animationDelay: `${i * 0.1}s`
                }}
              />
            ))}
          </div>
        )

      case "icon":
        return (
          <Loader2 className={cn(
            "animate-spin",
            sizeClasses[size]
          )} />
        )

      default:
        return (
          <div className={cn(
            "animate-spin rounded-full border-2 border-transparent border-t-current",
            sizeClasses[size]
          )} />
        )
    }
  }

  const content = (
    <div className={cn(
      "flex flex-col items-center justify-center space-y-2",
      color === "primary" ? "text-primary" :
      color === "secondary" ? "text-secondary" :
      color === "muted" ? "text-muted-foreground" :
      "text-white",
      className
    )}>
      {renderSpinner()}
      {text && (
        <p className={cn(
          "font-medium",
          textSizeClasses[size]
        )}>
          {text}
        </p>
      )}
    </div>
  )

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        {content}
      </div>
    )
  }

  return content
}

// Specialized loading components
export function ButtonSpinner({ size = "sm", className }: { size?: "xs" | "sm" | "md", className?: string }) {
  return (
    <LoadingSpinner
      size={size}
      variant="icon"
      className={cn("mr-2", className)}
    />
  )
}

export function PageSpinner({ text = "Loading..." }: { text?: string }) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <LoadingSpinner
        size="lg"
        variant="default"
        text={text}
      />
    </div>
  )
}

export function InlineSpinner({ size = "sm", className }: { size?: "xs" | "sm" | "md", className?: string }) {
  return (
    <LoadingSpinner
      size={size}
      variant="icon"
      className={cn("inline-block", className)}
    />
  )
}

export function OverlaySpinner({ text }: { text?: string }) {
  return (
    <LoadingSpinner
      size="lg"
      variant="default"
      text={text}
      fullScreen
    />
  )
}

// Loading states for specific components
export function TableLoadingRow({ columns = 4 }: { columns?: number }) {
  return (
    <tr>
      <td colSpan={columns} className="p-8">
        <div className="flex items-center justify-center">
          <LoadingSpinner size="md" text="Loading data..." />
        </div>
      </td>
    </tr>
  )
}

export function CardLoadingState({ className }: { className?: string }) {
  return (
    <div className={cn(
      "flex items-center justify-center p-8 border rounded-lg bg-muted/20",
      className
    )}>
      <LoadingSpinner size="md" variant="dots" />
    </div>
  )
}
