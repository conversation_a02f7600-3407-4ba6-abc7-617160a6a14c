"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users, TrendingUp, DollarSign, Star, BarChart,
  ArrowUpRight, ArrowDownRight, AlertTriangle,
  CheckCircle, Lightbulb, Target, Award
} from "lucide-react"
import { cn } from "@/lib/utils"

// Widget Types
interface AnalyticsWidgetData {
  metrics: {
    totalCustomers: number
    activeCustomers: number
    totalRevenue: number
    avgCustomerValue: number
    growthRate: number
    retentionRate: number
  }
  insights: {
    opportunities: number
    risks: number
    actionableInsights: number
    confidenceScore: number
  }
  topPerformers: Array<{
    company: string
    revenue: number
    tier: string
  }>
  recentActivity: Array<{
    type: string
    description: string
    impact: "positive" | "negative" | "neutral"
    date: string
  }>
}

interface CustomerAnalyticsWidgetProps {
  className?: string
  showInsights?: boolean
  showTopPerformers?: boolean
  showRecentActivity?: boolean
  onViewDetails?: () => void
}

export function CustomerAnalyticsWidget({
  className,
  showInsights = true,
  showTopPerformers = true,
  showRecentActivity = true,
  onViewDetails
}: CustomerAnalyticsWidgetProps) {
  const [data, setData] = useState<AnalyticsWidgetData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAnalyticsData()
  }, [])

  const fetchAnalyticsData = async () => {
    try {
      setError(null)
      const response = await fetch('/api/customers/analytics?type=overview')
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data')
      }
      
      const result = await response.json()
      
      // Transform API response to widget format
      const widgetData: AnalyticsWidgetData = {
        metrics: {
          totalCustomers: result.metrics?.totalCustomers || 0,
          activeCustomers: result.metrics?.activeCustomers || 0,
          totalRevenue: result.metrics?.totalRevenue || 0,
          avgCustomerValue: result.metrics?.avgAnnualValue || 0,
          growthRate: result.metrics?.monthlyGrowthRate || 0,
          retentionRate: result.metrics?.retentionRate || 0
        },
        insights: {
          opportunities: 0, // Would come from insights API
          risks: 0,
          actionableInsights: 0,
          confidenceScore: 0
        },
        topPerformers: [], // Would be populated from analytics
        recentActivity: [
          {
            type: "new_customer",
            description: "3 new customers added this week",
            impact: "positive",
            date: new Date().toISOString()
          }
        ]
      }
      
      setData(widgetData)
    } catch (error) {
      console.error('Failed to fetch analytics:', error)
      setError((error as Error).message)
    } finally {
      setLoading(false)
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpRight className="h-3 w-3 text-green-600" />
    if (change < 0) return <ArrowDownRight className="h-3 w-3 text-red-600" />
    return null
  }

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case "positive": return <CheckCircle className="h-3 w-3 text-green-600" />
      case "negative": return <AlertTriangle className="h-3 w-3 text-red-600" />
      default: return <Lightbulb className="h-3 w-3 text-blue-600" />
    }
  }

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>
              ))}
            </div>
            <Skeleton className="h-32 w-full" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || "Failed to load analytics data"}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Main Metrics Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <BarChart className="h-5 w-5" />
            Customer Analytics
          </CardTitle>
          {onViewDetails && (
            <Button variant="outline" size="sm" onClick={onViewDetails}>
              View Details
            </Button>
          )}
        </CardHeader>
        <CardContent>
          {/* Key Metrics Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-muted-foreground">Total</span>
              </div>
              <div className="text-2xl font-bold">{data.metrics.totalCustomers}</div>
              <div className="flex items-center gap-1 text-xs">
                {getChangeIcon(data.metrics.growthRate)}
                <span className={cn(
                  data.metrics.growthRate > 0 ? "text-green-600" : "text-red-600"
                )}>
                  {Math.abs(data.metrics.growthRate).toFixed(1)}%
                </span>
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <TrendingUp className="h-4 w-4 text-green-600" />
                <span className="text-sm text-muted-foreground">Active</span>
              </div>
              <div className="text-2xl font-bold">{data.metrics.activeCustomers}</div>
              <div className="text-xs text-muted-foreground">
                {data.metrics.totalCustomers > 0 
                  ? Math.round((data.metrics.activeCustomers / data.metrics.totalCustomers) * 100)
                  : 0
                }% of total
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <DollarSign className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-muted-foreground">Revenue</span>
              </div>
              <div className="text-xl font-bold">
                ${Math.round(data.metrics.totalRevenue / 1000)}K
              </div>
              <div className="text-xs text-muted-foreground">Total annual</div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 text-purple-600" />
                <span className="text-sm text-muted-foreground">Avg Value</span>
              </div>
              <div className="text-xl font-bold">
                ${Math.round(data.metrics.avgCustomerValue / 1000)}K
              </div>
              <div className="text-xs text-muted-foreground">Per customer</div>
            </div>
          </div>

          {/* Retention Rate */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Customer Retention Rate</span>
              <span className="font-medium">{data.metrics.retentionRate.toFixed(1)}%</span>
            </div>
            <Progress value={data.metrics.retentionRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Insights Card */}
      {showInsights && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Lightbulb className="h-4 w-4" />
              Key Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">{data.insights.opportunities}</div>
                <div className="text-xs text-muted-foreground">Opportunities</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-red-600">{data.insights.risks}</div>
                <div className="text-xs text-muted-foreground">Risks</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600">{data.insights.actionableInsights}</div>
                <div className="text-xs text-muted-foreground">Actionable</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">{data.insights.confidenceScore}%</div>
                <div className="text-xs text-muted-foreground">Confidence</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Performers */}
      {showTopPerformers && data.topPerformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Award className="h-4 w-4" />
              Top Performers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.topPerformers.slice(0, 3).map((performer, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">{performer.company}</p>
                    <Badge variant="secondary" className="text-xs">
                      {performer.tier}
                    </Badge>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-sm">
                      ${(performer.revenue / 1000).toFixed(0)}K
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      {showRecentActivity && data.recentActivity.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Target className="h-4 w-4" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recentActivity.slice(0, 3).map((activity, index) => (
                <div key={index} className="flex items-start gap-2">
                  {getImpactIcon(activity.impact)}
                  <div className="flex-1">
                    <p className="text-sm">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Compact version for dashboard
export function CustomerAnalyticsCompact({
  className,
  onViewDetails
}: {
  className?: string
  onViewDetails?: () => void
}) {
  return (
    <CustomerAnalyticsWidget
      className={className}
      showInsights={false}
      showTopPerformers={false}
      showRecentActivity={false}
      onViewDetails={onViewDetails}
    />
  )
}

// Full version with all sections
export function CustomerAnalyticsFull({
  className,
  onViewDetails
}: {
  className?: string
  onViewDetails?: () => void
}) {
  return (
    <CustomerAnalyticsWidget
      className={className}
      showInsights={true}
      showTopPerformers={true}
      showRecentActivity={true}
      onViewDetails={onViewDetails}
    />
  )
}
