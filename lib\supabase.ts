import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { ContactSource, CustomerStatus } from '@/app/types/customer'

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// Validate environment variables
if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    "Missing required Supabase environment variables:\n" +
    "- NEXT_PUBLIC_SUPABASE_URL\n" +
    "- NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n" +
    "Please set these in your .env.local file."
  )
}

// ✅ ENHANCED SINGLETON SUPABASE CLIENT - Prevents multiple GoTrueClient instances
// This is the main Supabase client instance that should be used throughout the app
// Using a singleton pattern to prevent authentication conflicts and improve performance
let supabaseInstance: ReturnType<typeof createSupabaseClient> | null = null

// ✅ CRITICAL FIX: Global instance tracking to prevent multiple GoTrueClient instances
if (typeof window !== 'undefined') {
  // Client-side: Use global window object to ensure true singleton across all modules
  if (!(window as any).__NAWRAS_SUPABASE_CLIENT__) {
    console.log('🔧 [SINGLETON] Creating new Supabase client instance')
  } else {
    console.log('🔄 [SINGLETON] Reusing existing Supabase client instance')
  }
}

const getSupabaseClient = () => {
  // ✅ CRITICAL: Check for existing global instance first (client-side only)
  if (typeof window !== 'undefined' && (window as any).__NAWRAS_SUPABASE_CLIENT__) {
    console.log('🔄 [SINGLETON] Using global Supabase client instance')
    return (window as any).__NAWRAS_SUPABASE_CLIENT__
  }

  if (!supabaseInstance) {
    console.log('🔧 [SINGLETON] Creating new Supabase client instance')
    supabaseInstance = createSupabaseClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        // Use localStorage only in browser environment
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        // Prevent multiple auth instances with unique storage key
        storageKey: 'nawras-crm-auth-token',
        // ✅ CRITICAL FIX: Optimize for 10-second server timeout
        debug: false, // Disable debug logging for performance
      },
      global: {
        headers: {
          'x-application-name': 'nawras-crm',
          'x-client-info': 'nawras-crm-singleton@3.0.0',
          // ✅ PERFORMANCE OPTIMIZED: Enable caching for better performance
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=30, max=1000',
          'Cache-Control': 'public, max-age=300', // ✅ 5-minute cache for data queries
          'Pragma': 'cache'
        },
        // ✅ OPTIMIZED: Extended fetch timeout for better reliability
        fetch: (url, options = {}) => {
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 seconds for database queries

          return fetch(url, {
            ...options,
            signal: controller.signal,
          }).finally(() => clearTimeout(timeoutId))
        }
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    })

    // ✅ CRITICAL: Store in global window object to prevent multiple instances
    if (typeof window !== 'undefined') {
      (window as any).__NAWRAS_SUPABASE_CLIENT__ = supabaseInstance
      console.log('✅ [SINGLETON] Supabase client stored in global window object')
    }
  }
  return supabaseInstance
}

// ✅ ENHANCED: Export the singleton instance with monitoring
export const supabase = getSupabaseClient()

// ✅ PERFORMANCE: Create pooled client alias for compatibility
// For now, use the same client to ensure deployment stability
export const supabasePooled = getSupabaseClient()

// ✅ ENHANCED: Export createClient function that returns the singleton instance
// This prevents multiple GoTrueClient instances and improves performance
export const createClient = () => {
  const client = getSupabaseClient()

  // ✅ DEBUG: Log client usage for monitoring
  if (typeof window !== 'undefined') {
    const instanceCount = (window as any).__NAWRAS_CLIENT_COUNT__ || 0
    ;(window as any).__NAWRAS_CLIENT_COUNT__ = instanceCount + 1
    console.log(`🔍 [SINGLETON] Client requested (usage count: ${instanceCount + 1})`)
  }

  return client
}

// ✅ DIAGNOSTIC: Function to check for multiple GoTrueClient instances
export const diagnosticClientInfo = () => {
  if (typeof window === 'undefined') {
    return { environment: 'server', instances: 'unknown' }
  }

  const globalClient = (window as any).__NAWRAS_SUPABASE_CLIENT__
  const usageCount = (window as any).__NAWRAS_CLIENT_COUNT__ || 0

  return {
    environment: 'client',
    hasGlobalInstance: !!globalClient,
    usageCount,
    clientId: globalClient?.supabaseKey?.slice(-8) || 'none'
  }
}

// Export type definitions
export type Database = {
  public: {
    Tables: {
      customers: {
        Row: {
          id: string
          user_id: string
          name: string
          company: string
          contact_person: string
          email: string
          phone?: string | null
          country: string
          city: string
          contact_source: ContactSource
          status: CustomerStatus
          notes?: string | null
          created_at: string
          updated_at: string
        }
        Insert: Omit<Database['public']['Tables']['customers']['Row'], 'id' | 'created_at' | 'updated_at'>
        Update: Partial<Database['public']['Tables']['customers']['Row']>
      }
    }
  }
}