"use client"

import React from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Shield, AlertTriangle, ArrowLeft, RefreshCw } from "lucide-react"

interface AdminGuardProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  redirectTo?: string
  showFallback?: boolean
}

/**
 * AdminGuard Component
 * Protects admin-only routes and components
 * Provides role-based access control for administrative features
 */
export function AdminGuard({
  children,
  fallback,
  redirectTo = "/dashboard",
  showFallback = true
}: AdminGuardProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [authTimeout, setAuthTimeout] = useState(false)

  const loading = status === "loading"
  const user = session?.user
  const isAdmin = () => (user as any)?.role === "admin"

  useEffect(() => {
    // Only redirect if not loading and user is not admin
    if (!loading && user && !isAdmin()) {
      console.warn("Access denied: Admin privileges required")
      if (redirectTo) {
        router.push(redirectTo)
      }
    }
  }, [user, loading, router, redirectTo])

  // CRITICAL FIX: Add timeout to prevent infinite loading loops
  useEffect(() => {
    const timeout = setTimeout(() => {
      if (loading) {
        console.warn("AdminGuard: Authentication timeout - preventing infinite loop")
        setAuthTimeout(true)
      }
    }, 10000) // 10 second timeout

    return () => clearTimeout(timeout)
  }, [loading])

  // Show loading state (with timeout protection)
  if (loading && !authTimeout) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center space-y-4">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground animate-pulse" />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Verifying Access</h3>
            <p className="text-muted-foreground">Checking administrative privileges...</p>
          </div>
        </div>
      </div>
    )
  }

  // If authentication timed out, show error and allow bypass
  if (authTimeout && loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-destructive mb-4" />
            <CardTitle>Authentication Timeout</CardTitle>
            <CardDescription>
              Authentication service is taking too long to respond.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-sm text-muted-foreground">
              This may be due to network issues or service maintenance.
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => window.location.reload()}
                className="flex-1"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button
                onClick={() => router.push("/dashboard")}
                className="flex-1"
              >
                Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <AlertTriangle className="h-12 w-12 mx-auto text-destructive mb-4" />
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              You must be logged in to access this area.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button onClick={() => router.push("/login")} className="w-full">
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User authenticated but not admin
  if (!isAdmin()) {
    if (fallback) {
      return <>{fallback}</>
    }

    if (!showFallback) {
      return null
    }

    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Shield className="h-12 w-12 mx-auto text-destructive mb-4" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have administrator privileges to access this area.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center text-sm text-muted-foreground">
              <p>Current role: <span className="font-medium">{(user as any)?.role || 'user'}</span></p>
              <p>Required role: <span className="font-medium">admin</span></p>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => router.back()} 
                className="flex-1"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Go Back
              </Button>
              <Button 
                onClick={() => router.push(redirectTo)} 
                className="flex-1"
              >
                Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // User is admin, render children
  return <>{children}</>
}

/**
 * Higher-order component for admin route protection
 */
export function withAdminGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: Omit<AdminGuardProps, 'children'>
) {
  return function AdminProtectedComponent(props: P) {
    return (
      <AdminGuard {...options}>
        <Component {...props} />
      </AdminGuard>
    )
  }
}

/**
 * Hook for checking admin access in components
 */
export function useAdminAccess() {
  const { data: session, status } = useSession()
  const loading = status === "loading"
  const user = session?.user
  const isAdminUser = (user as any)?.role === "admin"

  return {
    user,
    loading,
    isAdmin: isAdminUser,
    hasAccess: !loading && user && isAdminUser,
    isChecking: loading,
    isAuthenticated: !!user,
    role: (user as any)?.role || 'user'
  }
}
