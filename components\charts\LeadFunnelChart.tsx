'use client'

import React from 'react'
import { PieChart, Pie, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { useLeadSourceData } from '@/hooks/useChartData'
import { formatters } from '@/components/charts/chart-config'
import { ChartProps } from '@/components/charts/types'
import { Users, Target, TrendingUp } from 'lucide-react'

interface LeadFunnelChartProps extends Omit<ChartProps, 'data'> {
  chartType?: 'pie' | 'bar' | 'funnel'
  showConversionRate?: boolean
}

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{data.source}</p>
        <p className="text-sm text-muted-foreground">
          Total Leads: <span className="font-medium text-foreground">{data.count}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Converted: <span className="font-medium text-foreground">{data.converted}</span>
        </p>
        <p className="text-sm text-muted-foreground">
          Conversion Rate: <span className="font-medium text-foreground">{formatters.percentage(data.conversionRate)}</span>
        </p>
      </div>
    )
  }
  return null
}

const RADIAN = Math.PI / 180
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5
  const x = cx + radius * Math.cos(-midAngle * RADIAN)
  const y = cy + radius * Math.sin(-midAngle * RADIAN)

  return percent > 0.05 ? (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  ) : null
}

export function LeadFunnelChart({ 
  config,
  loading: externalLoading,
  error,
  className = "",
  title = "Lead Sources & Conversion",
  subtitle = "Lead generation and conversion analysis",
  chartType = 'bar',
  showConversionRate = true
}: LeadFunnelChartProps) {
  const { data, loading: dataLoading } = useLeadSourceData()
  const isLoading = externalLoading || dataLoading

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>Error loading lead data: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>No lead data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate summary metrics
  const totalLeads = data.reduce((sum, item) => sum + item.count, 0)
  const totalConverted = data.reduce((sum, item) => sum + item.converted, 0)
  const overallConversionRate = totalLeads > 0 ? (totalConverted / totalLeads) * 100 : 0
  const bestSource = data.reduce((best, current) => 
    current.conversionRate > best.conversionRate ? current : best
  )

  const renderChart = () => {
    switch (chartType) {
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={config?.height || 300}>
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={100}
                fill="#8884d8"
                dataKey="count"
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        )
      
      case 'funnel':
        // Simple funnel representation using bars
        const sortedData = [...data].sort((a, b) => b.count - a.count)
        return (
          <div className="space-y-3">
            {sortedData.map((item, index) => {
              const maxWidth = sortedData[0].count
              const widthPercentage = (item.count / maxWidth) * 100
              return (
                <div key={item.source} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{item.source}</span>
                    <span className="text-muted-foreground">{item.count} leads</span>
                  </div>
                  <div className="relative">
                    <div 
                      className="h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                      style={{ 
                        width: `${widthPercentage}%`,
                        backgroundColor: item.color,
                        minWidth: '60px'
                      }}
                    >
                      {formatters.percentage(item.conversionRate)}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )
      
      default: // bar chart
        return (
          <ResponsiveContainer width="100%" height={config?.height || 300}>
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis 
                dataKey="source" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="count" name="Total Leads" radius={[4, 4, 0, 0]}>
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
              {showConversionRate && (
                <Bar dataKey="converted" name="Converted" radius={[4, 4, 0, 0]} opacity={0.7}>
                  {data.map((entry, index) => (
                    <Cell key={`cell-converted-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              )}
            </BarChart>
          </ResponsiveContainer>
        )
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            {title}
          </div>
          <Badge variant="secondary" className="flex items-center gap-1">
            <Target className="h-3 w-3" />
            {formatters.percentage(overallConversionRate)}
          </Badge>
        </CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        {renderChart()}
        
        {/* Summary Stats */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">Total Leads</p>
            <p className="font-semibold text-lg">{formatters.number(totalLeads)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Converted</p>
            <p className="font-semibold text-lg text-green-600">{formatters.number(totalConverted)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Conversion Rate</p>
            <p className="font-semibold text-lg">{formatters.percentage(overallConversionRate)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Best Source</p>
            <p className="font-semibold text-lg">{bestSource.source}</p>
            <p className="text-xs text-muted-foreground">
              {formatters.percentage(bestSource.conversionRate)}
            </p>
          </div>
        </div>
        
        {/* Legend for pie chart */}
        {chartType === 'pie' && (
          <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-2">
            {data.map((item, index) => (
              <div key={item.source} className="flex items-center gap-2 text-sm">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: item.color }}
                />
                <span className="truncate">{item.source}</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
