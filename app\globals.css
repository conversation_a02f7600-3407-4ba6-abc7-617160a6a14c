@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core Design Tokens */
    --radius: 0.5rem;

    /* Brand Colors - Professional CRM Theme */
    --primary: 220 91% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-50: 220 100% 97%;
    --primary-100: 220 100% 92%;
    --primary-500: 220 91% 50%;
    --primary-600: 220 91% 42%;
    --primary-900: 220 91% 25%;

    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    /* Secondary Colors */
    --secondary: 0 0% 97%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 97%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 97%;
    --accent-foreground: 0 0% 9%;

    /* Semantic Colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 98%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;

    /* Status Colors for CRM */
    --status-active: 142 76% 36%;
    --status-inactive: 0 0% 45%;
    --status-pending: 38 92% 50%;
    --status-closed: 0 84% 60%;
    --status-qualified: 220 91% 50%;

    /* Priority Colors */
    --priority-low: 142 76% 36%;
    --priority-medium: 38 92% 50%;
    --priority-high: 0 84% 60%;
    --priority-urgent: 0 91% 35%;

    /* UI Elements */
    --border: 0 0% 89%;
    --input: 0 0% 89%;
    --ring: 220 91% 50%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Sidebar Colors - Professional Dark Theme */
    --sidebar: 220 13% 18%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 220 91% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 13% 25%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 220 13% 15%;
    --sidebar-ring: 220 91% 50%;

    /* Legacy support */
    --foreground-rgb: 23, 23, 23;
    --background-rgb: 255, 255, 255;
  }

  .dark {
    /* Brand Colors - Dark Mode */
    --primary: 220 91% 50%;
    --primary-foreground: 0 0% 9%;
    --primary-50: 220 13% 9%;
    --primary-100: 220 13% 15%;
    --primary-500: 220 91% 50%;
    --primary-600: 220 91% 42%;
    --primary-900: 220 91% 25%;

    /* Base Colors - Dark Mode */
    --background: 0 0% 9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 11%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 11%;
    --popover-foreground: 0 0% 98%;

    /* Secondary Colors - Dark Mode */
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 64%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 98%;

    /* Semantic Colors - Dark Mode */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 9%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 98%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;

    /* Status Colors - Dark Mode */
    --status-active: 142 76% 36%;
    --status-inactive: 0 0% 64%;
    --status-pending: 38 92% 50%;
    --status-closed: 0 84% 60%;
    --status-qualified: 220 91% 50%;

    /* Priority Colors - Dark Mode */
    --priority-low: 142 76% 36%;
    --priority-medium: 38 92% 50%;
    --priority-high: 0 84% 60%;
    --priority-urgent: 0 91% 35%;

    /* UI Elements - Dark Mode */
    --border: 0 0% 15%;
    --input: 0 0% 15%;
    --ring: 220 91% 50%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Sidebar Colors - Dark Mode */
    --sidebar: 220 13% 12%;
    --sidebar-foreground: 0 0% 95%;
    --sidebar-primary: 220 91% 50%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 220 13% 18%;
    --sidebar-accent-foreground: 0 0% 95%;
    --sidebar-border: 220 13% 8%;
    --sidebar-ring: 220 91% 50%;

    /* Legacy support */
    --foreground-rgb: 250, 250, 250;
    --background-rgb: 23, 23, 23;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Typography Classes */
  .heading-1 {
    @apply text-4xl font-bold leading-tight tracking-tight;
  }

  .heading-2 {
    @apply text-3xl font-semibold leading-tight tracking-tight;
  }

  .heading-3 {
    @apply text-2xl font-semibold leading-snug;
  }

  .heading-4 {
    @apply text-xl font-medium leading-snug;
  }

  .body-large {
    @apply text-lg font-normal leading-relaxed;
  }

  .body-base {
    @apply text-base font-normal leading-normal;
  }

  .body-small {
    @apply text-sm font-normal leading-normal;
  }

  .ui-label {
    @apply text-sm font-medium leading-normal;
  }

  .ui-caption {
    @apply text-xs font-normal leading-normal text-muted-foreground;
  }

  .ui-overline {
    @apply text-xs font-semibold leading-normal uppercase tracking-wider;
  }

  /* Animation Classes */
  .transition-fast {
    @apply transition-all duration-fast ease-out;
  }

  .transition-normal {
    @apply transition-all duration-normal ease-out;
  }

  .transition-slow {
    @apply transition-all duration-slow ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    @apply transition-transform duration-normal ease-out;
  }

  .hover-lift:hover {
    @apply -translate-y-0.5;
  }

  .hover-scale {
    @apply transition-transform duration-normal ease-out;
  }

  .hover-scale:hover {
    @apply scale-105;
  }

  /* Status Indicators */
  .status-active {
    @apply bg-status-active text-white;
  }

  .status-inactive {
    @apply bg-status-inactive text-white;
  }

  .status-pending {
    @apply bg-status-pending text-white;
  }

  .status-closed {
    @apply bg-status-closed text-white;
  }

  .status-qualified {
    @apply bg-status-qualified text-white;
  }

  /* Priority Indicators */
  .priority-low {
    @apply bg-priority-low text-white;
  }

  .priority-medium {
    @apply bg-priority-medium text-white;
  }

  .priority-high {
    @apply bg-priority-high text-white;
  }

  .priority-urgent {
    @apply bg-priority-urgent text-white;
  }

  /* Card Variants */
  .card-elevated {
    @apply shadow-lg hover:shadow-xl transition-shadow duration-normal;
  }

  .card-interactive {
    @apply hover-lift cursor-pointer;
  }

  /* Loading States */
  .loading-pulse {
    @apply animate-pulse;
  }

  .loading-spin {
    @apply animate-spin;
  }

  /* Enhanced animations for better UX */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes wave {
    0%, 60%, 100% {
      transform: initial;
    }
    30% {
      transform: translateY(-15px);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  .animate-wave {
    animation: wave 1.6s ease-in-out infinite;
  }
}