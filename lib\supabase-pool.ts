// ✅ PERFORMANCE OPTIMIZATION: Connection Pooling for Supabase
import { createClient as createSupabaseClient, SupabaseClient } from '@supabase/supabase-js'

interface PooledConnection {
  client: SupabaseClient
  inUse: boolean
  lastUsed: number
  created: number
}

class SupabaseConnectionPool {
  private pool: PooledConnection[] = []
  private readonly maxConnections: number
  private readonly maxIdleTime: number // milliseconds
  private readonly supabaseUrl: string
  private readonly supabaseKey: string
  private cleanupInterval: NodeJS.Timeout | null = null

  constructor(
    supabaseUrl: string,
    supabaseKey: string,
    maxConnections: number = 10,
    maxIdleTime: number = 5 * 60 * 1000 // 5 minutes
  ) {
    this.supabaseUrl = supabaseUrl
    this.supabaseKey = supabaseKey
    this.maxConnections = maxConnections
    this.maxIdleTime = maxIdleTime

    // Start cleanup interval
    this.startCleanup()
  }

  private createConnection(): PooledConnection {
    const client = createSupabaseClient(this.supabaseUrl, this.supabaseKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        storageKey: 'nawras-crm-auth-token',
        debug: false,
      },
      global: {
        headers: {
          'x-application-name': 'nawras-crm-pooled',
          'x-client-info': 'nawras-crm-pool@1.0.0',
          'Connection': 'keep-alive',
          'Keep-Alive': 'timeout=30, max=1000',
          'Cache-Control': 'public, max-age=300',
        },
        fetch: (url, options = {}) => {
          const controller = new AbortController()
          const timeoutId = setTimeout(() => controller.abort(), 30000)

          return fetch(url, {
            ...options,
            signal: controller.signal,
          }).finally(() => clearTimeout(timeoutId))
        }
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    })

    return {
      client,
      inUse: false,
      lastUsed: Date.now(),
      created: Date.now()
    }
  }

  async getConnection(): Promise<SupabaseClient> {
    // Find available connection
    let connection = this.pool.find(conn => !conn.inUse)

    // Create new connection if none available and under limit
    if (!connection && this.pool.length < this.maxConnections) {
      connection = this.createConnection()
      this.pool.push(connection)
      console.log(`🔧 [POOL] Created new connection (${this.pool.length}/${this.maxConnections})`)
    }

    // Wait for connection if at limit
    if (!connection) {
      console.log(`⏳ [POOL] Waiting for available connection...`)
      await new Promise(resolve => setTimeout(resolve, 100))
      return this.getConnection() // Retry
    }

    // Mark as in use
    connection.inUse = true
    connection.lastUsed = Date.now()

    console.log(`✅ [POOL] Connection acquired (${this.getActiveConnections()}/${this.pool.length} active)`)
    return connection.client
  }

  releaseConnection(client: SupabaseClient): void {
    const connection = this.pool.find(conn => conn.client === client)
    if (connection) {
      connection.inUse = false
      connection.lastUsed = Date.now()
      console.log(`🔄 [POOL] Connection released (${this.getActiveConnections()}/${this.pool.length} active)`)
    }
  }

  private getActiveConnections(): number {
    return this.pool.filter(conn => conn.inUse).length
  }

  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      const now = Date.now()
      const initialCount = this.pool.length

      // Remove idle connections
      this.pool = this.pool.filter(conn => {
        const isIdle = !conn.inUse && (now - conn.lastUsed) > this.maxIdleTime
        if (isIdle) {
          console.log(`🧹 [POOL] Removing idle connection (idle for ${Math.round((now - conn.lastUsed) / 1000)}s)`)
        }
        return !isIdle
      })

      if (this.pool.length !== initialCount) {
        console.log(`🧹 [POOL] Cleanup complete: ${initialCount} → ${this.pool.length} connections`)
      }
    }, 60000) // Cleanup every minute
  }

  getStats() {
    return {
      total: this.pool.length,
      active: this.getActiveConnections(),
      idle: this.pool.length - this.getActiveConnections(),
      maxConnections: this.maxConnections,
    }
  }

  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.pool = []
    console.log(`🗑️ [POOL] Connection pool destroyed`)
  }
}

// ✅ SINGLETON: Global connection pool instance
let poolInstance: SupabaseConnectionPool | null = null

export function getConnectionPool(): SupabaseConnectionPool {
  if (!poolInstance) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    
    poolInstance = new SupabaseConnectionPool(supabaseUrl, supabaseKey, 10, 5 * 60 * 1000)
    console.log(`🏊 [POOL] Connection pool initialized`)
  }
  
  return poolInstance
}

// ✅ HELPER: Pooled query execution
export async function executePooledQuery<T>(
  queryFn: (client: SupabaseClient) => Promise<T>
): Promise<T> {
  const pool = getConnectionPool()
  const client = await pool.getConnection()
  
  try {
    const result = await queryFn(client)
    return result
  } finally {
    pool.releaseConnection(client)
  }
}
