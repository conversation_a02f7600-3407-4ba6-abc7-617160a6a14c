"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { CustomerAddress } from "@/app/types/customer"
import { MapPin, Plus, Edit, Trash2, Building, Home, Warehouse, CreditCard } from "lucide-react"

interface AddressManagerProps {
  addresses: CustomerAddress[]
  onAddressChange: (addresses: CustomerAddress[]) => void
  customerId?: string
}

interface AddressFormData {
  address_type: 'billing' | 'shipping' | 'office' | 'warehouse'
  is_primary: boolean
  company_name: string
  contact_person: string
  street_address: string
  address_line_2: string
  city: string
  state_province: string
  postal_code: string
  country: string
  phone: string
  email: string
  special_instructions: string
}

export function AddressManager({ addresses, onAddressChange, customerId }: AddressManagerProps) {
  const [isAddingAddress, setIsAddingAddress] = useState(false)
  const [editingAddress, setEditingAddress] = useState<CustomerAddress | null>(null)
  const [formData, setFormData] = useState<AddressFormData>({
    address_type: 'billing',
    is_primary: false,
    company_name: '',
    contact_person: '',
    street_address: '',
    address_line_2: '',
    city: '',
    state_province: '',
    postal_code: '',
    country: 'Jordan',
    phone: '',
    email: '',
    special_instructions: ''
  })

  const getAddressIcon = (type: string) => {
    switch (type) {
      case 'billing': return <CreditCard className="h-4 w-4" />
      case 'shipping': return <MapPin className="h-4 w-4" />
      case 'office': return <Building className="h-4 w-4" />
      case 'warehouse': return <Warehouse className="h-4 w-4" />
      default: return <Home className="h-4 w-4" />
    }
  }

  const getAddressTypeColor = (type: string) => {
    switch (type) {
      case 'billing': return 'bg-blue-100 text-blue-800'
      case 'shipping': return 'bg-green-100 text-green-800'
      case 'office': return 'bg-purple-100 text-purple-800'
      case 'warehouse': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const handleInputChange = (field: keyof AddressFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const resetForm = () => {
    setFormData({
      address_type: 'billing',
      is_primary: false,
      company_name: '',
      contact_person: '',
      street_address: '',
      address_line_2: '',
      city: '',
      state_province: '',
      postal_code: '',
      country: 'Jordan',
      phone: '',
      email: '',
      special_instructions: ''
    })
    setIsAddingAddress(false)
    setEditingAddress(null)
  }

  const handleSaveAddress = () => {
    const newAddress: CustomerAddress = {
      id: editingAddress?.id || `temp-${Date.now()}`,
      customer_id: customerId || '',
      user_id: '', // Will be set by the backend
      ...formData,
      created_at: editingAddress?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    let updatedAddresses: CustomerAddress[]
    
    if (editingAddress) {
      updatedAddresses = addresses.map(addr => 
        addr.id === editingAddress.id ? newAddress : addr
      )
    } else {
      updatedAddresses = [...addresses, newAddress]
    }

    // If this is set as primary, make sure no other address of the same type is primary
    if (formData.is_primary) {
      updatedAddresses = updatedAddresses.map(addr => 
        addr.address_type === formData.address_type && addr.id !== newAddress.id
          ? { ...addr, is_primary: false }
          : addr
      )
    }

    onAddressChange(updatedAddresses)
    resetForm()
  }

  const handleEditAddress = (address: CustomerAddress) => {
    setFormData({
      address_type: address.address_type,
      is_primary: address.is_primary,
      company_name: address.company_name || '',
      contact_person: address.contact_person || '',
      street_address: address.street_address,
      address_line_2: address.address_line_2 || '',
      city: address.city,
      state_province: address.state_province || '',
      postal_code: address.postal_code || '',
      country: address.country,
      phone: address.phone || '',
      email: address.email || '',
      special_instructions: address.special_instructions || ''
    })
    setEditingAddress(address)
    setIsAddingAddress(true)
  }

  const handleDeleteAddress = (addressId: string) => {
    const updatedAddresses = addresses.filter(addr => addr.id !== addressId)
    onAddressChange(updatedAddresses)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Addresses</h3>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setIsAddingAddress(true)}
          disabled={isAddingAddress}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Address
        </Button>
      </div>

      {/* Existing Addresses */}
      {addresses.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map((address) => (
            <Card key={address.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getAddressIcon(address.address_type)}
                    <CardTitle className="text-sm capitalize">
                      {address.address_type} Address
                    </CardTitle>
                    {address.is_primary && (
                      <Badge variant="secondary" className="text-xs">Primary</Badge>
                    )}
                  </div>
                  <div className="flex gap-1">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditAddress(address)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAddress(address.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-1 text-sm">
                  {address.company_name && (
                    <p className="font-medium">{address.company_name}</p>
                  )}
                  {address.contact_person && (
                    <p className="text-gray-600">{address.contact_person}</p>
                  )}
                  <p>{address.street_address}</p>
                  {address.address_line_2 && <p>{address.address_line_2}</p>}
                  <p>{address.city}, {address.state_province} {address.postal_code}</p>
                  <p>{address.country}</p>
                  {address.phone && (
                    <p className="text-gray-600">📞 {address.phone}</p>
                  )}
                  {address.email && (
                    <p className="text-gray-600">✉️ {address.email}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Address Form */}
      {isAddingAddress && (
        <Card>
          <CardHeader>
            <CardTitle>
              {editingAddress ? 'Edit Address' : 'Add New Address'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="address_type">Address Type</Label>
                <Select 
                  value={formData.address_type} 
                  onValueChange={(value) => handleInputChange("address_type", value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="billing">Billing Address</SelectItem>
                    <SelectItem value="shipping">Shipping Address</SelectItem>
                    <SelectItem value="office">Office Address</SelectItem>
                    <SelectItem value="warehouse">Warehouse Address</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_primary"
                    checked={formData.is_primary}
                    onCheckedChange={(checked) => handleInputChange("is_primary", checked)}
                  />
                  <Label htmlFor="is_primary">Primary {formData.address_type} address</Label>
                </div>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="company_name">Company Name</Label>
                <Input
                  id="company_name"
                  value={formData.company_name}
                  onChange={(e) => handleInputChange("company_name", e.target.value)}
                  placeholder="Company name for this address"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contact_person">Contact Person</Label>
                <Input
                  id="contact_person"
                  value={formData.contact_person}
                  onChange={(e) => handleInputChange("contact_person", e.target.value)}
                  placeholder="Contact person at this address"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="street_address">Street Address *</Label>
              <Input
                id="street_address"
                value={formData.street_address}
                onChange={(e) => handleInputChange("street_address", e.target.value)}
                placeholder="123 Main Street"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="address_line_2">Address Line 2</Label>
              <Input
                id="address_line_2"
                value={formData.address_line_2}
                onChange={(e) => handleInputChange("address_line_2", e.target.value)}
                placeholder="Apartment, suite, etc."
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  placeholder="Amman"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="state_province">State/Province</Label>
                <Input
                  id="state_province"
                  value={formData.state_province}
                  onChange={(e) => handleInputChange("state_province", e.target.value)}
                  placeholder="State or Province"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="postal_code">Postal Code</Label>
                <Input
                  id="postal_code"
                  value={formData.postal_code}
                  onChange={(e) => handleInputChange("postal_code", e.target.value)}
                  placeholder="11181"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="country">Country *</Label>
              <Input
                id="country"
                value={formData.country}
                onChange={(e) => handleInputChange("country", e.target.value)}
                placeholder="Jordan"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  placeholder="+962-6-123-4567"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="special_instructions">Special Instructions</Label>
              <Textarea
                id="special_instructions"
                value={formData.special_instructions}
                onChange={(e) => handleInputChange("special_instructions", e.target.value)}
                placeholder="Any special delivery instructions..."
                rows={2}
              />
            </div>

            <div className="flex justify-end gap-3">
              <Button type="button" variant="outline" onClick={resetForm}>
                Cancel
              </Button>
              <Button type="button" onClick={handleSaveAddress}>
                {editingAddress ? 'Update Address' : 'Add Address'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
