import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { createClient } from '@supabase/supabase-js'
import { authOptions } from '@/lib/auth-config'

// Create server-side Supabase client with service role for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

// Helper function to check if user is admin
async function checkAdminAccess() {
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    return { error: 'Unauthorized', status: 401 }
  }

  const userRole = (session.user as any).role
  if (userRole !== 'admin') {
    return { error: 'Admin access required', status: 403 }
  }

  return { user: session.user, role: userRole }
}

// GET - Fetch all users (admin only)
export async function GET() {
  try {
    console.log('🔍 [ADMIN-USERS-API] Fetching all users...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    // Fetch all users using service role
    const { data: users, error } = await supabaseAdmin
      .from('users')
      .select('id, email, full_name, role, department, phone, status, created_at, updated_at')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ [ADMIN-USERS-API] Database error:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log(`✅ [ADMIN-USERS-API] Successfully fetched ${users?.length || 0} users`)
    return NextResponse.json({ data: users || [], count: users?.length || 0 })

  } catch (error: any) {
    console.error('❌ [ADMIN-USERS-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Create new user (admin only)
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 [ADMIN-USERS-API] Creating new user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const body = await request.json()
    const { email, full_name, role = 'user', department, phone, password = 'TempPassword123!' } = body

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('email')
      .eq('email', email)
      .single()

    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 409 })
    }

    // Create user in Supabase Auth (if using Supabase Auth)
    // For now, we'll just create the user record directly
    const newUser = {
      id: crypto.randomUUID(), // Generate UUID for new user
      email,
      full_name,
      role,
      department,
      phone,
      password, // In production, this should be hashed
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: createdUser, error } = await supabaseAdmin
      .from('users')
      .insert(newUser)
      .select()
      .single()

    if (error) {
      console.error('❌ [ADMIN-USERS-API] Error creating user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ [ADMIN-USERS-API] User created successfully:', createdUser.email)
    return NextResponse.json({ data: createdUser }, { status: 201 })

  } catch (error: any) {
    console.error('❌ [ADMIN-USERS-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PUT - Update user (admin only)
export async function PUT(request: NextRequest) {
  try {
    console.log('🔍 [ADMIN-USERS-API] Updating user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const body = await request.json()
    const { id, full_name, role, department, phone, status } = body

    if (!id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    const updateData = {
      full_name,
      role,
      department,
      phone,
      status,
      updated_at: new Date().toISOString()
    }

    // Remove undefined values
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData]
      }
    })

    const { data: updatedUser, error } = await supabaseAdmin
      .from('users')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('❌ [ADMIN-USERS-API] Error updating user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!updatedUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    console.log('✅ [ADMIN-USERS-API] User updated successfully:', updatedUser.email)
    return NextResponse.json({ data: updatedUser })

  } catch (error: any) {
    console.error('❌ [ADMIN-USERS-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Delete user (admin only)
export async function DELETE(request: NextRequest) {
  try {
    console.log('🔍 [ADMIN-USERS-API] Deleting user...')
    
    const authCheck = await checkAdminAccess()
    if ('error' in authCheck) {
      return NextResponse.json({ error: authCheck.error }, { status: authCheck.status })
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('id')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    // Prevent admin from deleting themselves
    const currentUserId = (authCheck.user as any).id
    if (userId === currentUserId) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 })
    }

    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId)

    if (error) {
      console.error('❌ [ADMIN-USERS-API] Error deleting user:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    console.log('✅ [ADMIN-USERS-API] User deleted successfully')
    return NextResponse.json({ message: 'User deleted successfully' })

  } catch (error: any) {
    console.error('❌ [ADMIN-USERS-API] Server error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
