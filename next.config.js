/** @type {import('next').NextConfig} */
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

const nextConfig = {
  // ✅ SIMPLIFIED: Use default build ID for Vercel compatibility
  // generateBuildId: async () => {
  //   return `cache-bust-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  // },

  // ✅ SIMPLIFIED: Basic performance optimizations only
  experimental: {
    // Enable optimized package imports (Next.js 15 compatible)
    optimizePackageImports: [
      '@radix-ui/react-icons',
      'lucide-react',
      'date-fns'
    ],

    // Disable React Compiler for stability
    reactCompiler: false,
  },

  // ✅ SIMPLIFIED: Remove complex Turbopack configuration
  // turbopack: {
  //   rules: {
  //     '*.svg': {
  //       loaders: ['@svgr/webpack'],
  //       as: '*.js',
  //     },
  //   },
  // },

  // Server components external packages
  serverExternalPackages: ['pg'],

  // ✅ SIMPLIFIED: Basic compiler optimizations only
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Headers for performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN'
          },
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          }
        ]
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=300, s-maxage=300'
          }
        ]
      },
      {
        source: '/dashboard/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0'
          },
          {
            key: 'Pragma',
            value: 'no-cache'
          },
          {
            key: 'Expires',
            value: '0'
          },
          {
            key: 'X-RSC-Compatible',
            value: 'true'
          }
        ]
      },
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0'
          },
          {
            key: 'Pragma',
            value: 'no-cache'
          },
          {
            key: 'Expires',
            value: '0'
          }
        ]
      }
    ]
  },

  // ✅ SIMPLIFIED: Minimal webpack configuration for Vercel compatibility
  webpack: (config, { buildId, dev, isServer, defaultLoaders, webpack }) => {
    // ✅ REMOVED: Aggressive cache busting that may cause deployment issues
    // Only add essential optimizations

    // Basic resolve aliases
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname),
    }

    // Add fallbacks for Node.js modules in client-side bundles
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
    }

    return config
  },

  // ✅ SIMPLIFIED: Basic production optimizations only
  ...(process.env.NODE_ENV === 'production' && {
    // Disable source maps in production for smaller bundles
    productionBrowserSourceMaps: false,

    // Disable x-powered-by header
    poweredByHeader: false
  }),

  // ✅ SIMPLIFIED: Basic development optimizations
  ...(process.env.NODE_ENV === 'development' && {
    // Fast refresh
    reactStrictMode: true,
  })
}

module.exports = withBundleAnalyzer(nextConfig)