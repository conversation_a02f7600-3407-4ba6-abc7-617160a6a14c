"use client"

import * as React from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  AlertCircle, CheckCircle, Eye, EyeOff, Info, 
  Calendar, Clock, DollarSign, Percent, Hash,
  Phone, Mail, Globe, MapPin, User, Building2
} from "lucide-react"
import { cn } from "@/lib/utils"

// Field Types
export type FieldType = 
  | "text" | "email" | "password" | "number" | "tel" | "url"
  | "textarea" | "select" | "checkbox" | "radio" | "switch"
  | "date" | "datetime-local" | "time" | "currency" | "percentage"

export interface FormFieldProps {
  id: string
  name?: string
  label?: string
  description?: string
  placeholder?: string
  type?: FieldType
  value?: any
  defaultValue?: any
  onChange?: (value: any) => void
  onBlur?: () => void
  onFocus?: () => void
  required?: boolean
  disabled?: boolean
  readOnly?: boolean
  error?: string
  success?: string
  hint?: string
  options?: Array<{ value: string; label: string; disabled?: boolean }>
  validation?: {
    required?: boolean
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: RegExp
    custom?: (value: any) => string | undefined
  }
  icon?: React.ComponentType<{ className?: string }>
  prefix?: string
  suffix?: string
  className?: string
  inputClassName?: string
  labelClassName?: string
  size?: "sm" | "md" | "lg"
  variant?: "default" | "filled" | "outlined"
}

// Get appropriate icon for field type
const getFieldIcon = (type: FieldType): React.ComponentType<{ className?: string }> | undefined => {
  switch (type) {
    case "email": return Mail
    case "tel": return Phone
    case "url": return Globe
    case "date": return Calendar
    case "time": return Clock
    case "currency": return DollarSign
    case "percentage": return Percent
    case "number": return Hash
    default: return undefined
  }
}

// Format value based on field type
const formatValue = (value: any, type: FieldType): string => {
  if (value === null || value === undefined) return ""
  
  switch (type) {
    case "currency":
      return typeof value === "number" ? value.toString() : value
    case "percentage":
      return typeof value === "number" ? value.toString() : value
    default:
      return value.toString()
  }
}

// Parse value based on field type
const parseValue = (value: string, type: FieldType): any => {
  switch (type) {
    case "number":
    case "currency":
    case "percentage":
      const num = parseFloat(value)
      return isNaN(num) ? undefined : num
    case "checkbox":
    case "switch":
      return Boolean(value)
    default:
      return value
  }
}

export function FormField({
  id,
  name = id,
  label,
  description,
  placeholder,
  type = "text",
  value,
  defaultValue,
  onChange,
  onBlur,
  onFocus,
  required = false,
  disabled = false,
  readOnly = false,
  error,
  success,
  hint,
  options = [],
  validation,
  icon: IconProp,
  prefix,
  suffix,
  className,
  inputClassName,
  labelClassName,
  size = "md",
  variant = "default"
}: FormFieldProps) {
  const [showPassword, setShowPassword] = React.useState(false)
  const [internalValue, setInternalValue] = React.useState(value || defaultValue || "")
  const [touched, setTouched] = React.useState(false)
  const [validationError, setValidationError] = React.useState<string>()

  // Use provided icon or get default for field type
  const Icon = IconProp || getFieldIcon(type)

  // Validate field value
  const validateField = React.useCallback((val: any): string | undefined => {
    if (!validation) return undefined

    if (validation.required && (!val || val === "")) {
      return `${label || "Field"} is required`
    }

    if (val && validation.minLength && val.toString().length < validation.minLength) {
      return `Minimum ${validation.minLength} characters required`
    }

    if (val && validation.maxLength && val.toString().length > validation.maxLength) {
      return `Maximum ${validation.maxLength} characters allowed`
    }

    if (val && validation.min && Number(val) < validation.min) {
      return `Minimum value is ${validation.min}`
    }

    if (val && validation.max && Number(val) > validation.max) {
      return `Maximum value is ${validation.max}`
    }

    if (val && validation.pattern && !validation.pattern.test(val.toString())) {
      return `Invalid format`
    }

    if (validation.custom) {
      return validation.custom(val)
    }

    return undefined
  }, [validation, label])

  // Handle value changes
  const handleChange = React.useCallback((newValue: any) => {
    const parsedValue = parseValue(newValue, type)
    setInternalValue(newValue)
    
    if (onChange) {
      onChange(parsedValue)
    }

    // Validate on change if field has been touched
    if (touched) {
      const error = validateField(parsedValue)
      setValidationError(error)
    }
  }, [onChange, type, touched, validateField])

  // Handle blur events
  const handleBlur = React.useCallback(() => {
    setTouched(true)
    const error = validateField(parseValue(internalValue, type))
    setValidationError(error)
    
    if (onBlur) {
      onBlur()
    }
  }, [internalValue, type, validateField, onBlur])

  // Update internal value when external value changes
  React.useEffect(() => {
    if (value !== undefined) {
      setInternalValue(formatValue(value, type))
    }
  }, [value, type])

  // Determine field state
  const hasError = !!(error || validationError)
  const hasSuccess = !!success && !hasError
  const displayError = error || validationError
  const displayHint = hint && !hasError && !hasSuccess

  // Size classes
  const sizeClasses = {
    sm: "h-8 text-sm",
    md: "h-10 text-sm",
    lg: "h-12 text-base"
  }

  // Common input props
  const inputProps = {
    id,
    name,
    placeholder,
    value: internalValue,
    disabled,
    readOnly,
    onFocus,
    onBlur: handleBlur,
    className: cn(
      sizeClasses[size],
      hasError && "border-destructive focus:border-destructive",
      hasSuccess && "border-success focus:border-success",
      inputClassName
    )
  }

  // Render different input types
  const renderInput = () => {
    switch (type) {
      case "textarea":
        return (
          <Textarea
            {...inputProps}
            onChange={(e) => handleChange(e.target.value)}
            className={cn("min-h-[80px] resize-y", inputProps.className)}
          />
        )

      case "select":
        return (
          <Select value={internalValue} onValueChange={handleChange} disabled={disabled}>
            <SelectTrigger className={inputProps.className}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value}
                  disabled={option.disabled}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={id}
              checked={Boolean(internalValue)}
              onCheckedChange={handleChange}
              disabled={disabled}
            />
            {label && (
              <Label htmlFor={id} className={cn("text-sm font-normal", labelClassName)}>
                {label}
                {required && <span className="text-destructive ml-1">*</span>}
              </Label>
            )}
          </div>
        )

      case "radio":
        return (
          <RadioGroup value={internalValue} onValueChange={handleChange} disabled={disabled}>
            {options.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`${id}-${option.value}`} />
                <Label htmlFor={`${id}-${option.value}`} className="text-sm font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        )

      case "switch":
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={id}
              checked={Boolean(internalValue)}
              onCheckedChange={handleChange}
              disabled={disabled}
            />
            {label && (
              <Label htmlFor={id} className={cn("text-sm font-normal", labelClassName)}>
                {label}
                {required && <span className="text-destructive ml-1">*</span>}
              </Label>
            )}
          </div>
        )

      case "password":
        return (
          <div className="relative">
            <Input
              {...inputProps}
              type={showPassword ? "text" : "password"}
              onChange={(e) => handleChange(e.target.value)}
              className={cn(Icon && "pl-10", "pr-10")}
            />
            {Icon && (
              <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            )}
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-muted-foreground" />
              ) : (
                <Eye className="h-4 w-4 text-muted-foreground" />
              )}
            </Button>
          </div>
        )

      default:
        const inputType = type === "currency" || type === "percentage" ? "number" : type
        return (
          <div className="relative">
            {prefix && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                {prefix}
              </div>
            )}
            <Input
              {...inputProps}
              type={inputType}
              onChange={(e) => handleChange(e.target.value)}
              className={cn(
                Icon && !prefix && "pl-10",
                prefix && "pl-8",
                suffix && "pr-8",
                inputProps.className
              )}
              step={type === "currency" ? "0.01" : undefined}
            />
            {Icon && !prefix && (
              <Icon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            )}
            {suffix && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
                {suffix}
              </div>
            )}
          </div>
        )
    }
  }

  // For checkbox and switch, render differently
  if (type === "checkbox" || type === "switch") {
    return (
      <div className={cn("space-y-2", className)}>
        {renderInput()}
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
        {displayError && (
          <div className="flex items-center gap-1 text-xs text-destructive">
            <AlertCircle className="h-3 w-3" />
            {displayError}
          </div>
        )}
        {hasSuccess && success && (
          <div className="flex items-center gap-1 text-xs text-success">
            <CheckCircle className="h-3 w-3" />
            {success}
          </div>
        )}
        {displayHint && (
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Info className="h-3 w-3" />
            {displayHint}
          </div>
        )}
      </div>
    )
  }

  // Standard field layout
  return (
    <div className={cn("space-y-2", className)}>
      {label && !["checkbox", "switch"].includes(type as string) && (
        <Label htmlFor={id} className={cn("text-sm font-medium", labelClassName)}>
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>
      )}
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      {renderInput()}
      
      {displayError && (
        <div className="flex items-center gap-1 text-xs text-destructive">
          <AlertCircle className="h-3 w-3" />
          {displayError}
        </div>
      )}
      
      {hasSuccess && success && (
        <div className="flex items-center gap-1 text-xs text-success">
          <CheckCircle className="h-3 w-3" />
          {success}
        </div>
      )}
      
      {displayHint && (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Info className="h-3 w-3" />
          {displayHint}
        </div>
      )}
    </div>
  )
}
