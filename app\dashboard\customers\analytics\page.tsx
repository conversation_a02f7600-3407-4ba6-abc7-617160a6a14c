"use client"

import { useState, useEffect } from "react"
import { CustomerAnalytics, type CustomerAnalytics as CustomerAnalyticsType } from "@/components/customers/customer-analytics"
import { CustomerFormData } from "@/app/types/customer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import {
  BarChart, Download, RefreshCw, Settings,
  TrendingUp, Users, DollarSign, AlertCircle
} from "lucide-react"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

export default function CustomerAnalyticsPage() {
  const [customers, setCustomers] = useState<CustomerFormData[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch customers data
  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      setError(null)
      const response = await fetch('/api/customers')
      
      if (!response.ok) {
        throw new Error('Failed to fetch customers')
      }
      
      const data = await response.json()
      setCustomers(data.customers || [])
    } catch (error) {
      console.error('Failed to fetch customers:', error)
      setError((error as Error).message)
      toast.error('Failed to load customer data')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchCustomers()
    setRefreshing(false)
    toast.success('Customer data refreshed')
  }

  const handleExportReport = async (analytics: CustomerAnalyticsType) => {
    try {
      // Generate comprehensive analytics report
      const report = {
        generatedAt: new Date().toISOString(),
        dateRange: "All time", // Would be dynamic based on filters
        summary: {
          totalCustomers: analytics.overview.totalCustomers,
          activeCustomers: analytics.overview.activeCustomers,
          totalRevenue: analytics.overview.totalRevenue,
          avgCustomerValue: analytics.overview.avgCustomerValue,
          growthRate: analytics.overview.growthRate,
          retentionRate: analytics.overview.retentionRate
        },
        demographics: analytics.demographics,
        segmentation: analytics.segmentation,
        financial: analytics.financial,
        insights: {
          topPerformers: analytics.insights.topPerformers.map(c => ({
            company: c.company,
            contactPerson: c.contact_person,
            annualVolume: c.annual_volume,
            tier: c.customer_tier
          })),
          riskCustomers: analytics.insights.riskCustomers.map(c => ({
            company: c.company,
            contactPerson: c.contact_person,
            status: c.status,
            lastActivity: c.customer_since
          })),
          growthOpportunities: analytics.insights.growthOpportunities.map(c => ({
            company: c.company,
            contactPerson: c.contact_person,
            currentTier: c.customer_tier,
            annualVolume: c.annual_volume
          }))
        }
      }

      // Convert to JSON and download
      const blob = new Blob([JSON.stringify(report, null, 2)], { 
        type: 'application/json' 
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `customer-analytics-report-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(a)
      a.click()
      URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast.success('Analytics report exported successfully')
    } catch (error) {
      console.error('Export failed:', error)
      toast.error('Failed to export analytics report')
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="flex gap-3">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-40" />
          </div>
        </div>

        {/* Metrics Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-64 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}. Please try refreshing the page or contact support if the problem persists.
          </AlertDescription>
        </Alert>
        
        <div className="mt-6 text-center">
          <Button onClick={handleRefresh} variant="outline" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      </div>
    )
  }

  if (customers.length === 0) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <Users className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-semibold mb-2">No Customer Data Available</h2>
          <p className="text-muted-foreground mb-6">
            Add some customers to your CRM to see analytics and insights.
          </p>
          <Button onClick={() => window.location.href = '/dashboard/customers'}>
            Go to Customers
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="heading-1 text-foreground flex items-center gap-2">
            <BarChart className="h-8 w-8" />
            Customer Analytics
          </h1>
          <p className="body-large text-muted-foreground mt-2">
            Comprehensive insights and analytics for your customer base
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={refreshing}
            className="gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
            {refreshing ? "Refreshing..." : "Refresh"}
          </Button>
          
          <Button variant="outline" className="gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-xl font-bold">{customers.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Active Customers</p>
                <p className="text-xl font-bold">
                  {customers.filter(c => c.status === "Active" || !c.status).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-xl font-bold">
                  ${customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg Customer Value</p>
                <p className="text-xl font-bold">
                  ${customers.length > 0 ? 
                    Math.round(customers.reduce((sum, c) => sum + (c.annual_volume || 0), 0) / customers.length).toLocaleString() : 
                    0
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Component */}
      <CustomerAnalytics
        customers={customers}
        onExportReport={handleExportReport}
      />

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Data Quality Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Complete Profiles</span>
                <span className="text-sm font-medium">
                  {Math.round((customers.filter(c => 
                    c.contact_person && c.email && c.company && c.city && c.country
                  ).length / customers.length) * 100)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">With Phone Numbers</span>
                <span className="text-sm font-medium">
                  {Math.round((customers.filter(c => c.phone || c.mobile).length / customers.length) * 100)}%
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">With Annual Volume</span>
                <span className="text-sm font-medium">
                  {Math.round((customers.filter(c => c.annual_volume && c.annual_volume > 0).length / customers.length) * 100)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                <div>
                  <p className="text-sm font-medium">Improve Data Completeness</p>
                  <p className="text-xs text-muted-foreground">
                    {customers.filter(c => !c.phone && !c.mobile).length} customers missing contact numbers
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2" />
                <div>
                  <p className="text-sm font-medium">Focus on High-Value Segments</p>
                  <p className="text-xs text-muted-foreground">
                    {customers.filter(c => c.customer_tier === "Gold" || c.customer_tier === "Platinum").length} customers in premium tiers
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2" />
                <div>
                  <p className="text-sm font-medium">Geographic Expansion</p>
                  <p className="text-xs text-muted-foreground">
                    Consider expanding in top-performing regions
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
