// Chart Data Types for CRM Analytics
export interface ChartDataPoint {
  name: string
  value: number
  color?: string
  percentage?: number
  label?: string
}

export interface TimeSeriesDataPoint {
  date: string
  value: number
  label?: string
  category?: string
}

export interface PipelineStageData {
  stage: string
  count: number
  value: number
  probability: number
  color: string
}

export interface LeadSourceData {
  source: string
  count: number
  converted: number
  conversionRate: number
  color: string
}

export interface CustomerDistributionData {
  category: string
  count: number
  percentage: number
  color: string
}

export interface RevenueData {
  period: string
  revenue: number
  target?: number
  growth?: number
}

export interface ActivityData {
  type: string
  completed: number
  pending: number
  overdue: number
}

export interface MetricCardData {
  title: string
  value: string | number
  change: number
  changeType: 'increase' | 'decrease' | 'neutral'
  icon: string
  color: string
}

// Chart Configuration Types
export interface ChartConfig {
  responsive?: boolean
  showGrid?: boolean
  showTooltip?: boolean
  showLegend?: boolean
  height?: number
  colors?: string[]
  theme?: 'light' | 'dark'
}

export interface ChartProps {
  data: any[]
  config?: ChartConfig
  loading?: boolean
  error?: string | null
  className?: string
  title?: string
  subtitle?: string
}

// Aggregated Dashboard Data
export interface DashboardAnalytics {
  pipeline: PipelineStageData[]
  revenue: RevenueData[]
  leadSources: LeadSourceData[]
  customerDistribution: CustomerDistributionData[]
  activities: ActivityData[]
  metrics: MetricCardData[]
  trends: TimeSeriesDataPoint[]
}

// Chart Filter Options
export interface ChartFilters {
  dateRange: {
    start: Date
    end: Date
  }
  userId?: string
  customerTier?: string[]
  dealStage?: string[]
  leadStatus?: string[]
}
