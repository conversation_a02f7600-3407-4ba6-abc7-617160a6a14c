import { cn } from "@/lib/utils"

interface EnhancedSkeletonProps {
  className?: string
  variant?: "default" | "card" | "text" | "avatar" | "button" | "table-row"
  animation?: "pulse" | "wave" | "shimmer"
  lines?: number
  width?: string | number
  height?: string | number
}

export function EnhancedSkeleton({
  className,
  variant = "default",
  animation = "shimmer",
  lines = 1,
  width,
  height,
  ...props
}: EnhancedSkeletonProps) {
  const getVariantClasses = () => {
    switch (variant) {
      case "card":
        return "h-32 w-full rounded-lg"
      case "text":
        return "h-4 w-full rounded"
      case "avatar":
        return "h-10 w-10 rounded-full"
      case "button":
        return "h-10 w-24 rounded-md"
      case "table-row":
        return "h-12 w-full rounded"
      default:
        return "h-4 w-full rounded"
    }
  }

  const getAnimationClasses = () => {
    switch (animation) {
      case "pulse":
        return "animate-pulse"
      case "wave":
        return "animate-wave"
      case "shimmer":
        return "animate-shimmer bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%]"
      default:
        return "animate-pulse"
    }
  }

  const baseClasses = "bg-muted"
  const variantClasses = getVariantClasses()
  const animationClasses = getAnimationClasses()

  const style = {
    width: width ? (typeof width === 'number' ? `${width}px` : width) : undefined,
    height: height ? (typeof height === 'number' ? `${height}px` : height) : undefined,
  }

  if (lines > 1) {
    return (
      <div className="space-y-2">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              baseClasses,
              variantClasses,
              animationClasses,
              index === lines - 1 && "w-3/4", // Last line shorter
              className
            )}
            style={style}
            {...props}
          />
        ))}
      </div>
    )
  }

  return (
    <div
      className={cn(
        baseClasses,
        variantClasses,
        animationClasses,
        className
      )}
      style={style}
      {...props}
    />
  )
}

// Specialized skeleton components for common use cases
export function SkeletonCard({ className, ...props }: Omit<EnhancedSkeletonProps, "variant">) {
  return (
    <div className={cn("p-6 space-y-4", className)}>
      <EnhancedSkeleton variant="text" width="60%" />
      <EnhancedSkeleton variant="text" lines={2} />
      <div className="flex justify-between items-center">
        <EnhancedSkeleton variant="button" />
        <EnhancedSkeleton variant="avatar" />
      </div>
    </div>
  )
}

export function SkeletonTable({ rows = 5, columns = 4, className, ...props }: {
  rows?: number
  columns?: number
  className?: string
}) {
  return (
    <div className={cn("space-y-3", className)}>
      {/* Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <EnhancedSkeleton key={index} variant="text" width="80%" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <EnhancedSkeleton 
              key={colIndex} 
              variant="text" 
              width={colIndex === 0 ? "90%" : "70%"}
              animation="shimmer"
            />
          ))}
        </div>
      ))}
    </div>
  )
}

export function SkeletonMetric({ className, ...props }: Omit<EnhancedSkeletonProps, "variant">) {
  return (
    <div className={cn("p-6 space-y-3", className)}>
      <div className="flex justify-between items-start">
        <EnhancedSkeleton variant="text" width="60%" />
        <EnhancedSkeleton variant="avatar" width={32} height={32} />
      </div>
      <EnhancedSkeleton variant="text" width="40%" height={32} />
      <EnhancedSkeleton variant="text" width="50%" />
    </div>
  )
}

export function SkeletonForm({ fields = 4, className, ...props }: {
  fields?: number
  className?: string
}) {
  return (
    <div className={cn("space-y-6", className)}>
      {Array.from({ length: fields }).map((_, index) => (
        <div key={index} className="space-y-2">
          <EnhancedSkeleton variant="text" width="30%" />
          <EnhancedSkeleton variant="button" width="100%" height={40} />
        </div>
      ))}
      <div className="flex justify-end space-x-2">
        <EnhancedSkeleton variant="button" />
        <EnhancedSkeleton variant="button" />
      </div>
    </div>
  )
}
