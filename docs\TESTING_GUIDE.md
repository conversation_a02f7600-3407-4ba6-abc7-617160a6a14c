# Nawras CRM - Comprehensive Testing Guide

## Overview

This document provides comprehensive testing guidelines for the Nawras CRM application, covering all testing methodologies, tools, and best practices used in the project.

## Testing Strategy

### Testing Pyramid
```
                    E2E Tests (Playwright)
                   /                    \
              Integration Tests          \
             /                           \
        Unit Tests (Jest)                 \
       /                                   \
  Component Tests                          \
 /                                          \
Static Analysis (TypeScript, ESLint)        \
```

### Testing Levels
1. **Unit Tests**: Individual functions and components
2. **Component Tests**: React component behavior
3. **Integration Tests**: API and database interactions
4. **End-to-End Tests**: Complete user workflows
5. **Performance Tests**: Load and response time testing
6. **Security Tests**: Authentication and authorization

## Testing Tools and Configuration

### Primary Testing Stack
- **Playwright**: End-to-end testing with browser automation
- **Jest**: Unit and integration testing framework
- **React Testing Library**: Component testing utilities
- **MSW (Mock Service Worker)**: API mocking for tests
- **TypeScript**: Type checking and static analysis

### Configuration Files
```typescript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1'
  },
  collectCoverageFrom: [
    'components/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}',
    'hooks/**/*.{ts,tsx}',
    '!**/*.d.ts'
  ]
}

// playwright.config.ts
export default defineConfig({
  testDir: '__tests__',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry'
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } }
  ]
})
```

## Unit Testing

### Component Testing Best Practices
```typescript
// Example: CustomerForm component test
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { CustomerForm } from '@/components/customers/customer-form'
import { AuthProvider } from '@/components/auth-provider'

// Test wrapper with providers
const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <AuthProvider>
      {component}
    </AuthProvider>
  )
}

describe('CustomerForm', () => {
  const mockOnSubmit = jest.fn()
  const mockOnCancel = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders all required form fields', () => {
    renderWithProviders(
      <CustomerForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
    )

    expect(screen.getByLabelText(/contact person/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/company/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/country/i)).toBeInTheDocument()
  })

  it('validates required fields on submission', async () => {
    renderWithProviders(
      <CustomerForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
    )

    const submitButton = screen.getByRole('button', { name: /save/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText(/contact person is required/i)).toBeInTheDocument()
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
    })

    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    const validCustomerData = {
      contact_person: 'John Doe',
      email: '<EMAIL>',
      company: 'Example Corp',
      country: 'Jordan',
      city: 'Amman',
      source: 'Website',
      status: 'Active'
    }

    renderWithProviders(
      <CustomerForm onSubmit={mockOnSubmit} onCancel={mockOnCancel} />
    )

    // Fill form fields
    fireEvent.change(screen.getByLabelText(/contact person/i), {
      target: { value: validCustomerData.contact_person }
    })
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: validCustomerData.email }
    })
    // ... fill other fields

    const submitButton = screen.getByRole('button', { name: /save/i })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining(validCustomerData)
      )
    })
  })
})
```

### Hook Testing
```typescript
// Example: useCustomerData hook test
import { renderHook, waitFor } from '@testing-library/react'
import { useCustomerData } from '@/hooks/use-customer-data'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useCustomerData', () => {
  it('fetches customer data successfully', async () => {
    const { result } = renderHook(() => useCustomerData(), {
      wrapper: createWrapper()
    })

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })

    expect(result.current.data).toBeDefined()
    expect(result.current.error).toBeNull()
  })
})
```

## Integration Testing

### API Route Testing
```typescript
// Example: Customer API route test
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/customers/route'
import { createClient } from '@/lib/supabase'

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  createClient: jest.fn()
}))

describe('/api/customers', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('GET returns customers for authenticated user', async () => {
    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user-123' } },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            order: jest.fn().mockResolvedValue({
              data: [{ id: '1', name: 'Test Customer' }],
              error: null
            })
          })
        })
      })
    }

    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)

    const { req, res } = createMocks({ method: 'GET' })
    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    expect(JSON.parse(res._getData())).toEqual([
      { id: '1', name: 'Test Customer' }
    ])
  })

  it('POST creates new customer', async () => {
    const customerData = {
      contact_person: 'John Doe',
      email: '<EMAIL>',
      company: 'Example Corp'
    }

    const mockSupabase = {
      auth: {
        getUser: jest.fn().mockResolvedValue({
          data: { user: { id: 'user-123' } },
          error: null
        })
      },
      from: jest.fn().mockReturnValue({
        insert: jest.fn().mockResolvedValue({
          data: [{ id: 'new-customer-id', ...customerData }],
          error: null
        })
      })
    }

    ;(createClient as jest.Mock).mockReturnValue(mockSupabase)

    const { req, res } = createMocks({
      method: 'POST',
      body: customerData
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(201)
    expect(JSON.parse(res._getData())).toMatchObject({
      success: true,
      data: expect.objectContaining(customerData)
    })
  })
})
```

## End-to-End Testing with Playwright

### Test Structure
```typescript
// Example: Customer management E2E test
import { test, expect } from '@playwright/test'

test.describe('Customer Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login before each test
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', '111333Tt')
    await page.click('[data-testid="login-button"]')
    
    // Wait for dashboard to load
    await expect(page.locator('[data-testid="dashboard"]')).toBeVisible()
  })

  test('should create a new customer', async ({ page }) => {
    // Navigate to customers page
    await page.goto('/dashboard/customers')
    await expect(page.locator('h1')).toContainText('Customers')

    // Click add customer button
    await page.click('[data-testid="add-customer-button"]')
    
    // Fill customer form
    await page.fill('[data-testid="contact-person"]', 'John Doe')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="company"]', 'Example Corp')
    await page.selectOption('[data-testid="country"]', 'Jordan')
    await page.fill('[data-testid="city"]', 'Amman')
    await page.selectOption('[data-testid="source"]', 'Website')
    await page.selectOption('[data-testid="status"]', 'Active')

    // Submit form
    await page.click('[data-testid="submit-button"]')

    // Verify success
    await expect(page.locator('.success-message')).toBeVisible()
    await expect(page.locator('[data-testid="customers-table"]')).toContainText('John Doe')
  })

  test('should edit existing customer', async ({ page }) => {
    await page.goto('/dashboard/customers')
    
    // Click edit button for first customer
    await page.click('[data-testid="edit-customer-0"]')
    
    // Update customer information
    await page.fill('[data-testid="contact-person"]', 'John Smith')
    await page.click('[data-testid="submit-button"]')
    
    // Verify update
    await expect(page.locator('.success-message')).toBeVisible()
    await expect(page.locator('[data-testid="customers-table"]')).toContainText('John Smith')
  })

  test('should delete customer', async ({ page }) => {
    await page.goto('/dashboard/customers')
    
    // Click delete button
    await page.click('[data-testid="delete-customer-0"]')
    
    // Confirm deletion
    await page.click('[data-testid="confirm-delete"]')
    
    // Verify deletion
    await expect(page.locator('.success-message')).toBeVisible()
  })
})
```

### Cross-Module Integration Tests
```typescript
test.describe('Deal-Customer Integration', () => {
  test('should create deal linked to customer', async ({ page }) => {
    // First create a customer
    await page.goto('/dashboard/customers')
    await page.click('[data-testid="add-customer-button"]')
    // ... fill customer form
    await page.click('[data-testid="submit-button"]')
    
    // Then create a deal linked to that customer
    await page.goto('/dashboard/deals')
    await page.click('[data-testid="add-deal-button"]')
    await page.fill('[data-testid="deal-title"]', 'Test Deal')
    await page.selectOption('[data-testid="customer-select"]', 'John Doe')
    await page.fill('[data-testid="deal-value"]', '10000')
    await page.click('[data-testid="submit-button"]')
    
    // Verify deal is created and linked
    await expect(page.locator('[data-testid="deals-kanban"]')).toContainText('Test Deal')
    await expect(page.locator('[data-testid="deal-customer"]')).toContainText('John Doe')
  })
})
```

## Performance Testing

### Load Testing with Playwright
```typescript
test.describe('Performance Tests', () => {
  test('customer page loads within 3 seconds', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/dashboard/customers')
    await expect(page.locator('[data-testid="customers-table"]')).toBeVisible()
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(3000) // 3 seconds
  })

  test('form submission completes within 2 seconds', async ({ page }) => {
    await page.goto('/dashboard/customers')
    await page.click('[data-testid="add-customer-button"]')
    
    // Fill form
    await page.fill('[data-testid="contact-person"]', 'Performance Test')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="company"]', 'Test Corp')
    
    const startTime = Date.now()
    await page.click('[data-testid="submit-button"]')
    await expect(page.locator('.success-message')).toBeVisible()
    
    const submitTime = Date.now() - startTime
    expect(submitTime).toBeLessThan(2000) // 2 seconds
  })
})
```

## Security Testing

### Authentication Tests
```typescript
test.describe('Authentication Security', () => {
  test('should redirect unauthenticated users to login', async ({ page }) => {
    await page.goto('/dashboard/customers')
    await expect(page).toHaveURL(/.*\/login/)
  })

  test('should prevent access to admin pages for non-admin users', async ({ page }) => {
    // Login as regular user
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password')
    await page.click('[data-testid="login-button"]')
    
    // Try to access admin page
    await page.goto('/dashboard/users')
    await expect(page.locator('.error-message')).toContainText('Access denied')
  })
})
```

### Data Security Tests
```typescript
test.describe('Data Security', () => {
  test('users can only see their own data', async ({ page }) => {
    // Login as user 1
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password')
    await page.click('[data-testid="login-button"]')
    
    await page.goto('/dashboard/customers')
    const customerCount1 = await page.locator('[data-testid="customer-row"]').count()
    
    // Logout and login as user 2
    await page.click('[data-testid="logout-button"]')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password')
    await page.click('[data-testid="login-button"]')
    
    await page.goto('/dashboard/customers')
    const customerCount2 = await page.locator('[data-testid="customer-row"]').count()
    
    // Verify different data sets
    expect(customerCount1).not.toBe(customerCount2)
  })
})
```

## Test Data Management

### Test Data Setup
```typescript
// test-helpers/setup-test-data.ts
export async function setupTestCustomer() {
  return {
    contact_person: 'Test Customer',
    email: '<EMAIL>',
    company: 'Test Company',
    country: 'Jordan',
    city: 'Amman',
    source: 'Website',
    status: 'Active'
  }
}

export async function cleanupTestData(page: Page) {
  // Clean up test data after tests
  await page.evaluate(() => {
    // Clear local storage
    localStorage.clear()
    sessionStorage.clear()
  })
}
```

### Database Seeding for Tests
```typescript
// test-helpers/seed-database.ts
export async function seedTestDatabase() {
  const supabase = createClient()
  
  // Insert test customers
  await supabase.from('customers').insert([
    {
      contact_person: 'Test Customer 1',
      email: '<EMAIL>',
      company: 'Test Company 1'
    },
    {
      contact_person: 'Test Customer 2',
      email: '<EMAIL>',
      company: 'Test Company 2'
    }
  ])
}
```

## Running Tests

### Test Commands
```bash
# Run all unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run specific test file
npm test customer-form.test.tsx

# Run tests for specific module
npm test -- --testPathPattern=customers

# Run E2E tests in headed mode
npm run test:e2e:headed

# Generate test report
npm run test:report
```

### Continuous Integration
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm test -- --coverage
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: test-results/
```

## Test Coverage Requirements

### Coverage Targets
- **Unit Tests**: Minimum 80% code coverage
- **Component Tests**: 90% component coverage
- **Integration Tests**: All API endpoints tested
- **E2E Tests**: All critical user journeys covered

### Coverage Reporting
```bash
# Generate coverage report
npm run test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

This comprehensive testing guide ensures thorough testing of all aspects of the Nawras CRM application, from individual components to complete user workflows, maintaining high quality and reliability standards.
