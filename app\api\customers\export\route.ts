import { NextRequest, NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'
import { CustomerExportOptions } from '@/components/customers/customer-import-export'
import { CustomerDatabaseSchema } from '@/app/types/customer'

// Helper function to convert data to CSV
function convertToCSV(data: any[], options: CustomerExportOptions): string {
  if (data.length === 0) return ''

  const delimiter = options.delimiter || ','
  const headers = Object.keys(data[0])
  
  let csv = ''
  
  // Add headers if requested
  if (options.includeHeaders) {
    csv += headers.join(delimiter) + '\n'
  }
  
  // Add data rows
  data.forEach(row => {
    const values = headers.map(header => {
      let value = row[header] || ''
      
      // Escape values that contain the delimiter or quotes
      if (typeof value === 'string' && (value.includes(delimiter) || value.includes('"') || value.includes('\n'))) {
        value = '"' + value.replace(/"/g, '""') + '"'
      }
      
      return value
    })
    csv += values.join(delimiter) + '\n'
  })
  
  return csv
}

// Helper function to convert data to Excel format (simplified)
function convertToExcel(data: any[], options: CustomerExportOptions): string {
  // For a full Excel implementation, you would use a library like 'xlsx'
  // For now, we'll return CSV format with Excel-compatible formatting
  return convertToCSV(data, { ...options, delimiter: '\t' })
}

// Helper function to convert data to JSON
function convertToJSON(data: any[], options: CustomerExportOptions): string {
  return JSON.stringify(data, null, 2)
}

export async function POST(request: NextRequest) {
  try {
    // Use singleton Supabase client to prevent multiple GoTrueClient instances
    // Check authentication
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError || !session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { customerIds, options }: { customerIds?: string[], options: CustomerExportOptions } = body

    // Build query
    let query = supabase
      .from('customers')
      .select('*')
      .eq('user_id', session.user.id)

    // Apply filters if customer IDs are provided
    if (customerIds && customerIds.length > 0) {
      query = query.in('id', customerIds)
    }

    // Apply additional filters from options
    if (options.filters) {
      if (options.filters.status && options.filters.status.length > 0) {
        query = query.in('status', options.filters.status)
      }
      
      if (options.filters.tier && options.filters.tier.length > 0) {
        query = query.in('customer_tier', options.filters.tier)
      }
      
      if (options.filters.businessType && options.filters.businessType.length > 0) {
        query = query.in('business_type', options.filters.businessType)
      }
      
      if (options.filters.dateRange) {
        query = query
          .gte('created_at', options.filters.dateRange.start)
          .lte('created_at', options.filters.dateRange.end)
      }
    }

    const { data: customers, error } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json({ error: 'Failed to fetch customers' }, { status: 500 })
    }

    if (!customers || customers.length === 0) {
      return NextResponse.json({ error: 'No customers found' }, { status: 404 })
    }

    // Transform data based on selected columns
    const transformedData = customers.map((customer: CustomerDatabaseSchema) => {
      const row: any = {}
      
      options.columns.forEach(columnId => {
        let value = customer[columnId as keyof typeof customer]
        
        // Format dates
        if (columnId.includes('date') || columnId.includes('since')) {
          if (value && (typeof value === 'string' || typeof value === 'number')) {
            const date = new Date(value)
            switch (options.dateFormat) {
              case 'US':
                value = date.toLocaleDateString('en-US')
                break
              case 'EU':
                value = date.toLocaleDateString('en-GB')
                break
              default:
                value = date.toISOString().split('T')[0]
            }
          }
        }
        
        // Format currency
        if (columnId.includes('volume') || columnId.includes('limit')) {
          if (typeof value === 'number') {
            value = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: customer.currency_preference || 'USD'
            }).format(value)
          }
        }
        
        // Format arrays
        if (Array.isArray(value)) {
          value = value.join(', ')
        }
        
        // Get column label for header
        const columnLabel = columnId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
        row[columnLabel] = value || ''
      })
      
      return row
    })

    // Convert to requested format
    let content: string
    let contentType: string
    let filename: string

    switch (options.format) {
      case 'csv':
        content = convertToCSV(transformedData, options)
        contentType = 'text/csv'
        filename = `customers_export_${new Date().toISOString().split('T')[0]}.csv`
        break
        
      case 'excel':
        content = convertToExcel(transformedData, options)
        contentType = 'application/vnd.ms-excel'
        filename = `customers_export_${new Date().toISOString().split('T')[0]}.xls`
        break
        
      case 'json':
        content = convertToJSON(transformedData, options)
        contentType = 'application/json'
        filename = `customers_export_${new Date().toISOString().split('T')[0]}.json`
        break
        
      default:
        return NextResponse.json({ error: 'Unsupported export format' }, { status: 400 })
    }

    // Set appropriate headers for file download
    const headers = new Headers()
    headers.set('Content-Type', contentType)
    headers.set('Content-Disposition', `attachment; filename="${filename}"`)
    headers.set('Content-Length', Buffer.byteLength(content, (options.encoding || 'UTF-8').toLowerCase() as BufferEncoding).toString())

    return new NextResponse(content, { headers })

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET endpoint for export templates
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const templateType = searchParams.get('template')

    if (templateType === 'import') {
      // Return a CSV template for importing customers
      const headers = [
        'Contact Person',
        'Title/Position',
        'Email',
        'Phone',
        'Mobile',
        'Company',
        'Website',
        'City',
        'Country',
        'Business Type',
        'Industry',
        'Annual Volume',
        'Company Size',
        'Tax ID',
        'Credit Limit',
        'Payment Terms',
        'Currency',
        'Shipping Method',
        'Incoterms',
        'Shipping Instructions',
        'Account Manager',
        'Customer Since',
        'Customer Tier',
        'Tags',
        'Required Certificates',
        'Compliance Requirements',
        'Source',
        'Status',
        'Notes'
      ]

      // Add sample data row
      const sampleData = [
        'John Doe',
        'CEO',
        '<EMAIL>',
        '******-0123',
        '******-0124',
        'Example Corp',
        'https://example.com',
        'New York',
        'USA',
        'Retailer',
        'Technology',
        '500000',
        'Medium',
        'TAX123456',
        '100000',
        '30 Days',
        'USD',
        'Air',
        'FOB',
        'Handle with care',
        'Jane Smith',
        '2023-01-15',
        'Gold',
        'VIP, Priority',
        'ISO 9001, CE',
        'GDPR, SOX',
        'Website',
        'Active',
        'Important customer'
      ]

      const csvContent = headers.join(',') + '\n' + sampleData.join(',')

      const headers_response = new Headers()
      headers_response.set('Content-Type', 'text/csv')
      headers_response.set('Content-Disposition', 'attachment; filename="customer_import_template.csv"')

      return new NextResponse(csvContent, { headers: headers_response })
    }

    return NextResponse.json({ error: 'Invalid template type' }, { status: 400 })

  } catch (error) {
    console.error('Template generation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
