# Nawras CRM - Database Schema Documentation

## Overview

This document provides comprehensive documentation for the Supabase PostgreSQL database schema used in the Nawras CRM application. The schema is designed to support a multi-tenant CRM system with role-based access control.

## Database Configuration

### Connection Details
- **Provider**: Supabase (PostgreSQL 15+)
- **Project ID**: ojhtdwrzolfwbiwrprok
- **Region**: us-east-1
- **URL**: `https://ojhtdwrzolfwbiwrprok.supabase.co`

### Security Features
- Row Level Security (RLS) enabled on all tables
- User-based data isolation
- Role-based access control
- Audit logging for data changes

## Core Tables

### 1. Users Table (auth.users)
**Purpose**: User authentication and profile information (managed by Supa<PERSON> Auth)

```sql
-- Extended user metadata
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  full_name TEXT,
  avatar_url TEXT,
  role TEXT DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user')),
  department TEXT,
  phone TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Columns:**
- `id`: UUID, Primary Key, References auth.users(id)
- `full_name`: User's full name
- `avatar_url`: Profile picture URL
- `role`: User role (admin, manager, user)
- `department`: User's department
- `phone`: Contact phone number
- `created_at`: Record creation timestamp
- `updated_at`: Last update timestamp

### 2. Customers Table
**Purpose**: Customer relationship management and contact information

```sql
CREATE TABLE public.customers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  contact_person TEXT NOT NULL,
  title_position TEXT,
  email TEXT NOT NULL,
  phone TEXT,
  mobile TEXT,
  company TEXT NOT NULL,
  website TEXT,
  industry TEXT,
  business_type TEXT CHECK (business_type IN ('Individual', 'Company', 'Government', 'NGO')),
  company_size TEXT CHECK (company_size IN ('1-10', '11-50', '51-200', '201-1000', '1000+')),
  annual_volume DECIMAL(15,2),
  tax_id TEXT,
  credit_limit DECIMAL(15,2),
  payment_terms INTEGER, -- Days
  currency_preference TEXT DEFAULT 'USD',
  
  -- Address Information
  country TEXT NOT NULL,
  city TEXT NOT NULL,
  address TEXT,
  postal_code TEXT,
  
  -- Shipping Information
  preferred_shipping_method TEXT,
  preferred_incoterms TEXT,
  shipping_instructions TEXT,
  
  -- Business Relationship
  account_manager TEXT,
  customer_since DATE,
  customer_tier TEXT DEFAULT 'Bronze' CHECK (customer_tier IN ('Bronze', 'Silver', 'Gold', 'Platinum')),
  
  -- Compliance and Certifications
  required_certificates TEXT[],
  compliance_requirements TEXT[],
  
  -- CRM Fields
  source TEXT DEFAULT 'Website' CHECK (source IN ('Website', 'Email', 'Phone', 'Social Media', 'Referral', 'Trade Show', 'Cold Call', 'Other')),
  status TEXT DEFAULT 'Active' CHECK (status IN ('Active', 'Inactive', 'Prospect', 'Churned')),
  tags TEXT[],
  notes TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Indexes:**
```sql
CREATE INDEX idx_customers_user_id ON customers(user_id);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_company ON customers(company);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_country ON customers(country);
```

### 3. Companies Table
**Purpose**: Company and organization management

```sql
CREATE TABLE public.companies (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  legal_name TEXT,
  industry TEXT,
  size TEXT CHECK (size IN ('Startup', 'Small', 'Medium', 'Large', 'Enterprise')),
  website TEXT,
  description TEXT,
  logo_url TEXT,
  
  -- Headquarters Information
  headquarters_country TEXT,
  headquarters_city TEXT,
  headquarters_address TEXT,
  
  -- Business Information
  founded_year INTEGER,
  annual_revenue DECIMAL(15,2),
  employee_count INTEGER,
  stock_symbol TEXT,
  
  -- Contact Information
  main_phone TEXT,
  main_email TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Deals Table
**Purpose**: Sales pipeline and deal management

```sql
CREATE TABLE public.deals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  value DECIMAL(15,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  stage TEXT DEFAULT 'Prospecting' CHECK (stage IN ('Prospecting', 'Qualification', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost')),
  probability INTEGER DEFAULT 0 CHECK (probability >= 0 AND probability <= 100),
  
  -- Relationships
  customer_id UUID REFERENCES customers(id),
  company_id UUID REFERENCES companies(id),
  assigned_to UUID REFERENCES auth.users(id),
  
  -- Timeline
  expected_close_date DATE,
  actual_close_date DATE,
  
  -- Additional Information
  source TEXT,
  competitors TEXT[],
  next_steps TEXT,
  loss_reason TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Leads Table
**Purpose**: Lead generation and qualification

```sql
CREATE TABLE public.leads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone TEXT,
  company TEXT,
  job_title TEXT,
  
  -- Lead Information
  source TEXT CHECK (source IN ('Website', 'Social Media', 'Email Campaign', 'Cold Call', 'Referral', 'Trade Show', 'Advertisement', 'Other')),
  status TEXT DEFAULT 'New' CHECK (status IN ('New', 'Contacted', 'Qualified', 'Unqualified', 'Converted', 'Lost')),
  score INTEGER DEFAULT 0 CHECK (score >= 0 AND score <= 100),
  
  -- Qualification Information
  budget DECIMAL(15,2),
  timeline TEXT,
  authority TEXT,
  need TEXT,
  
  -- Conversion
  converted_to_customer_id UUID REFERENCES customers(id),
  converted_at TIMESTAMP WITH TIME ZONE,
  
  -- Additional Information
  notes TEXT,
  tags TEXT[],
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. Opportunities Table
**Purpose**: Sales opportunity tracking

```sql
CREATE TABLE public.opportunities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  value DECIMAL(15,2) NOT NULL,
  probability INTEGER DEFAULT 0 CHECK (probability >= 0 AND probability <= 100),
  stage TEXT DEFAULT 'Identification' CHECK (stage IN ('Identification', 'Qualification', 'Analysis', 'Proposal', 'Negotiation', 'Closed Won', 'Closed Lost')),
  
  -- Relationships
  customer_id UUID REFERENCES customers(id),
  lead_id UUID REFERENCES leads(id),
  assigned_to UUID REFERENCES auth.users(id),
  
  -- Timeline
  expected_close_date DATE,
  actual_close_date DATE,
  
  -- Additional Information
  products_services TEXT[],
  competitors TEXT[],
  decision_criteria TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 7. Tasks Table
**Purpose**: Task management and follow-ups

```sql
CREATE TABLE public.tasks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  priority TEXT DEFAULT 'Medium' CHECK (priority IN ('Low', 'Medium', 'High', 'Urgent')),
  status TEXT DEFAULT 'Open' CHECK (status IN ('Open', 'In Progress', 'Completed', 'Cancelled')),
  
  -- Assignment
  assigned_to UUID REFERENCES auth.users(id),
  
  -- Timeline
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  
  -- Relationships (polymorphic)
  related_to_type TEXT CHECK (related_to_type IN ('customer', 'deal', 'lead', 'opportunity', 'company')),
  related_to_id UUID,
  
  -- Additional Information
  tags TEXT[],
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 8. Proposals Table
**Purpose**: Client proposals and quotes

```sql
CREATE TABLE public.proposals (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'Draft' CHECK (status IN ('Draft', 'Sent', 'Viewed', 'Accepted', 'Rejected', 'Expired')),
  
  -- Financial Information
  total_amount DECIMAL(15,2),
  currency TEXT DEFAULT 'USD',
  valid_until DATE,
  
  -- Relationships
  customer_id UUID REFERENCES customers(id),
  deal_id UUID REFERENCES deals(id),
  
  -- Document Information
  document_url TEXT,
  template_used TEXT,
  
  -- Tracking
  sent_at TIMESTAMP WITH TIME ZONE,
  viewed_at TIMESTAMP WITH TIME ZONE,
  responded_at TIMESTAMP WITH TIME ZONE,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Row Level Security (RLS) Policies

### Customer Policies
```sql
-- Enable RLS
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- Users can only see their own customers
CREATE POLICY "Users can view own customers" ON customers
  FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own customers
CREATE POLICY "Users can insert own customers" ON customers
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own customers
CREATE POLICY "Users can update own customers" ON customers
  FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own customers
CREATE POLICY "Users can delete own customers" ON customers
  FOR DELETE USING (auth.uid() = user_id);

-- Admin users can see all customers
CREATE POLICY "Admins can view all customers" ON customers
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

### Deal Policies
```sql
ALTER TABLE deals ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own deals" ON deals
  FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Admins can manage all deals" ON deals
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM user_profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

## Database Functions

### Update Timestamp Function
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';
```

### Apply to All Tables
```sql
CREATE TRIGGER update_customers_updated_at 
  BEFORE UPDATE ON customers 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deals_updated_at 
  BEFORE UPDATE ON deals 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Apply to all other tables...
```

### Customer Analytics Function
```sql
CREATE OR REPLACE FUNCTION get_customer_analytics(
  user_uuid UUID,
  start_date DATE DEFAULT NULL,
  end_date DATE DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_customers', COUNT(*),
    'active_customers', COUNT(*) FILTER (WHERE status = 'Active'),
    'new_customers', COUNT(*) FILTER (WHERE created_at >= COALESCE(start_date, CURRENT_DATE - INTERVAL '30 days')),
    'by_country', json_agg(DISTINCT country),
    'by_source', json_object_agg(source, COUNT(*))
  )
  INTO result
  FROM customers
  WHERE user_id = user_uuid
    AND (start_date IS NULL OR created_at >= start_date)
    AND (end_date IS NULL OR created_at <= end_date);
    
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Views

### Customer Summary View
```sql
CREATE VIEW customer_summary AS
SELECT 
  c.id,
  c.contact_person,
  c.company,
  c.email,
  c.country,
  c.status,
  c.customer_tier,
  COUNT(d.id) as deal_count,
  COALESCE(SUM(d.value), 0) as total_deal_value,
  c.created_at
FROM customers c
LEFT JOIN deals d ON c.id = d.customer_id
GROUP BY c.id, c.contact_person, c.company, c.email, c.country, c.status, c.customer_tier, c.created_at;
```

### Deal Pipeline View
```sql
CREATE VIEW deal_pipeline AS
SELECT 
  stage,
  COUNT(*) as deal_count,
  SUM(value) as total_value,
  AVG(probability) as avg_probability,
  user_id
FROM deals
WHERE stage NOT IN ('Closed Won', 'Closed Lost')
GROUP BY stage, user_id;
```

## Indexes for Performance

### Customer Indexes
```sql
CREATE INDEX idx_customers_user_status ON customers(user_id, status);
CREATE INDEX idx_customers_created_at ON customers(created_at);
CREATE INDEX idx_customers_company_name ON customers USING gin(to_tsvector('english', company));
```

### Deal Indexes
```sql
CREATE INDEX idx_deals_user_stage ON deals(user_id, stage);
CREATE INDEX idx_deals_customer_id ON deals(customer_id);
CREATE INDEX idx_deals_expected_close ON deals(expected_close_date);
CREATE INDEX idx_deals_value ON deals(value);
```

## Data Migration Scripts

### Initial Data Setup
```sql
-- Insert default user roles
INSERT INTO user_profiles (id, role) 
SELECT id, 'user' FROM auth.users 
WHERE id NOT IN (SELECT id FROM user_profiles);

-- Set admin role for specific users
UPDATE user_profiles 
SET role = 'admin' 
WHERE id IN ('admin-user-uuid-here');
```

### Data Cleanup
```sql
-- Remove orphaned records
DELETE FROM deals WHERE customer_id NOT IN (SELECT id FROM customers);
DELETE FROM tasks WHERE related_to_type = 'customer' AND related_to_id NOT IN (SELECT id FROM customers);
```

## Backup and Maintenance

### Automated Backups
- Daily automated backups via Supabase
- Point-in-time recovery available
- Cross-region backup replication

### Maintenance Tasks
```sql
-- Analyze table statistics
ANALYZE customers;
ANALYZE deals;
ANALYZE leads;

-- Vacuum tables
VACUUM ANALYZE customers;
VACUUM ANALYZE deals;
```

## Performance Monitoring

### Query Performance
```sql
-- Monitor slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;
```

### Table Statistics
```sql
-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

This database schema documentation provides a comprehensive overview of the Nawras CRM database structure, including tables, relationships, security policies, and performance optimizations.
