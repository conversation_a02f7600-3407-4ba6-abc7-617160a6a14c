"use client"

import { useState } from "react"
import { useAuth } from "@/components/auth-provider"
import { Button } from "@/components/ui/button"
import { AlertTriangle, RefreshCw, CheckCircle, XCircle } from "lucide-react"

interface AuthResetProps {
  title?: string
  description?: string
  showDiagnostics?: boolean
  onResetComplete?: () => void
}

export function AuthReset({ 
  title = "Authentication Issue Detected",
  description = "The authentication system appears to be stuck. You can reset it to resolve the issue.",
  showDiagnostics = true,
  onResetComplete
}: AuthResetProps) {
  const { user, loading, error, resetAuthentication } = useAuth()
  const [isResetting, setIsResetting] = useState(false)
  const [resetStatus, setResetStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [resetMessage, setResetMessage] = useState('')

  const handleReset = async () => {
    try {
      setIsResetting(true)
      setResetStatus('idle')
      setResetMessage('')
      
      console.log('🔄 User initiated authentication reset')
      
      await resetAuthentication()
      
      setResetStatus('success')
      setResetMessage('Authentication reset completed successfully!')
      
      // Call the completion callback if provided
      if (onResetComplete) {
        setTimeout(() => {
          onResetComplete()
        }, 2000)
      }
      
    } catch (error) {
      console.error('❌ Authentication reset failed:', error)
      setResetStatus('error')
      setResetMessage('Authentication reset failed. Please refresh the page manually.')
    } finally {
      setIsResetting(false)
    }
  }

  const handlePageRefresh = () => {
    window.location.reload()
  }

  const handleReturnToDashboard = () => {
    window.location.href = '/dashboard'
  }

  return (
    <div className="flex items-center justify-center min-h-[400px] p-6">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg border border-gray-200 p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-orange-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600 text-sm">{description}</p>
        </div>

        {/* Diagnostics */}
        {showDiagnostics && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="font-medium text-gray-900 mb-3">Current Status:</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Authentication Loading:</span>
                <span className={loading ? 'text-orange-600 font-medium' : 'text-green-600'}>
                  {loading ? 'Yes (Stuck)' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">User Present:</span>
                <span className={user ? 'text-green-600' : 'text-gray-500'}>
                  {user ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Error Present:</span>
                <span className={error ? 'text-red-600' : 'text-green-600'}>
                  {error ? 'Yes' : 'No'}
                </span>
              </div>
              {error && (
                <div className="mt-2 p-2 bg-red-50 rounded text-red-700 text-xs">
                  {error}
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">Page:</span>
                <span className="text-gray-900 font-mono text-xs">
                  {typeof window !== 'undefined' ? window.location.pathname : 'Unknown'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Time:</span>
                <span className="text-gray-900 text-xs">
                  {new Date().toLocaleTimeString()}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Reset Status */}
        {resetStatus !== 'idle' && (
          <div className={`rounded-lg p-4 mb-4 ${
            resetStatus === 'success' 
              ? 'bg-green-50 border border-green-200' 
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              {resetStatus === 'success' ? (
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600 mr-2" />
              )}
              <span className={`text-sm font-medium ${
                resetStatus === 'success' ? 'text-green-800' : 'text-red-800'
              }`}>
                {resetMessage}
              </span>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button 
            onClick={handleReset} 
            disabled={isResetting}
            className="w-full bg-orange-600 hover:bg-orange-700 text-white"
          >
            {isResetting ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Resetting Authentication...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Reset Authentication
              </>
            )}
          </Button>
          
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="outline" 
              onClick={handlePageRefresh}
              disabled={isResetting}
              className="text-sm"
            >
              Refresh Page
            </Button>
            <Button 
              variant="outline" 
              onClick={handleReturnToDashboard}
              disabled={isResetting}
              className="text-sm"
            >
              Return to Dashboard
            </Button>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            If the issue persists after resetting, please contact support or try logging out and back in.
          </p>
        </div>
      </div>
    </div>
  )
}
