export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      customers: {
        Row: {
          id: string
          user_id: string
          contact_person: string
          title_position: string | null
          email: string
          phone: string | null
          mobile: string | null
          company: string
          website: string | null
          industry: string | null
          business_type: 'Individual' | 'Company' | 'Government' | 'NGO' | null
          company_size: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null
          annual_volume: number | null
          tax_id: string | null
          credit_limit: number | null
          payment_terms: number | null
          currency_preference: string | null
          country: string
          city: string
          address: string | null
          postal_code: string | null
          preferred_shipping_method: string | null
          preferred_incoterms: string | null
          shipping_instructions: string | null
          account_manager: string | null
          customer_since: string | null
          customer_tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | null
          required_certificates: string[] | null
          compliance_requirements: string[] | null
          source: 'Website' | 'Email' | 'Phone' | 'Social Media' | 'Referral' | 'Trade Show' | 'Cold Call' | 'Other' | null
          status: 'Active' | 'Inactive' | 'Prospect' | 'Churned' | null
          tags: string[] | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          contact_person: string
          title_position?: string | null
          email: string
          phone?: string | null
          mobile?: string | null
          company: string
          website?: string | null
          industry?: string | null
          business_type?: 'Individual' | 'Company' | 'Government' | 'NGO' | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null
          annual_volume?: number | null
          tax_id?: string | null
          credit_limit?: number | null
          payment_terms?: number | null
          currency_preference?: string | null
          country: string
          city: string
          address?: string | null
          postal_code?: string | null
          preferred_shipping_method?: string | null
          preferred_incoterms?: string | null
          shipping_instructions?: string | null
          account_manager?: string | null
          customer_since?: string | null
          customer_tier?: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | null
          required_certificates?: string[] | null
          compliance_requirements?: string[] | null
          source?: 'Website' | 'Email' | 'Phone' | 'Social Media' | 'Referral' | 'Trade Show' | 'Cold Call' | 'Other' | null
          status?: 'Active' | 'Inactive' | 'Prospect' | 'Churned' | null
          tags?: string[] | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          contact_person?: string
          title_position?: string | null
          email?: string
          phone?: string | null
          mobile?: string | null
          company?: string
          website?: string | null
          industry?: string | null
          business_type?: 'Individual' | 'Company' | 'Government' | 'NGO' | null
          company_size?: '1-10' | '11-50' | '51-200' | '201-1000' | '1000+' | null
          annual_volume?: number | null
          tax_id?: string | null
          credit_limit?: number | null
          payment_terms?: number | null
          currency_preference?: string | null
          country?: string
          city?: string
          address?: string | null
          postal_code?: string | null
          preferred_shipping_method?: string | null
          preferred_incoterms?: string | null
          shipping_instructions?: string | null
          account_manager?: string | null
          customer_since?: string | null
          customer_tier?: 'Bronze' | 'Silver' | 'Gold' | 'Platinum' | null
          required_certificates?: string[] | null
          compliance_requirements?: string[] | null
          source?: 'Website' | 'Email' | 'Phone' | 'Social Media' | 'Referral' | 'Trade Show' | 'Cold Call' | 'Other' | null
          status?: 'Active' | 'Inactive' | 'Prospect' | 'Churned' | null
          tags?: string[] | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      companies: {
        Row: {
          id: string
          user_id: string
          name: string
          legal_name: string | null
          industry: string | null
          size: 'Startup' | 'Small' | 'Medium' | 'Large' | 'Enterprise' | null
          website: string | null
          description: string | null
          logo_url: string | null
          headquarters_country: string | null
          headquarters_city: string | null
          headquarters_address: string | null
          founded_year: number | null
          annual_revenue: number | null
          employee_count: number | null
          stock_symbol: string | null
          main_phone: string | null
          main_email: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          legal_name?: string | null
          industry?: string | null
          size?: 'Startup' | 'Small' | 'Medium' | 'Large' | 'Enterprise' | null
          website?: string | null
          description?: string | null
          logo_url?: string | null
          headquarters_country?: string | null
          headquarters_city?: string | null
          headquarters_address?: string | null
          founded_year?: number | null
          annual_revenue?: number | null
          employee_count?: number | null
          stock_symbol?: string | null
          main_phone?: string | null
          main_email?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          legal_name?: string | null
          industry?: string | null
          size?: 'Startup' | 'Small' | 'Medium' | 'Large' | 'Enterprise' | null
          website?: string | null
          description?: string | null
          logo_url?: string | null
          headquarters_country?: string | null
          headquarters_city?: string | null
          headquarters_address?: string | null
          founded_year?: number | null
          annual_revenue?: number | null
          employee_count?: number | null
          stock_symbol?: string | null
          main_phone?: string | null
          main_email?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      deals: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          value: number
          currency: string | null
          stage: 'Prospecting' | 'Qualification' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          probability: number | null
          customer_id: string | null
          company_id: string | null
          assigned_to: string | null
          expected_close_date: string | null
          actual_close_date: string | null
          source: string | null
          competitors: string[] | null
          next_steps: string | null
          loss_reason: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          value: number
          currency?: string | null
          stage?: 'Prospecting' | 'Qualification' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          probability?: number | null
          customer_id?: string | null
          company_id?: string | null
          assigned_to?: string | null
          expected_close_date?: string | null
          actual_close_date?: string | null
          source?: string | null
          competitors?: string[] | null
          next_steps?: string | null
          loss_reason?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          value?: number
          currency?: string | null
          stage?: 'Prospecting' | 'Qualification' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          probability?: number | null
          customer_id?: string | null
          company_id?: string | null
          assigned_to?: string | null
          expected_close_date?: string | null
          actual_close_date?: string | null
          source?: string | null
          competitors?: string[] | null
          next_steps?: string | null
          loss_reason?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "deals_customer_id_fkey"
            columns: ["customer_id"]
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "deals_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          }
        ]
      }
      leads: {
        Row: {
          id: string
          user_id: string
          name: string
          email: string
          phone: string | null
          company: string | null
          job_title: string | null
          source: 'Website' | 'Social Media' | 'Email Campaign' | 'Cold Call' | 'Referral' | 'Trade Show' | 'Advertisement' | 'Other' | null
          status: 'New' | 'Contacted' | 'Qualified' | 'Unqualified' | 'Converted' | 'Lost' | null
          score: number | null
          budget: number | null
          timeline: string | null
          authority: string | null
          need: string | null
          converted_to_customer_id: string | null
          converted_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          email: string
          phone?: string | null
          company?: string | null
          job_title?: string | null
          source?: 'Website' | 'Social Media' | 'Email Campaign' | 'Cold Call' | 'Referral' | 'Trade Show' | 'Advertisement' | 'Other' | null
          status?: 'New' | 'Contacted' | 'Qualified' | 'Unqualified' | 'Converted' | 'Lost' | null
          score?: number | null
          budget?: number | null
          timeline?: string | null
          authority?: string | null
          need?: string | null
          converted_to_customer_id?: string | null
          converted_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          email?: string
          phone?: string | null
          company?: string | null
          job_title?: string | null
          source?: 'Website' | 'Social Media' | 'Email Campaign' | 'Cold Call' | 'Referral' | 'Trade Show' | 'Advertisement' | 'Other' | null
          status?: 'New' | 'Contacted' | 'Qualified' | 'Unqualified' | 'Converted' | 'Lost' | null
          score?: number | null
          budget?: number | null
          timeline?: string | null
          authority?: string | null
          need?: string | null
          converted_to_customer_id?: string | null
          converted_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "leads_converted_to_customer_id_fkey"
            columns: ["converted_to_customer_id"]
            referencedRelation: "customers"
            referencedColumns: ["id"]
          }
        ]
      }
      opportunities: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          value: number
          probability: number | null
          stage: 'Identification' | 'Qualification' | 'Analysis' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          customer_id: string | null
          lead_id: string | null
          assigned_to: string | null
          expected_close_date: string | null
          actual_close_date: string | null
          products_services: string[] | null
          competitors: string[] | null
          decision_criteria: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          value: number
          probability?: number | null
          stage?: 'Identification' | 'Qualification' | 'Analysis' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          customer_id?: string | null
          lead_id?: string | null
          assigned_to?: string | null
          expected_close_date?: string | null
          actual_close_date?: string | null
          products_services?: string[] | null
          competitors?: string[] | null
          decision_criteria?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          value?: number
          probability?: number | null
          stage?: 'Identification' | 'Qualification' | 'Analysis' | 'Proposal' | 'Negotiation' | 'Closed Won' | 'Closed Lost' | null
          customer_id?: string | null
          lead_id?: string | null
          assigned_to?: string | null
          expected_close_date?: string | null
          actual_close_date?: string | null
          products_services?: string[] | null
          competitors?: string[] | null
          decision_criteria?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "opportunities_customer_id_fkey"
            columns: ["customer_id"]
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "opportunities_lead_id_fkey"
            columns: ["lead_id"]
            referencedRelation: "leads"
            referencedColumns: ["id"]
          }
        ]
      }
      tasks: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          priority: 'Low' | 'Medium' | 'High' | 'Urgent' | null
          status: 'Open' | 'In Progress' | 'Completed' | 'Cancelled' | null
          assigned_to: string | null
          due_date: string | null
          completed_at: string | null
          related_to_type: 'customer' | 'deal' | 'lead' | 'opportunity' | 'company' | null
          related_to_id: string | null
          tags: string[] | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          priority?: 'Low' | 'Medium' | 'High' | 'Urgent' | null
          status?: 'Open' | 'In Progress' | 'Completed' | 'Cancelled' | null
          assigned_to?: string | null
          due_date?: string | null
          completed_at?: string | null
          related_to_type?: 'customer' | 'deal' | 'lead' | 'opportunity' | 'company' | null
          related_to_id?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          priority?: 'Low' | 'Medium' | 'High' | 'Urgent' | null
          status?: 'Open' | 'In Progress' | 'Completed' | 'Cancelled' | null
          assigned_to?: string | null
          due_date?: string | null
          completed_at?: string | null
          related_to_type?: 'customer' | 'deal' | 'lead' | 'opportunity' | 'company' | null
          related_to_id?: string | null
          tags?: string[] | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      proposals: {
        Row: {
          id: string
          user_id: string
          title: string
          description: string | null
          status: 'Draft' | 'Sent' | 'Viewed' | 'Accepted' | 'Rejected' | 'Expired' | null
          total_amount: number | null
          currency: string | null
          valid_until: string | null
          customer_id: string | null
          deal_id: string | null
          document_url: string | null
          template_used: string | null
          sent_at: string | null
          viewed_at: string | null
          responded_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          description?: string | null
          status?: 'Draft' | 'Sent' | 'Viewed' | 'Accepted' | 'Rejected' | 'Expired' | null
          total_amount?: number | null
          currency?: string | null
          valid_until?: string | null
          customer_id?: string | null
          deal_id?: string | null
          document_url?: string | null
          template_used?: string | null
          sent_at?: string | null
          viewed_at?: string | null
          responded_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          description?: string | null
          status?: 'Draft' | 'Sent' | 'Viewed' | 'Accepted' | 'Rejected' | 'Expired' | null
          total_amount?: number | null
          currency?: string | null
          valid_until?: string | null
          customer_id?: string | null
          deal_id?: string | null
          document_url?: string | null
          template_used?: string | null
          sent_at?: string | null
          viewed_at?: string | null
          responded_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "proposals_customer_id_fkey"
            columns: ["customer_id"]
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "proposals_deal_id_fkey"
            columns: ["deal_id"]
            referencedRelation: "deals"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role: 'admin' | 'manager' | 'user' | null
          department: string | null
          phone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          role?: 'admin' | 'manager' | 'user' | null
          department?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role?: 'admin' | 'manager' | 'user' | null
          department?: string | null
          phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
