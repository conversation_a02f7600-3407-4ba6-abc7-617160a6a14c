"use client"

import type React from "react"
import { useEffect, useState } from "react"
import { useRout<PERSON>, usePathname } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useLanguage } from "@/components/language-provider"
import { AuthErrorHandler } from "@/components/auth-error-handler"

// Breadcrumb mapping for different routes
const getBreadcrumbData = (pathname: string, t: (key: string) => string) => {
  const segments = pathname.split('/').filter(Boolean)

  if (pathname === '/dashboard') {
    return { title: t('Dashboard'), parent: null }
  }

  const routeMap: Record<string, { title: string; parent?: string }> = {
    'customers': { title: t('Customers'), parent: t('Dashboard') },
    'proposals': { title: t('Proposals'), parent: t('Dashboard') },
    'tasks': { title: t('Tasks'), parent: t('Dashboard') },
    'leads': { title: t('Leads'), parent: t('Dashboard') },
    'reports': { title: t('Reports'), parent: t('Dashboard') },
    'settings': { title: t('Settings'), parent: t('Dashboard') },
    'admin': { title: t('Admin Dashboard'), parent: t('Dashboard') },
    'users': { title: t('User Management'), parent: t('Admin Dashboard') },
  }

  const lastSegment = segments[segments.length - 1]
  return routeMap[lastSegment] || { title: t('Dashboard'), parent: null }
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, error, retryConnection } = useAuth()
  const { t } = useLanguage()
  const router = useRouter()
  const pathname = usePathname()
  const [isClient, setIsClient] = useState(false)
  const [authTimeout, setAuthTimeout] = useState(false)

  useEffect(() => {
    setIsClient(true)
    
    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.log("Authentication timeout triggered - proceeding without auth")
      setAuthTimeout(true)
    }, 120000) // 120 seconds timeout (2 minutes for better reliability)

    return () => clearTimeout(timeout)
  }, [])

  useEffect(() => {
    if (!loading && !user && isClient && !authTimeout && !error) {
      router.push("/login")
    }
  }, [user, loading, router, isClient, authTimeout, error])

  // Show enhanced error handler if there's an authentication error
  if (error) {
    return <AuthErrorHandler error={error} onRetry={retryConnection} />
  }

  // If authentication times out, show the page anyway (for development)
  if ((loading || !isClient) && !authTimeout) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-sm text-muted-foreground">{t("Loading...")}</p>
          <p className="text-xs text-muted-foreground">Connecting to authentication service...</p>
          <p className="text-xs text-orange-600">Will proceed automatically in {authTimeout ? '0' : '15'} seconds...</p>
        </div>
      </div>
    )
  }

  // If auth timed out, show a warning but continue
  if (authTimeout && !user) {
    console.warn("Authentication timeout - proceeding without auth for development")
  }

  // Only redirect if we're sure there's no user and auth has loaded
  if (!user && !loading && !authTimeout) {
    return null
  }

  const breadcrumbData = getBreadcrumbData(pathname, t)

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-gradient-to-r from-white via-purple-50 to-blue-50 backdrop-blur shadow-sm">
          <SidebarTrigger className="-ml-1 text-purple-600 hover:bg-purple-100" />
          <Separator orientation="vertical" className="mr-2 h-4 bg-purple-200" />
          <Breadcrumb>
            <BreadcrumbList>
              {breadcrumbData.parent && (
                <>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard" className="text-purple-600 hover:text-purple-800 font-medium">{breadcrumbData.parent}</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block text-purple-400" />
                </>
              )}
              <BreadcrumbItem>
                <BreadcrumbPage className="text-gray-800 font-semibold">{breadcrumbData.title}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          {authTimeout && (
            <div className="ml-auto">
              <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                Auth Timeout - Dev Mode
              </span>
            </div>
          )}
        </header>
        <div className="flex flex-1 flex-col overflow-auto bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50">{children}</div>
      </SidebarInset>
    </SidebarProvider>
  )
}