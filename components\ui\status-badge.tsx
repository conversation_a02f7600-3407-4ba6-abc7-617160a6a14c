import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  XCircle, 
  Play, 
  Pause,
  Target,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react"

interface StatusBadgeProps {
  status: string
  type?: "deal" | "task" | "lead" | "customer" | "priority" | "generic"
  size?: "sm" | "md" | "lg"
  showIcon?: boolean
  className?: string
}

export function StatusBadge({ 
  status, 
  type = "generic", 
  size = "md", 
  showIcon = true,
  className 
}: StatusBadgeProps) {
  const getStatusConfig = () => {
    const normalizedStatus = status.toLowerCase().replace(/[_\s]/g, '')

    // Deal statuses
    if (type === "deal") {
      switch (normalizedStatus) {
        case "prospecting":
          return {
            variant: "secondary" as const,
            icon: Target,
            color: "bg-blue-100 text-blue-800 border-blue-200",
            label: "Prospecting"
          }
        case "qualification":
          return {
            variant: "secondary" as const,
            icon: Play,
            color: "bg-purple-100 text-purple-800 border-purple-200",
            label: "Qualification"
          }
        case "proposal":
          return {
            variant: "secondary" as const,
            icon: Clock,
            color: "bg-yellow-100 text-yellow-800 border-yellow-200",
            label: "Proposal"
          }
        case "negotiation":
          return {
            variant: "secondary" as const,
            icon: TrendingUp,
            color: "bg-orange-100 text-orange-800 border-orange-200",
            label: "Negotiation"
          }
        case "closedwon":
        case "won":
          return {
            variant: "success" as const,
            icon: CheckCircle,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Closed Won"
          }
        case "closedlost":
        case "lost":
          return {
            variant: "destructive" as const,
            icon: XCircle,
            color: "bg-red-100 text-red-800 border-red-200",
            label: "Closed Lost"
          }
        default:
          return {
            variant: "outline" as const,
            icon: Minus,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: status
          }
      }
    }

    // Task statuses
    if (type === "task") {
      switch (normalizedStatus) {
        case "todo":
        case "pending":
          return {
            variant: "secondary" as const,
            icon: Clock,
            color: "bg-blue-100 text-blue-800 border-blue-200",
            label: "To Do"
          }
        case "inprogress":
        case "active":
          return {
            variant: "secondary" as const,
            icon: Play,
            color: "bg-yellow-100 text-yellow-800 border-yellow-200",
            label: "In Progress"
          }
        case "review":
          return {
            variant: "secondary" as const,
            icon: AlertCircle,
            color: "bg-purple-100 text-purple-800 border-purple-200",
            label: "Review"
          }
        case "done":
        case "completed":
          return {
            variant: "success" as const,
            icon: CheckCircle,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Done"
          }
        case "cancelled":
          return {
            variant: "destructive" as const,
            icon: XCircle,
            color: "bg-red-100 text-red-800 border-red-200",
            label: "Cancelled"
          }
        default:
          return {
            variant: "outline" as const,
            icon: Minus,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: status
          }
      }
    }

    // Priority statuses
    if (type === "priority") {
      switch (normalizedStatus) {
        case "critical":
        case "urgent":
          return {
            variant: "destructive" as const,
            icon: AlertCircle,
            color: "bg-red-100 text-red-800 border-red-200",
            label: "Critical"
          }
        case "high":
          return {
            variant: "secondary" as const,
            icon: TrendingUp,
            color: "bg-orange-100 text-orange-800 border-orange-200",
            label: "High"
          }
        case "medium":
        case "normal":
          return {
            variant: "secondary" as const,
            icon: Minus,
            color: "bg-yellow-100 text-yellow-800 border-yellow-200",
            label: "Medium"
          }
        case "low":
          return {
            variant: "secondary" as const,
            icon: TrendingDown,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Low"
          }
        default:
          return {
            variant: "outline" as const,
            icon: Minus,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: status
          }
      }
    }

    // Lead statuses
    if (type === "lead") {
      switch (normalizedStatus) {
        case "new":
          return {
            variant: "secondary" as const,
            icon: Target,
            color: "bg-blue-100 text-blue-800 border-blue-200",
            label: "New"
          }
        case "qualified":
          return {
            variant: "secondary" as const,
            icon: CheckCircle,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Qualified"
          }
        case "contacted":
          return {
            variant: "secondary" as const,
            icon: Play,
            color: "bg-purple-100 text-purple-800 border-purple-200",
            label: "Contacted"
          }
        case "converted":
          return {
            variant: "success" as const,
            icon: CheckCircle,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Converted"
          }
        case "lost":
          return {
            variant: "destructive" as const,
            icon: XCircle,
            color: "bg-red-100 text-red-800 border-red-200",
            label: "Lost"
          }
        default:
          return {
            variant: "outline" as const,
            icon: Minus,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: status
          }
      }
    }

    // Customer statuses
    if (type === "customer") {
      switch (normalizedStatus) {
        case "active":
          return {
            variant: "success" as const,
            icon: CheckCircle,
            color: "bg-green-100 text-green-800 border-green-200",
            label: "Active"
          }
        case "inactive":
          return {
            variant: "secondary" as const,
            icon: Pause,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: "Inactive"
          }
        case "prospect":
          return {
            variant: "secondary" as const,
            icon: Target,
            color: "bg-blue-100 text-blue-800 border-blue-200",
            label: "Prospect"
          }
        default:
          return {
            variant: "outline" as const,
            icon: Minus,
            color: "bg-gray-100 text-gray-800 border-gray-200",
            label: status
          }
      }
    }

    // Generic fallback
    return {
      variant: "outline" as const,
      icon: Minus,
      color: "bg-gray-100 text-gray-800 border-gray-200",
      label: status
    }
  }

  const config = getStatusConfig()
  const Icon = config.icon

  const sizeClasses = {
    sm: "text-xs px-2 py-1",
    md: "text-sm px-2.5 py-1",
    lg: "text-base px-3 py-1.5"
  }

  const iconSizes = {
    sm: "h-3 w-3",
    md: "h-3.5 w-3.5", 
    lg: "h-4 w-4"
  }

  return (
    <Badge
      variant={config.variant}
      className={cn(
        "inline-flex items-center gap-1.5 font-medium border transition-colors",
        config.color,
        sizeClasses[size],
        className
      )}
    >
      {showIcon && <Icon className={iconSizes[size]} />}
      {config.label}
    </Badge>
  )
}
