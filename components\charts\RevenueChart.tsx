'use client'

import React from 'react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from '@/components/ui/badge'
import { useRevenueData } from '@/hooks/useChartData'
import { formatters, CHART_COLORS } from '@/components/charts/chart-config'
import { ChartProps } from '@/components/charts/types'
import { TrendingUp, TrendingDown, DollarSign } from 'lucide-react'

interface RevenueChartProps extends Omit<ChartProps, 'data'> {
  showTarget?: boolean
  chartType?: 'line' | 'area'
}

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
        <p className="font-medium text-foreground">{formatters.monthYear(label + '-01')}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: <span className="font-medium">{formatters.currency(entry.value)}</span>
          </p>
        ))}
      </div>
    )
  }
  return null
}

export function RevenueChart({ 
  config,
  loading: externalLoading,
  error,
  className = "",
  title = "Revenue Trends",
  subtitle = "Monthly revenue performance",
  showTarget = true,
  chartType = 'area'
}: RevenueChartProps) {
  const { data, loading: dataLoading } = useRevenueData()
  const isLoading = externalLoading || dataLoading

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <Skeleton className="h-[300px] w-full" />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>Error loading revenue data: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-[300px] text-muted-foreground">
            <p>No revenue data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Calculate growth metrics
  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0)
  const avgRevenue = totalRevenue / data.length
  const lastPeriodRevenue = data[data.length - 1]?.revenue || 0
  const previousPeriodRevenue = data[data.length - 2]?.revenue || 0
  const growthRate = previousPeriodRevenue > 0 
    ? ((lastPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100 
    : 0

  const ChartComponent = chartType === 'area' ? AreaChart : LineChart

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-primary" />
            {title}
          </div>
          <Badge variant={growthRate >= 0 ? "success" : "destructive"} className="flex items-center gap-1">
            {growthRate >= 0 ? <TrendingUp className="h-3 w-3" /> : <TrendingDown className="h-3 w-3" />}
            {formatters.percentage(Math.abs(growthRate))}
          </Badge>
        </CardTitle>
        {subtitle && <p className="text-sm text-muted-foreground">{subtitle}</p>}
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={config?.height || 300}>
          <ChartComponent
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
            <XAxis 
              dataKey="period" 
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatters.monthYear(value + '-01')}
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              tickFormatter={formatters.compactNumber}
            />
            <Tooltip content={<CustomTooltip />} />
            
            {chartType === 'area' ? (
              <>
                <defs>
                  <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={CHART_COLORS.primary} stopOpacity={0.3}/>
                    <stop offset="95%" stopColor={CHART_COLORS.primary} stopOpacity={0}/>
                  </linearGradient>
                  {showTarget && (
                    <linearGradient id="targetGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={CHART_COLORS.secondary} stopOpacity={0.2}/>
                      <stop offset="95%" stopColor={CHART_COLORS.secondary} stopOpacity={0}/>
                    </linearGradient>
                  )}
                </defs>
                <Area
                  type="monotone"
                  dataKey="revenue"
                  stroke={CHART_COLORS.primary}
                  strokeWidth={2}
                  fill="url(#revenueGradient)"
                  name="Revenue"
                />
                {showTarget && (
                  <Area
                    type="monotone"
                    dataKey="target"
                    stroke={CHART_COLORS.secondary}
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    fill="url(#targetGradient)"
                    name="Target"
                  />
                )}
              </>
            ) : (
              <>
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke={CHART_COLORS.primary}
                  strokeWidth={2}
                  dot={{ fill: CHART_COLORS.primary, strokeWidth: 2, r: 4 }}
                  name="Revenue"
                />
                {showTarget && (
                  <Line
                    type="monotone"
                    dataKey="target"
                    stroke={CHART_COLORS.secondary}
                    strokeWidth={1}
                    strokeDasharray="5 5"
                    dot={{ fill: CHART_COLORS.secondary, strokeWidth: 1, r: 3 }}
                    name="Target"
                  />
                )}
              </>
            )}
          </ChartComponent>
        </ResponsiveContainer>
        
        {/* Summary Stats */}
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <p className="text-muted-foreground">Total Revenue</p>
            <p className="font-semibold text-lg">{formatters.currency(totalRevenue)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Avg Monthly</p>
            <p className="font-semibold text-lg">{formatters.currency(avgRevenue)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Last Period</p>
            <p className="font-semibold text-lg">{formatters.currency(lastPeriodRevenue)}</p>
          </div>
          <div className="text-center">
            <p className="text-muted-foreground">Growth Rate</p>
            <p className={`font-semibold text-lg ${growthRate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {growthRate >= 0 ? '+' : ''}{formatters.percentage(growthRate)}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
