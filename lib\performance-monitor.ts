/**
 * Performance Monitoring Utility
 * Tracks and reports performance metrics for the CRM application
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  type: 'timing' | 'counter' | 'gauge'
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric[]> = new Map()
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    // Observe Core Web Vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        // Largest Contentful Paint (LCP)
        const lcpObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('LCP', entry.startTime, 'timing')
          }
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // First Input Delay (FID)
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric('FID', (entry as any).processingStart - entry.startTime, 'timing')
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)

        // Cumulative Layout Shift (CLS)
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.recordMetric('CLS', clsValue, 'gauge')
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)

      } catch (error) {
        console.warn('Performance monitoring not supported:', error)
      }
    }
  }

  recordMetric(name: string, value: number, type: 'timing' | 'counter' | 'gauge' = 'timing') {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type
    }

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const metrics = this.metrics.get(name)!
    metrics.push(metric)

    // Keep only last 100 metrics per type
    if (metrics.length > 100) {
      metrics.shift()
    }

    // Log performance warnings
    this.checkThresholds(name, value, type)
  }

  private checkThresholds(name: string, value: number, type: string) {
    const thresholds = {
      'LCP': 2500, // 2.5 seconds
      'FID': 100,  // 100ms
      'CLS': 0.1,  // 0.1
      'page-load': 3000, // 3 seconds
      'query-time': 1000, // 1 second
      'render-time': 16   // 16ms (60fps)
    }

    const threshold = thresholds[name as keyof typeof thresholds]
    if (threshold && value > threshold) {
      console.warn(`⚠️ Performance threshold exceeded for ${name}: ${value} > ${threshold}`)
    }
  }

  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.get(name) || []
    }

    const allMetrics: PerformanceMetric[] = []
    Array.from(this.metrics.values()).forEach(metrics => {
      allMetrics.push(...metrics)
    })
    return allMetrics.sort((a, b) => b.timestamp - a.timestamp)
  }

  getAverageMetric(name: string): number {
    const metrics = this.metrics.get(name) || []
    if (metrics.length === 0) return 0

    const sum = metrics.reduce((acc, metric) => acc + metric.value, 0)
    return sum / metrics.length
  }

  clearMetrics(name?: string) {
    if (name) {
      this.metrics.delete(name)
    } else {
      this.metrics.clear()
    }
  }

  generateReport(): string {
    const report = ['📊 Performance Report', '='.repeat(50)]
    
    Array.from(this.metrics.entries()).forEach(([name, metrics]) => {
      if (metrics.length === 0) return

      const latest = metrics[metrics.length - 1]
      const average = this.getAverageMetric(name)
      const min = Math.min(...metrics.map(m => m.value))
      const max = Math.max(...metrics.map(m => m.value))

      report.push(`\n${name}:`)
      report.push(`  Latest: ${latest.value.toFixed(2)}${latest.type === 'timing' ? 'ms' : ''}`)
      report.push(`  Average: ${average.toFixed(2)}${latest.type === 'timing' ? 'ms' : ''}`)
      report.push(`  Min: ${min.toFixed(2)}${latest.type === 'timing' ? 'ms' : ''}`)
      report.push(`  Max: ${max.toFixed(2)}${latest.type === 'timing' ? 'ms' : ''}`)
      report.push(`  Samples: ${metrics.length}`)
    })

    return report.join('\n')
  }

  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Utility functions
export function measureTime<T>(name: string, fn: () => T): T {
  const start = performance.now()
  const result = fn()
  const end = performance.now()
  performanceMonitor.recordMetric(name, end - start, 'timing')
  return result
}

export async function measureAsyncTime<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  performanceMonitor.recordMetric(name, end - start, 'timing')
  return result
}

export function trackPageLoad(pageName: string) {
  if (typeof window !== 'undefined') {
    window.addEventListener('load', () => {
      const loadTime = performance.now()
      performanceMonitor.recordMetric(`page-load-${pageName}`, loadTime, 'timing')
    })
  }
}

export function trackComponentRender(componentName: string, renderTime: number) {
  performanceMonitor.recordMetric(`render-${componentName}`, renderTime, 'timing')
}
