"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { CustomerFormData, FormErrors } from "@/app/types/customer"
import { 
  User, Building2, Mail, Phone, MapPin, Globe, 
  CreditCard, Truck, Star, AlertCircle, CheckCircle,
  ChevronRight, ChevronDown, Save, X
} from "lucide-react"
import { cn } from "@/lib/utils"

interface EnhancedCustomerFormV2Props {
  initialData?: Partial<CustomerFormData>
  onSubmit: (data: CustomerFormData) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

interface FormSection {
  id: string
  title: string
  icon: React.ComponentType<{ className?: string }>
  required: boolean
  completed: boolean
  fields: (keyof CustomerFormData)[]
}

export function EnhancedCustomerFormV2({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false
}: EnhancedCustomerFormV2Props) {
  // Component lifecycle logging
  console.log(`🚀 [COMPONENT-LIFECYCLE] EnhancedCustomerFormV2 rendering at:`, new Date().toISOString())
  console.log(`📋 [COMPONENT-LIFECYCLE] Props received:`, {
    hasInitialData: !!initialData,
    isLoading,
    onSubmit: typeof onSubmit,
    onCancel: typeof onCancel
  })

  const [formData, setFormData] = useState<CustomerFormData>({
    contact_person: initialData?.contact_person || "",
    title_position: initialData?.title_position || "",
    email: initialData?.email || "",
    phone: initialData?.phone || "",
    mobile: initialData?.mobile || "",
    company: initialData?.company || "",
    website: initialData?.website || "",
    city: initialData?.city || "",
    country: initialData?.country || "Jordan",
    business_type: initialData?.business_type || undefined,
    industry: initialData?.industry || "",
    annual_volume: initialData?.annual_volume || undefined,
    company_size: initialData?.company_size || undefined,
    tax_id: initialData?.tax_id || "",
    credit_limit: initialData?.credit_limit || undefined,
    payment_terms: initialData?.payment_terms || undefined,
    currency_preference: initialData?.currency_preference || "USD",
    preferred_shipping_method: initialData?.preferred_shipping_method || undefined,
    preferred_incoterms: initialData?.preferred_incoterms || undefined,
    shipping_instructions: initialData?.shipping_instructions || "",
    account_manager: initialData?.account_manager || "",
    customer_since: initialData?.customer_since || "",
    customer_tier: initialData?.customer_tier || "Bronze",
    tags: initialData?.tags || [],
    required_certificates: initialData?.required_certificates || [],
    compliance_requirements: initialData?.compliance_requirements || [],
    source: initialData?.source || "Website",
    status: initialData?.status || "Active",
    notes: initialData?.notes || "",
  })

  const [errors, setErrors] = useState<FormErrors>({})
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(["contact"]))
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Component mounting/unmounting tracking
  useEffect(() => {
    console.log(`🎯 [COMPONENT-LIFECYCLE] EnhancedCustomerFormV2 MOUNTED at:`, new Date().toISOString())
    console.log(`📊 [COMPONENT-LIFECYCLE] Initial form data:`, formData)

    return () => {
      console.log(`🔚 [COMPONENT-LIFECYCLE] EnhancedCustomerFormV2 UNMOUNTING at:`, new Date().toISOString())
    }
  }, []) // Empty dependency array = mount/unmount only

  // Test function for debugging
  const testHandleInputChange = () => {
    console.log(`🧪 [DEBUG-TEST] Testing handleInputChange programmatically`)
    handleInputChange("contact_person", "TEST_PROGRAMMATIC_INPUT")
  }

  // Define form sections with progressive disclosure
  const formSections: FormSection[] = [
    {
      id: "contact",
      title: "Contact Information",
      icon: User,
      required: true,
      completed: false,
      fields: ["contact_person", "title_position", "email", "phone", "mobile"]
    },
    {
      id: "company",
      title: "Company Details",
      icon: Building2,
      required: true,
      completed: false,
      fields: ["company", "website", "city", "country", "industry"]
    },
    {
      id: "business",
      title: "Business Information",
      icon: Star,
      required: false,
      completed: false,
      fields: ["business_type", "annual_volume", "company_size", "customer_tier"]
    },
    {
      id: "financial",
      title: "Financial Details",
      icon: CreditCard,
      required: false,
      completed: false,
      fields: ["tax_id", "credit_limit", "payment_terms", "currency_preference"]
    },
    {
      id: "shipping",
      title: "Shipping Preferences",
      icon: Truck,
      required: false,
      completed: false,
      fields: ["preferred_shipping_method", "preferred_incoterms", "shipping_instructions"]
    }
  ]

  // Calculate completion status for each section
  const getSectionCompletion = (section: FormSection) => {
    if (section.required) {
      // For required sections, only check the actually required fields
      const requiredFieldsInSection = {
        contact: ["contact_person", "email"],
        company: ["company", "city", "country"]
      }

      const fieldsToCheck = requiredFieldsInSection[section.id as keyof typeof requiredFieldsInSection] || section.fields

      return fieldsToCheck.every(field => {
        const value = formData[field as keyof CustomerFormData]
        return value !== "" && value !== undefined && value !== null
      })
    }

    // For optional sections, consider complete if any field is filled
    const filledFields = section.fields.filter(field => {
      const value = formData[field]
      return value !== "" && value !== undefined && value !== null
    })

    return filledFields.length > 0
  }

  // Calculate overall form progress
  const getFormProgress = () => {
    const requiredSections = formSections.filter(s => s.required)
    const completedRequired = requiredSections.filter(getSectionCompletion).length
    const totalRequired = requiredSections.length
    
    const optionalSections = formSections.filter(s => !s.required)
    const completedOptional = optionalSections.filter(getSectionCompletion).length
    const totalOptional = optionalSections.length
    
    // Weight required sections more heavily
    const requiredWeight = 0.7
    const optionalWeight = 0.3
    
    const requiredProgress = totalRequired > 0 ? (completedRequired / totalRequired) * requiredWeight : 0
    const optionalProgress = totalOptional > 0 ? (completedOptional / totalOptional) * optionalWeight : 0
    
    return Math.round((requiredProgress + optionalProgress) * 100)
  }

  const handleInputChange = (field: keyof CustomerFormData, value: any) => {
    console.log(`🔄 [EVENT-HANDLER] handleInputChange called:`, {
      field,
      value,
      valueType: typeof value,
      timestamp: new Date().toISOString()
    })

    try {
      setFormData(prev => {
        console.log(`📝 [EVENT-HANDLER] Previous form data:`, prev)
        const newFormData = { ...prev, [field]: value }
        console.log(`✅ [EVENT-HANDLER] New form data:`, newFormData)
        console.log(`🔍 [EVENT-HANDLER] Field changed:`, { field, oldValue: prev[field], newValue: value })
        return newFormData
      })

      // Clear error when user starts typing
      if (errors[field]) {
        console.log(`🧹 [EVENT-HANDLER] Clearing error for field:`, field)
        setErrors(prev => ({ ...prev, [field]: undefined }))
      }

      console.log(`✅ [EVENT-HANDLER] handleInputChange completed successfully`)
    } catch (error) {
      console.error(`❌ [EVENT-HANDLER] Error in handleInputChange:`, error)
    }
  }

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId)
      } else {
        newSet.add(sectionId)
      }
      return newSet
    })
  }

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Required fields validation
    if (!formData.contact_person.trim()) {
      newErrors.contact_person = "Contact person is required"
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address"
    }
    if (!formData.company.trim()) {
      newErrors.company = "Company name is required"
    }
    if (!formData.city.trim()) {
      newErrors.city = "City is required"
    }
    if (!formData.country.trim()) {
      newErrors.country = "Country is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Check if form has minimum required fields for save button
  const isFormValid = useMemo((): boolean => {
    console.log(`🔍 [VALIDATION] isFormValid recalculating at:`, new Date().toISOString())
    console.log(`🔍 [VALIDATION] useMemo dependencies triggered:`, {
      contact_person: formData.contact_person,
      email: formData.email,
      company: formData.company,
      city: formData.city,
      country: formData.country
    })

    // Log individual field values
    console.log(`📋 Field values:`, {
      contact_person: formData.contact_person,
      email: formData.email,
      company: formData.company,
      city: formData.city,
      country: formData.country
    })

    // Check each validation condition individually
    const contactPersonValid = !!(formData.contact_person.trim())
    const emailNotEmpty = !!(formData.email.trim())
    const emailFormatValid = /\S+@\S+\.\S+/.test(formData.email)
    const companyValid = !!(formData.company.trim())
    const cityValid = !!(formData.city.trim())
    const countryValid = !!(formData.country.trim())

    console.log(`✅ Individual validations:`, {
      contactPersonValid,
      emailNotEmpty,
      emailFormatValid,
      companyValid,
      cityValid,
      countryValid
    })

    const finalResult = !!(
      contactPersonValid &&
      emailNotEmpty &&
      emailFormatValid &&
      companyValid &&
      cityValid &&
      countryValid
    )

    console.log(`🎯 Final validation result:`, finalResult)
    return finalResult
  }, [formData.contact_person, formData.email, formData.company, formData.city, formData.country])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      // Expand sections with errors
      const sectionsWithErrors = formSections.filter(section => 
        section.fields.some(field => errors[field])
      )
      setExpandedSections(prev => {
        const newSet = new Set(prev)
        sectionsWithErrors.forEach(section => newSet.add(section.id))
        return newSet
      })
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error("Form submission error:", error)
      setErrors({ submit: "Failed to save customer. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const progress = getFormProgress()

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="heading-3">🚀 ENHANCED CUSTOMER FORM V2 🚀</h3>
            <p className="body-small text-muted-foreground">
              Complete the required sections to create a customer record
            </p>
          </div>
          <Badge variant={progress >= 70 ? "success" : progress >= 40 ? "warning" : "secondary"}>
            {progress}% Complete
          </Badge>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Form Sections */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {formSections.map((section) => {
          const isExpanded = expandedSections.has(section.id)
          const isCompleted = getSectionCompletion(section)
          const hasErrors = section.fields.some(field => errors[field])
          
          return (
            <Card 
              key={section.id} 
              variant={hasErrors ? "outlined" : isCompleted ? "elevated" : "default"}
              className={cn(
                "transition-all duration-normal",
                hasErrors && "border-destructive",
                isCompleted && "border-success/20 bg-success/5"
              )}
            >
              <CardHeader 
                className="cursor-pointer hover:bg-accent/50 transition-colors"
                onClick={() => toggleSection(section.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "p-2 rounded-lg",
                      isCompleted ? "bg-success/10" : hasErrors ? "bg-destructive/10" : "bg-muted"
                    )}>
                      <section.icon className={cn(
                        "h-4 w-4",
                        isCompleted ? "text-success" : hasErrors ? "text-destructive" : "text-muted-foreground"
                      )} />
                    </div>
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {section.title}
                        {section.required && (
                          <Badge variant="outline" className="text-xs">Required</Badge>
                        )}
                      </CardTitle>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {isCompleted && (
                      <CheckCircle className="h-4 w-4 text-success" />
                    )}
                    {hasErrors && (
                      <AlertCircle className="h-4 w-4 text-destructive" />
                    )}
                    {isExpanded ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </div>
                </div>
              </CardHeader>
              
              {isExpanded && (
                <CardContent className="space-y-4 animate-fade-in">
                  {section.id === "contact" && (
                    <ContactSection 
                      formData={formData} 
                      errors={errors} 
                      onChange={handleInputChange} 
                    />
                  )}
                  {section.id === "company" && (
                    <CompanySection 
                      formData={formData} 
                      errors={errors} 
                      onChange={handleInputChange} 
                    />
                  )}
                  {section.id === "business" && (
                    <BusinessSection 
                      formData={formData} 
                      errors={errors} 
                      onChange={handleInputChange} 
                    />
                  )}
                  {section.id === "financial" && (
                    <FinancialSection 
                      formData={formData} 
                      errors={errors} 
                      onChange={handleInputChange} 
                    />
                  )}
                  {section.id === "shipping" && (
                    <ShippingSection 
                      formData={formData} 
                      errors={errors} 
                      onChange={handleInputChange} 
                    />
                  )}
                </CardContent>
              )}
            </Card>
          )
        })}

        {/* Submit Error */}
        {errors.submit && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{errors.submit}</AlertDescription>
          </Alert>
        )}

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-3 pt-6 border-t">
          {/* Debug button for testing */}
          <Button
            type="button"
            variant="secondary"
            onClick={testHandleInputChange}
            className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
          >
            🧪 Test Handler
          </Button>

          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={(() => {
              const buttonDisabled = isSubmitting || !isFormValid
              console.log(`🔘 Save Customer button render:`, {
                isSubmitting,
                isFormValid,
                buttonDisabled,
                timestamp: new Date().toISOString()
              })
              return buttonDisabled
            })()}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                Saving...
              </div>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Customer
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}

// Contact Information Section
interface SectionProps {
  formData: CustomerFormData
  errors: FormErrors
  onChange: (field: keyof CustomerFormData, value: any) => void
}

function ContactSection({ formData, errors, onChange }: SectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="contact_person" className="flex items-center gap-1">
          Contact Person
          <span className="text-destructive">*</span>
        </Label>
        <Input
          id="contact_person"
          value={formData.contact_person}
          onChange={(e) => onChange("contact_person", e.target.value)}
          placeholder="John Doe"
          className={errors.contact_person ? "border-destructive" : ""}
        />
        {errors.contact_person && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.contact_person}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="title_position">Title/Position</Label>
        <Input
          id="title_position"
          value={formData.title_position}
          onChange={(e) => onChange("title_position", e.target.value)}
          placeholder="Sales Manager"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="flex items-center gap-1">
          Email Address
          <span className="text-destructive">*</span>
        </Label>
        <div className="relative">
          <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => onChange("email", e.target.value)}
            placeholder="<EMAIL>"
            className={cn("pl-10", errors.email && "border-destructive")}
          />
        </div>
        {errors.email && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.email}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <div className="relative">
          <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => onChange("phone", e.target.value)}
            placeholder="+962 6 123 4567"
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="mobile">Mobile Number</Label>
        <div className="relative">
          <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="mobile"
            value={formData.mobile}
            onChange={(e) => onChange("mobile", e.target.value)}
            placeholder="+962 79 123 4567"
            className="pl-10"
          />
        </div>
      </div>
    </div>
  )
}

// Company Details Section
function CompanySection({ formData, errors, onChange }: SectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="company" className="flex items-center gap-1">
          Company Name
          <span className="text-destructive">*</span>
        </Label>
        <div className="relative">
          <Building2 className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="company"
            value={formData.company}
            onChange={(e) => onChange("company", e.target.value)}
            placeholder="Acme Corporation"
            className={cn("pl-10", errors.company && "border-destructive")}
          />
        </div>
        {errors.company && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.company}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="website">Website</Label>
        <div className="relative">
          <Globe className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="website"
            value={formData.website}
            onChange={(e) => onChange("website", e.target.value)}
            placeholder="https://company.com"
            className="pl-10"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="city" className="flex items-center gap-1">
          City
          <span className="text-destructive">*</span>
        </Label>
        <div className="relative">
          <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => onChange("city", e.target.value)}
            placeholder="Amman"
            className={cn("pl-10", errors.city && "border-destructive")}
          />
        </div>
        {errors.city && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.city}
          </p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="country" className="flex items-center gap-1">
          Country
          <span className="text-destructive">*</span>
        </Label>
        <Select value={formData.country} onValueChange={(value) => onChange("country", value)}>
          <SelectTrigger className={errors.country ? "border-destructive" : ""}>
            <SelectValue placeholder="Select country" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Jordan">Jordan</SelectItem>
            <SelectItem value="UAE">United Arab Emirates</SelectItem>
            <SelectItem value="Saudi Arabia">Saudi Arabia</SelectItem>
            <SelectItem value="Kuwait">Kuwait</SelectItem>
            <SelectItem value="Qatar">Qatar</SelectItem>
            <SelectItem value="Bahrain">Bahrain</SelectItem>
            <SelectItem value="Oman">Oman</SelectItem>
            <SelectItem value="Lebanon">Lebanon</SelectItem>
            <SelectItem value="Syria">Syria</SelectItem>
            <SelectItem value="Iraq">Iraq</SelectItem>
            <SelectItem value="Egypt">Egypt</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
        {errors.country && (
          <p className="text-sm text-destructive flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {errors.country}
          </p>
        )}
      </div>

      <div className="space-y-2 md:col-span-2">
        <Label htmlFor="industry">Industry</Label>
        <Input
          id="industry"
          value={formData.industry}
          onChange={(e) => onChange("industry", e.target.value)}
          placeholder="Technology, Manufacturing, Healthcare, etc."
        />
      </div>
    </div>
  )
}

// Business Information Section
function BusinessSection({ formData, errors, onChange }: SectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="business_type">Business Type</Label>
        <Select
          value={formData.business_type || ""}
          onValueChange={(value) => onChange("business_type", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select business type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Manufacturer">Manufacturer</SelectItem>
            <SelectItem value="Distributor">Distributor</SelectItem>
            <SelectItem value="Retailer">Retailer</SelectItem>
            <SelectItem value="Wholesaler">Wholesaler</SelectItem>
            <SelectItem value="Service Provider">Service Provider</SelectItem>
            <SelectItem value="Consultant">Consultant</SelectItem>
            <SelectItem value="Government">Government</SelectItem>
            <SelectItem value="NGO">NGO</SelectItem>
            <SelectItem value="Other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="company_size">Company Size</Label>
        <Select
          value={formData.company_size || ""}
          onValueChange={(value) => onChange("company_size", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select company size" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1-10">1-10 employees</SelectItem>
            <SelectItem value="11-50">11-50 employees</SelectItem>
            <SelectItem value="51-200">51-200 employees</SelectItem>
            <SelectItem value="201-500">201-500 employees</SelectItem>
            <SelectItem value="501-1000">501-1000 employees</SelectItem>
            <SelectItem value="1000+">1000+ employees</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="annual_volume">Annual Volume (USD)</Label>
        <Input
          id="annual_volume"
          type="number"
          value={formData.annual_volume || ""}
          onChange={(e) => onChange("annual_volume", e.target.value ? Number(e.target.value) : undefined)}
          placeholder="1000000"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="customer_tier">Customer Tier</Label>
        <Select
          value={formData.customer_tier}
          onValueChange={(value) => onChange("customer_tier", value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Bronze">Bronze</SelectItem>
            <SelectItem value="Silver">Silver</SelectItem>
            <SelectItem value="Gold">Gold</SelectItem>
            <SelectItem value="Platinum">Platinum</SelectItem>
            <SelectItem value="VIP">VIP</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

// Financial Details Section
function FinancialSection({ formData, errors, onChange }: SectionProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="space-y-2">
        <Label htmlFor="tax_id">Tax ID</Label>
        <Input
          id="tax_id"
          value={formData.tax_id}
          onChange={(e) => onChange("tax_id", e.target.value)}
          placeholder="*********"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="credit_limit">Credit Limit (USD)</Label>
        <Input
          id="credit_limit"
          type="number"
          value={formData.credit_limit || ""}
          onChange={(e) => onChange("credit_limit", e.target.value ? Number(e.target.value) : undefined)}
          placeholder="50000"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="payment_terms">Payment Terms</Label>
        <Select
          value={formData.payment_terms || ""}
          onValueChange={(value) => onChange("payment_terms", value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select payment terms" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Net 15">Net 15</SelectItem>
            <SelectItem value="Net 30">Net 30</SelectItem>
            <SelectItem value="Net 45">Net 45</SelectItem>
            <SelectItem value="Net 60">Net 60</SelectItem>
            <SelectItem value="Net 90">Net 90</SelectItem>
            <SelectItem value="COD">Cash on Delivery</SelectItem>
            <SelectItem value="Prepaid">Prepaid</SelectItem>
            <SelectItem value="Letter of Credit">Letter of Credit</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="currency_preference">Currency Preference</Label>
        <Select
          value={formData.currency_preference}
          onValueChange={(value) => onChange("currency_preference", value)}
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="USD">USD - US Dollar</SelectItem>
            <SelectItem value="EUR">EUR - Euro</SelectItem>
            <SelectItem value="JOD">JOD - Jordanian Dinar</SelectItem>
            <SelectItem value="AED">AED - UAE Dirham</SelectItem>
            <SelectItem value="SAR">SAR - Saudi Riyal</SelectItem>
            <SelectItem value="KWD">KWD - Kuwaiti Dinar</SelectItem>
            <SelectItem value="QAR">QAR - Qatari Riyal</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}

// Shipping Preferences Section
function ShippingSection({ formData, errors, onChange }: SectionProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="preferred_shipping_method">Preferred Shipping Method</Label>
          <Select
            value={formData.preferred_shipping_method || ""}
            onValueChange={(value) => onChange("preferred_shipping_method", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select shipping method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Air Freight">Air Freight</SelectItem>
              <SelectItem value="Sea Freight">Sea Freight</SelectItem>
              <SelectItem value="Land Transport">Land Transport</SelectItem>
              <SelectItem value="Express Courier">Express Courier</SelectItem>
              <SelectItem value="Standard Delivery">Standard Delivery</SelectItem>
              <SelectItem value="Customer Pickup">Customer Pickup</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="preferred_incoterms">Preferred Incoterms</Label>
          <Select
            value={formData.preferred_incoterms || ""}
            onValueChange={(value) => onChange("preferred_incoterms", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select incoterms" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="EXW">EXW - Ex Works</SelectItem>
              <SelectItem value="FCA">FCA - Free Carrier</SelectItem>
              <SelectItem value="CPT">CPT - Carriage Paid To</SelectItem>
              <SelectItem value="CIP">CIP - Carriage and Insurance Paid To</SelectItem>
              <SelectItem value="DAP">DAP - Delivered at Place</SelectItem>
              <SelectItem value="DPU">DPU - Delivered at Place Unloaded</SelectItem>
              <SelectItem value="DDP">DDP - Delivered Duty Paid</SelectItem>
              <SelectItem value="FAS">FAS - Free Alongside Ship</SelectItem>
              <SelectItem value="FOB">FOB - Free on Board</SelectItem>
              <SelectItem value="CFR">CFR - Cost and Freight</SelectItem>
              <SelectItem value="CIF">CIF - Cost, Insurance and Freight</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="shipping_instructions">Special Shipping Instructions</Label>
        <Textarea
          id="shipping_instructions"
          value={formData.shipping_instructions}
          onChange={(e) => onChange("shipping_instructions", e.target.value)}
          placeholder="Any special handling requirements, delivery instructions, or notes..."
          className="min-h-[80px]"
        />
      </div>
    </div>
  )
}
