'use client'

import React, { useState, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  ChartFilters, 
  ChartZoomControls, 
  ChartExportControls, 
  ChartSettings 
} from './ChartInteractions'
import { 
  RefreshCw, 
  Share2, 
  Bookmark, 
  Info,
  TrendingUp,
  AlertCircle
} from 'lucide-react'
import { formatters } from './chart-config'
import { useChartPerformance } from '../../hooks/useChartPerformance'

interface ChartToolbarProps {
  title: string
  subtitle?: string
  data: any[]
  chartRef?: React.RefObject<HTMLDivElement>
  onRefresh?: () => void
  onShare?: () => void
  onBookmark?: () => void
  showFilters?: boolean
  showZoom?: boolean
  showExport?: boolean
  showSettings?: boolean
  isLoading?: boolean
  error?: string | null
  lastUpdated?: Date
  totalRecords?: number
  filteredRecords?: number
  performanceMetrics?: any
}

export const ChartToolbar: React.FC<ChartToolbarProps> = ({
  title,
  subtitle,
  data,
  chartRef,
  onRefresh,
  onShare,
  onBookmark,
  showFilters = true,
  showZoom = true,
  showExport = true,
  showSettings = true,
  isLoading = false,
  error = null,
  lastUpdated,
  totalRecords,
  filteredRecords,
  performanceMetrics
}) => {
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [chartSettings, setChartSettings] = useState({
    showGrid: true,
    showLegend: true,
    showTooltips: true,
    animationsEnabled: true
  })

  const handleRefresh = useCallback(async () => {
    if (onRefresh) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } finally {
        setIsRefreshing(false)
      }
    }
  }, [onRefresh])

  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + 0.25, 3))
  }, [])

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - 0.25, 0.25))
  }, [])

  const handleZoomReset = useCallback(() => {
    setZoomLevel(1)
  }, [])

  const handleToggleFullscreen = useCallback(() => {
    setIsFullscreen(prev => !prev)
  }, [])

  const handleSettingChange = useCallback((setting: string, value: boolean) => {
    setChartSettings(prev => ({
      ...prev,
      [setting]: value
    }))
  }, [])

  const getStatusBadge = () => {
    if (error) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Error
        </Badge>
      )
    }

    if (isLoading || isRefreshing) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <RefreshCw className="h-3 w-3 animate-spin" />
          Loading
        </Badge>
      )
    }

    if (performanceMetrics?.status === 'critical') {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Slow
        </Badge>
      )
    }

    if (performanceMetrics?.status === 'warning') {
      return (
        <Badge variant="warning" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Slow
        </Badge>
      )
    }

    return (
      <Badge variant="success" className="flex items-center gap-1">
        <TrendingUp className="h-3 w-3" />
        Live
      </Badge>
    )
  }

  const getDataInfo = () => {
    if (totalRecords !== undefined) {
      const filtered = filteredRecords !== undefined ? filteredRecords : totalRecords
      return (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>{formatters.number(filtered)} records</span>
          {filteredRecords !== undefined && filteredRecords !== totalRecords && (
            <span>of {formatters.number(totalRecords)}</span>
          )}
        </div>
      )
    }

    if (data && data.length > 0) {
      return (
        <div className="text-sm text-muted-foreground">
          {formatters.number(data.length)} data points
        </div>
      )
    }

    return null
  }

  const getLastUpdatedInfo = () => {
    if (!lastUpdated) return null

    const now = new Date()
    const diffMs = now.getTime() - lastUpdated.getTime()
    const diffMinutes = Math.floor(diffMs / 60000)

    let timeAgo = ''
    if (diffMinutes < 1) {
      timeAgo = 'Just now'
    } else if (diffMinutes < 60) {
      timeAgo = `${diffMinutes}m ago`
    } else {
      const diffHours = Math.floor(diffMinutes / 60)
      timeAgo = `${diffHours}h ago`
    }

    return (
      <div className="text-xs text-muted-foreground">
        Updated {timeAgo}
      </div>
    )
  }

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 border-b">
      {/* Title and Info Section */}
      <div className="flex-1">
        <div className="flex items-center gap-3 mb-1">
          <h3 className="text-lg font-semibold">{title}</h3>
          {getStatusBadge()}
        </div>
        
        {subtitle && (
          <p className="text-sm text-muted-foreground mb-2">{subtitle}</p>
        )}
        
        <div className="flex items-center gap-4">
          {getDataInfo()}
          {getLastUpdatedInfo()}
          {performanceMetrics && (
            <div className="text-xs text-muted-foreground">
              Load: {performanceMetrics.totalTime}
            </div>
          )}
        </div>
      </div>

      {/* Controls Section */}
      <div className="flex items-center gap-2 flex-wrap">
        {/* Refresh Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading || isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>

        <Separator orientation="vertical" className="h-6" />

        {/* Chart Controls */}
        <div className="flex items-center gap-1">
          {showFilters && (
            <ChartFilters
              availableCategories={[]}
              onDateRangeChange={() => {}}
              onCategoryFilter={() => {}}
              onValueRangeChange={() => {}}
            />
          )}

          {showZoom && (
            <ChartZoomControls
              onZoomIn={handleZoomIn}
              onZoomOut={handleZoomOut}
              onReset={handleZoomReset}
              onToggleFullscreen={handleToggleFullscreen}
              isFullscreen={isFullscreen}
              zoomLevel={zoomLevel}
            />
          )}

          {showExport && data && (
            <ChartExportControls
              data={data}
              chartRef={chartRef}
              filename={title.toLowerCase().replace(/\s+/g, '-')}
            />
          )}

          {showSettings && (
            <ChartSettings
              showGrid={chartSettings.showGrid}
              showLegend={chartSettings.showLegend}
              showTooltips={chartSettings.showTooltips}
              animationsEnabled={chartSettings.animationsEnabled}
              onSettingChange={handleSettingChange}
            />
          )}
        </div>

        <Separator orientation="vertical" className="h-6" />

        {/* Action Buttons */}
        <div className="flex items-center gap-1">
          {onShare && (
            <Button variant="outline" size="sm" onClick={onShare}>
              <Share2 className="h-4 w-4" />
            </Button>
          )}

          {onBookmark && (
            <Button variant="outline" size="sm" onClick={onBookmark}>
              <Bookmark className="h-4 w-4" />
            </Button>
          )}

          <Button variant="outline" size="sm">
            <Info className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}

// Enhanced chart container with toolbar
interface EnhancedChartContainerProps {
  children: React.ReactNode
  toolbarProps: Omit<ChartToolbarProps, 'chartRef'>
  className?: string
}

export const EnhancedChartContainer: React.FC<EnhancedChartContainerProps> = ({
  children,
  toolbarProps,
  className = ""
}) => {
  const chartRef = React.useRef<HTMLDivElement>(null)

  return (
    <div className={`border rounded-lg bg-card ${className}`}>
      <ChartToolbar {...toolbarProps} chartRef={chartRef} />
      <div ref={chartRef} className="p-4">
        {children}
      </div>
    </div>
  )
}
