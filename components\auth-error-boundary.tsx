"use client"

import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, LogIn } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface AuthErrorBoundaryProps {
  error: string | null
  loading: boolean
  onRetry: () => void
  onGoToLogin: () => void
  retryCount?: number
  maxRetries?: number
}

export function AuthErrorBoundary({ 
  error, 
  loading, 
  onRetry, 
  onGoToLogin,
  retryCount = 0,
  maxRetries = 5
}: AuthErrorBoundaryProps) {
  if (!error && !loading) return null

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
            </div>
            <CardTitle>Authenticating...</CardTitle>
            <CardDescription>
              Connecting to authentication service...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-12 w-12 text-amber-500" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900">
            Authentication Error
          </CardTitle>
          <CardDescription className="text-gray-600">
            {error}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {retryCount < maxRetries && (
            <Alert>
              <AlertDescription>
                Retry attempt {retryCount} of {maxRetries}. 
                The system will automatically retry the connection.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex flex-col gap-3">
            {retryCount < maxRetries ? (
              <Button 
                onClick={onRetry} 
                className="w-full"
                variant="default"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Retry Connection
              </Button>
            ) : (
              <Alert variant="destructive">
                <AlertDescription>
                  Maximum retry attempts reached. Please check your internet connection.
                </AlertDescription>
              </Alert>
            )}
            
            <Button 
              onClick={onGoToLogin} 
              variant="outline" 
              className="w-full"
            >
              <LogIn className="mr-2 h-4 w-4" />
              Go to Login
            </Button>
          </div>

          <div className="text-center text-sm text-gray-500">
            <p>If the problem persists, please:</p>
            <ul className="mt-2 text-left space-y-1">
              <li>• Check your internet connection</li>
              <li>• Clear your browser cache</li>
              <li>• Try refreshing the page</li>
              <li>• Contact support if issues continue</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
