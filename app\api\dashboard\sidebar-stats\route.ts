import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { cache, CacheKeys, CacheTTL } from '@/lib/cache'

export async function GET(request: NextRequest) {
  try {
    // Use the same authentication pattern as working modules
    const supabase = createClient()

    // Get the current user from Supabase
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized - No valid session' },
        { status: 401 }
      )
    }

    const userId = user.id
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized - No user ID in session' },
        { status: 401 }
      )
    }

    console.log('🔍 [SIDEBAR-STATS-API] Fetching sidebar stats for user:', userId)

    // Get user profile to check role
    const { data: userProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single()

    const userRole = userProfile?.role || 'user'
    const isAdmin = userRole === 'admin'

    console.log(`🔍 [SIDEBAR-STATS-API] User role: ${userRole}, isAdmin: ${isAdmin}`)

    // Check cache first
    const cacheKey = CacheKeys.sidebarStats(userId, isAdmin)
    const cachedStats = cache.get(cacheKey)

    if (cachedStats) {
      console.log('⚡ [SIDEBAR-STATS-API] Returning cached stats')
      return NextResponse.json(cachedStats)
    }

    // For admin users, show global stats; for regular users, show their own stats
    const [customersRes, leadsRes, opportunitiesRes] = await Promise.all([
      isAdmin
        ? supabase.from('customers').select('id', { count: 'exact', head: true })
        : supabase.from('customers').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabase.from('leads').select('id', { count: 'exact', head: true })
        : supabase.from('leads').select('id', { count: 'exact', head: true }).eq('user_id', userId),
      isAdmin
        ? supabase.from('deals').select('id', { count: 'exact', head: true })
        : supabase.from('deals').select('id', { count: 'exact', head: true }).eq('user_id', userId)
    ])

    // Check for errors
    if (customersRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Customers query error:', customersRes.error)
      throw customersRes.error
    }
    if (leadsRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Leads query error:', leadsRes.error)
      throw leadsRes.error
    }
    if (opportunitiesRes.error) {
      console.error('❌ [SIDEBAR-STATS-API] Opportunities query error:', opportunitiesRes.error)
      throw opportunitiesRes.error
    }

    const stats = [{
      customers_count: customersRes.count || 0,
      leads_count: leadsRes.count || 0,
      opportunities_count: opportunitiesRes.count || 0
    }]

    console.log('✅ [SIDEBAR-STATS-API] Sidebar stats fetched successfully:', stats[0])

    // Cache the results for future requests
    cache.set(cacheKey, stats, CacheTTL.QUICK_STATS)

    return NextResponse.json(stats)

  } catch (error: any) {
    console.error('❌ [SIDEBAR-STATS-API] Error fetching sidebar stats:', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch sidebar stats',
        details: error.message 
      },
      { status: 500 }
    )
  }
}
