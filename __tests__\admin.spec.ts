import { test, expect } from '@playwright/test';

test.describe('Admin Access Control Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Set longer timeout for navigation
    page.setDefaultTimeout(30000);
  });

  test('should allow admin user to access admin dashboard', async ({ page }) => {
    console.log('🔍 Testing admin access to admin dashboard...');
    
    // Navigate to login page
    await page.goto('/login');
    
    // Login with admin credentials
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    
    // Wait for authentication to complete
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    console.log('✅ Admin user authenticated successfully');
    
    // Navigate to admin dashboard
    await page.goto('/dashboard/admin');
    await page.waitForLoadState('networkidle');
    
    // Verify admin dashboard is accessible
    await expect(page.locator('h1')).toContainText('Admin Dashboard');
    await expect(page.locator('text=Administrator')).toBeVisible();
    await expect(page.locator('text=System Health')).toBeVisible();
    await expect(page.locator('text=Quick Actions')).toBeVisible();
    
    console.log('✅ Admin dashboard accessible to admin user');
  });

  test('should allow admin user to access user management', async ({ page }) => {
    console.log('🔍 Testing admin access to user management...');
    
    // Login as admin (reuse login flow)
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Navigate to user management
    await page.goto('/dashboard/admin/users');
    await page.waitForLoadState('networkidle');
    
    // Verify user management is accessible
    await expect(page.locator('h1')).toContainText('User Management');
    await expect(page.locator('text=Add User')).toBeVisible();
    await expect(page.locator('text=Total Users')).toBeVisible();
    await expect(page.locator('text=Administrators')).toBeVisible();
    
    console.log('✅ User management accessible to admin user');
  });

  test('should show admin navigation items for admin users', async ({ page }) => {
    console.log('🔍 Testing admin navigation visibility...');
    
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Check that admin navigation items are visible
    await expect(page.locator('text=Administration')).toBeVisible();
    await expect(page.locator('text=User Management')).toBeVisible();
    await expect(page.locator('text=Administrator')).toBeVisible(); // Role badge
    
    // Check that coming soon items show "Soon" badge for admin
    await expect(page.locator('text=Vista Status')).toBeVisible();
    await expect(page.locator('text=Soon')).toBeVisible();
    
    console.log('✅ Admin navigation items visible to admin user');
  });

  test('should verify admin role is properly recognized', async ({ page }) => {
    console.log('🔍 Testing admin role recognition...');
    
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Verify admin role is displayed in multiple places
    await expect(page.locator('text=Administrator')).toBeVisible();
    await expect(page.locator('text=<EMAIL>')).toBeVisible();
    await expect(page.locator('text=✅ Authenticated')).toBeVisible();
    
    // Navigate to admin dashboard and verify admin-specific content
    await page.goto('/dashboard/admin');
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('text=Welcome back, <EMAIL>')).toBeVisible();
    await expect(page.locator('text=Administrator')).toBeVisible();
    
    console.log('✅ Admin role properly recognized and displayed');
  });

  test('should test admin system reports access', async ({ page }) => {
    console.log('🔍 Testing admin access to system reports...');
    
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Navigate to system reports
    await page.goto('/dashboard/admin/reports');
    await page.waitForLoadState('networkidle');
    
    // Verify system reports page loads (even if it shows coming soon or basic content)
    // The important thing is that it doesn't show access denied
    const pageContent = await page.textContent('body');
    expect(pageContent).not.toContain('Access Denied');
    expect(pageContent).not.toContain('Authentication Required');
    
    console.log('✅ System reports accessible to admin user');
  });

  // Note: Testing non-admin access would require creating a non-admin user
  // For now, we'll focus on verifying admin access works correctly
  
  test('should verify admin quick actions work', async ({ page }) => {
    console.log('🔍 Testing admin quick actions...');
    
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Navigate to admin dashboard
    await page.goto('/dashboard/admin');
    await page.waitForLoadState('networkidle');
    
    // Test User Management quick action
    await page.click('text=User Management');
    await page.waitForURL(/\/dashboard\/admin\/users/, { timeout: 10000 });
    await expect(page.locator('h1')).toContainText('User Management');
    
    // Go back to admin dashboard
    await page.goto('/dashboard/admin');
    await page.waitForLoadState('networkidle');
    
    // Test System Reports quick action
    await page.click('text=System Reports');
    await page.waitForURL(/\/dashboard\/admin\/reports/, { timeout: 10000 });
    
    // Verify we're on the reports page (not access denied)
    const pageContent = await page.textContent('body');
    expect(pageContent).not.toContain('Access Denied');
    
    console.log('✅ Admin quick actions working correctly');
  });

  test('should verify admin dashboard metrics display', async ({ page }) => {
    console.log('🔍 Testing admin dashboard metrics...');
    
    // Login as admin
    await page.goto('/login');
    await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
    await page.fill('input[type="password"], input[name="password"]', '111333Tt');
    await page.click('button[type="submit"], button:has-text("Sign In")');
    await page.waitForURL(/\/dashboard/, { timeout: 15000 });
    
    // Navigate to admin dashboard
    await page.goto('/dashboard/admin');
    await page.waitForLoadState('networkidle');
    
    // Verify system metrics are displayed
    await expect(page.locator('text=Total Users')).toBeVisible();
    await expect(page.locator('text=Total Customers')).toBeVisible();
    await expect(page.locator('text=Total Revenue')).toBeVisible();
    await expect(page.locator('text=System Health')).toBeVisible();
    await expect(page.locator('text=All Systems Operational')).toBeVisible();
    
    // Verify recent activity section
    await expect(page.locator('text=Recent System Activity')).toBeVisible();
    
    console.log('✅ Admin dashboard metrics displaying correctly');
  });
});
